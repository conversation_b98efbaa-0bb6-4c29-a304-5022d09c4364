
from decouple import config
import redis 
import requests
from datetime import timedelta

from wyse_ussd.models import RedoceanAfricaRequestLogs

class RedoceanAfricaGatewayHelper:

    def __init__(self):
        self.base_url = config("REDOCEAN_AFRICA_BASE_BASE_URL")
    

    def get_token(self) -> str:
        """
        Retrieves the token for the Redocean Africa payment gateway.
        """
        redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
        redocean_africa_token = redis_db.get("redocean_africa_token")
        if redocean_africa_token is None:
            email = config("REDOCEAN_AFRICA_USEREMAIL")
            password = config("REDOCEAN_AFRICA_PASSWORD")

            url = f"{self.base_url}/partners/login"
            
            log_instance = RedoceanAfricaRequestLogs.create_record(
                phone_number = "010",
                request_payload = "Login",
                type_of_request = "LOGIN",
                url = url
            )

            

            payload = {
                "email": email,
                "password": password,
            }

            headers = {
                "Content-Type": "application/json",
            }

            try:
                response = requests.post(url, json=payload, headers=headers)
                log_instance.response_payload = response.text
                log_instance.save()
                res = response.json()
                token = res.get("data", {}).get("token")
                if not token:
                    return ""
                redis_db.set("redocean_africa_token", token, ex=timedelta(days=3))

                return res

            except Exception:
                return ""

        else:
            return redocean_africa_token

    def subscribe_a_phone_number(self, phone_number, short_code = "40001", country = "KE") -> dict:
        url = f"{self.base_url}/partners/requests/subscribe"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.get_token()}",
        }

        payload = {
            "phone_number": phone_number,
            "short_code": short_code,
            "country": country,
        }

        log_instance = RedoceanAfricaRequestLogs.create_record(
            phone_number = phone_number,
            request_payload = payload,
            type_of_request = "SUBSCRIPTION_REQUEST",
            url=url
        )


        response = requests.post(url, json=payload, headers=headers)

        try:
            res = response.json()
            log_instance.response_payload = res
            log_instance.save()

            return res

        except Exception as e:
            log_instance.response_payload = str(response.text)
            log_instance.save()
            return {}
        
    
    def check_the_subscription_status_of_a_phone_number(self, phone_number, short_code = "40001", country = "KE") -> dict:
        url = f"{self.base_url}/partners/requests/checkSubscriptionStatus"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.get_token()}",
        }

        payload = {
            "phone_number": phone_number,
            "short_code": short_code,
            "country": country,
        }

        log_instance = RedoceanAfricaRequestLogs.create_record(
            phone_number = phone_number,
            request_payload = payload,
            type_of_request = "SUBSCRIPTION_INQUIRY",
            url = url
        )

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            log_instance.response_payload = res
            log_instance.save()

            return res

        except Exception as e:
            log_instance.response_payload = str(e)
            log_instance.save()
            return {}
        
    
    def send_content(self, phone_number, content, short_code = "40001", country = "KE") -> dict:
        url = f"{self.base_url}/partners/requests/sendContent"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.get_token()}",
        }

        payload = {
            "phone_number": phone_number,
            "content": content,
            "short_code": short_code,
            "country": country,
        }

        log_instance = RedoceanAfricaRequestLogs.create_record(
            phone_number = phone_number,
            request_payload = payload,
            type_of_request = "SEND_CONTENT_REQUEST",
            url = url
        )

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            log_instance.response_payload = res
            log_instance.save()

            return res

        except Exception as e:
            log_instance.response_payload = str(e)
            log_instance.save()
            return {}
    

    def get_content_status(self, requestId) -> dict:
        url = f"{self.base_url}/partners/requests/checkContentStatus/{requestId}"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.get_token()}",
        }

        try:
            response = requests.get(url, headers=headers)
            res = response.json()
            return res

        except Exception as e:
            return {}