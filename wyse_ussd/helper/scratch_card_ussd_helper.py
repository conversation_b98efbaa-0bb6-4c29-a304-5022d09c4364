import requests
from django.urls import reverse


class UssdScratchCardHelper:
    def __init__(self) -> None:
        self.base_url = "http://127.0.0.1:80"

        self.headers = {"content-type": "application/json"}

    def verify_pin_and_serial_number(self, serial_number, pin):
        url = f'{self.base_url}{reverse("scratch_cards:get-scratch-card-amount")}'
        payload = {"serial_number": serial_number, "pin": pin}

        response = requests.request("POST", url, json=payload, headers=self.headers)

        try:
            res = response.json()
        except Exception:
            return False, "An error occurred"

        if response.status_code != 200:
            return False, str(res.get("message")).replace("[", "").replace("]", "").strip()

        return True, res

    def claim_winning(self, serial_number, pin, account_number, bank_code, phone_number, use_new_withdrawal_flow=False):
        payload = {
            "serial_number": serial_number,
            "pin": pin,
            "account_number": account_number,
            "bank_code": bank_code,
            "phone_number": phone_number,
        }

        print(
            f"""
            payload: {payload},
            \n\n\n\n
        """
        )

        if use_new_withdrawal_flow is False:
            url = f'{self.base_url}{reverse("scratch_cards:scratch-card-withdrawal")}'
        else:
            url = f'{self.base_url}{reverse("scratch_cards:new_scratch-card-withdrawal")}'

        response = requests.request("POST", url, json=payload, headers=self.headers)

        print("SCRATCH CARD VERIFICATION CLAIM", response.text, "\n\n\n\n")

        try:
            response.json()
        except Exception:
            return response.text
