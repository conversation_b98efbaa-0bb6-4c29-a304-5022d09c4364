from django.db.models import TextChoices
from django.utils.translation import gettext_lazy as _


class PurposeChoices(TextChoices):
    MORBID_RENEWALS = _("MORBID_RENEWALS"), "MORBID_RENEWALS"
    GAME_SUBSCRIPTION_NOTIFICATION = _("GAME_SUBSCRIPTION_NOTIFICATION"), "GAME_SUBSCRIPTION_NOTIFICATION"
    GAME_WINNING_NOTIFICATION = _("GAME_WINNING_NOTIFICATION"), "GAME_WINNING_NOTIFICATION"
    SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION = _("SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION"), "SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION"
    NITROSWITCH_RENEWALS = _("NITROSWITCH_RENEWALS"), "NITROSWITCH_RENEWALS"
