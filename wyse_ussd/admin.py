from django.contrib import admin
from django.db.utils import IntegrityError
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from ads_tracker.helpers.helpers import (
    get_lotto_subscribers_phone_list,
    send_ads_tracker_postback_url,
)
from ads_tracker.models import MobidTracker
from main.dashboard_filters import NitroswitchActionFilter, NitroswitchSericeType
from wyse_ussd.models import (
    CoralpayTransactions,
    CoralpayUserCode,
    ExtraTelcoOptions,
    GameShowParticipant,
    MonetizeAiSubscription,
    NitroswitchAiFortuneSubscription,
    NitroswitchContentDeliveryLogs,
    NitroSwitchDailyGameBreakdown,
    NitroSwitchDailySubscription,
    NitroSwitchData,
    NitroSwitchDataSync,
    NitroSwitchSMSMO,
    PendingAsyncTask,
    RedoceanAfricaContentDeliveryDataSync,
    RedoceanAfricaDailySubscription,
    RedoceanAfricaRequestLogs,
    RedoceanAfricaSubscriber,
    RedoceanAfricaSubscriptionDataSync,
    SecureDDataDump,
    SecureDTransaction,
    SoccerPrediction,
    SoccerPredictionRequestLogs,
    SubscriptionViaSmsDataDump,
    SuccessfullyChargeSoccerPredictionRequestLogs,
    TelcoAggregatorNotification,
    TelcoAirtimeReward,
    TelcoAvailableNetwork,
    TelcoCharge,
    TelcoConstant,
    TelcoDailyAnalytics,
    TelcoDailySubscription,
    TelcoDailySubscriptionAnalytics,
    TelcoDataSync,
    TelcoDataSyncJsons,
    TelcoGameWebSubscribers,
    TelcoLibertyLifeSubscription,
    TelcoSubscriptionPlan,
    TelcoSubscriptionRequest,
    TelcoUnsubscriptionRequest,
    TelcoUsers,
    TelcoUssdSessionIds,
    TempTelcoDataSync,
    UserTelcoSubscription,
    UserTelcoSubscriptionTransaction,
    UssdConstantVariable,
    UssdLotteryPayment,
    UssdPayment,
    PendingNitroswitchAsyncTask,
)

# Register your models here.


# RESOURCES
class CoralpayTransactionsResource(resources.ModelResource):
    class Meta:
        model = CoralpayTransactions


class CoralpayUserCodeResource(resources.ModelResource):
    class Meta:
        model = CoralpayUserCode


class UssdLotteryPaymentResource(resources.ModelResource):
    class Meta:
        model = UssdLotteryPayment


class SoccerPredictionResource(resources.ModelResource):
    class Meta:
        model = SoccerPrediction


class UssdPaymentResource(resources.ModelResource):
    class Meta:
        model = UssdPayment


class UssdConstantVariableResource(resources.ModelResource):
    class Meta:
        model = UssdConstantVariable


class ExtraTelcoOptionsResource(resources.ModelResource):
    class Meta:
        model = ExtraTelcoOptions


class TelcoSubscriptionPlanResource(resources.ModelResource):
    class Meta:
        model = TelcoSubscriptionPlan


class UserTelcoSubscriptionResource(resources.ModelResource):
    class Meta:
        model = UserTelcoSubscription


class UserTelcoSubscriptionTransactionResource(resources.ModelResource):
    class Meta:
        model = UserTelcoSubscriptionTransaction


class TelcoChargeResource(resources.ModelResource):
    class Meta:
        model = TelcoCharge


class TelcoDataSyncResource(resources.ModelResource):
    class Meta:
        model = TelcoDataSync


class TelcoDataSyncJsonsResource(resources.ModelResource):
    class Meta:
        model = TelcoDataSyncJsons


class TelcoConstantResource(resources.ModelResource):
    class Meta:
        model = TelcoConstant


class TelcoUsersResource(resources.ModelResource):
    class Meta:
        model = TelcoUsers


class TelcoUssdSessionIdsResource(resources.ModelResource):
    class Meta:
        model = TelcoUssdSessionIds


class SecureDDataDumpResource(resources.ModelResource):
    class Meta:
        model = SecureDDataDump


class TelcoSubscriptionRequestResource(resources.ModelResource):
    class Meta:
        model = TelcoSubscriptionRequest


class TelcoAggregatorNotificationResource(resources.ModelResource):
    class Meta:
        model = TelcoAggregatorNotification


class SecureDTransactionResource(resources.ModelResource):
    class Meta:
        model = SecureDTransaction


class SubscriptionViaSmsDataDumpResource(resources.ModelResource):
    class Meta:
        model = SubscriptionViaSmsDataDump


class TelcoGameWebSubscribersResource(resources.ModelResource):
    class Meta:
        model = TelcoGameWebSubscribers


class TelcoDailySubscriptionAnalyticsResource(resources.ModelResource):
    class Meta:
        model = TelcoDailySubscriptionAnalytics


class TelcoAirtimeRewardResource(resources.ModelResource):
    class Meta:
        model = TelcoAirtimeReward


class TelcoAvailableNetworkResource(resources.ModelResource):
    class Meta:
        model = TelcoAvailableNetwork


class TelcoDailyAnalyticsResource(resources.ModelResource):
    class Meta:
        model = TelcoDailyAnalytics


class TelcoDailySubscriptionResource(resources.ModelResource):
    class Meta:
        model = TelcoDailySubscription


class NitroSwitchDataSyncResource(resources.ModelResource):
    class Meta:
        model = NitroSwitchDataSync


class NitroSwitchDataResource(resources.ModelResource):
    class Meta:
        model = NitroSwitchData


class NitroSwitchDailySubscriptionResource(resources.ModelResource):
    class Meta:
        model = NitroSwitchDailySubscription


class NitroSwitchDailyGameBreakdownResource(resources.ModelResource):
    class Meta:
        model = NitroSwitchDailyGameBreakdown


class NitroswitchContentDeliveryLogsResource(resources.ModelResource):
    class Meta:
        model = NitroswitchContentDeliveryLogs


class NitroSwitchSMSMOResource(resources.ModelResource):
    class Meta:
        model = NitroSwitchSMSMO


class NitroswitchAiFortuneSubscriptionResource(resources.ModelResource):
    class Meta:
        model = NitroswitchAiFortuneSubscription


class GameShowParticipantResource(resources.ModelResource):
    class Meta:
        model = GameShowParticipant


class PendingAsyncTaskResource(resources.ModelResource):
    class Meta:
        model = PendingAsyncTask


class TelcoUnsubscriptionRequestResource(resources.ModelResource):
    class Meta:
        model = TelcoUnsubscriptionRequest


class MonetizeAiSubscriptionResource(resources.ModelResource):
    class Meta:
        model = MonetizeAiSubscription


class TelcoLibertyLifeSubscriptionResource(resources.ModelResource):
    class Meta:
        model = TelcoLibertyLifeSubscription


class SoccerPredictionRequestLogsResource(resources.ModelResource):
    class Meta:
        model = SoccerPredictionRequestLogs


class SuccessfullyChargeSoccerPredictionRequestLogsResource(resources.ModelResource):
    class Meta:
        model = SuccessfullyChargeSoccerPredictionRequestLogs


class TempTelcoDataSyncResource(resources.ModelResource):
    class Meta:
        model = TempTelcoDataSync

class PendingNitroswitchAsyncTaskResource(resources.ModelResource):
    class Meta:
        model = PendingNitroswitchAsyncTask


class RedoceanAfricaSubscriptionDataSyncResource(resources.ModelResource):
    class Meta:
        model = RedoceanAfricaSubscriptionDataSync


class RedoceanAfricaContentDeliveryDataSyncResource(resources.ModelResource):
    class Meta:
        model = RedoceanAfricaContentDeliveryDataSync



class RedoceanAfricaSubscriberResource(resources.ModelResource):
    class Meta:
        model = RedoceanAfricaSubscriber


class RedoceanAfricaDailySubscriptionResource(resources.ModelResource):
    class Meta:
        model = RedoceanAfricaDailySubscription


class RedoceanAfricaRequestLogsResource(resources.ModelResource):
    class Meta:
        model = RedoceanAfricaRequestLogs


#
# ADMINS


class CoralpayTransactionsResourceAdmin(ImportExportModelAdmin):
    resource_class = CoralpayTransactionsResource
    # autocomplete_fields = ['batch', 'user_profile']
    search_fields = ["id", "coralpay_customer_ref", "user_transaction_id"]
    list_filter = ("channel",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CoralpayUserCodeResourceAdmin(ImportExportModelAdmin):
    resource_class = CoralpayUserCodeResource
    autocomplete_fields = ["user"]
    search_fields = ["id", "user__phone_number", "code"]
    list_filter = ("is_used", "has_expired")
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UssdLotteryPaymentResourceAdmin(ImportExportModelAdmin):
    resource_class = UssdLotteryPaymentResource
    # autocomplete_fields = ['batch', 'user_profile']
    search_fields = ["id", "user__phone_number", "game_play_id"]
    list_filter = ("is_successful", "is_verified", "payment_initiated")
    date_hierarchy = "created_at"

    raw_id_fields = ("user",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SoccerPredictionResourceAdmin(ImportExportModelAdmin):
    resource_class = SoccerPredictionResource
    # autocomplete_fields = ['batch', 'user_profile']
    search_fields = ["id", "game_id", "game_fixture_id", "phone"]
    list_filter = ["channel", "date"]
    date_hierarchy = "date"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UssdPaymentResourceAdmin(ImportExportModelAdmin):
    resource_class = UssdPaymentResource
    # autocomplete_fields = ['batch', 'user_profile']
    search_fields = [
        "id",
        "user__phone_number",
    ]
    list_filter = ("successful", "verified", "payment_for", "source", "channel")
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UssdConstantVariableResourceAdmin(ImportExportModelAdmin):
    resource_class = UssdConstantVariableResource
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ExtraTelcoOptionsResourceAdmin(ImportExportModelAdmin):
    resource_class = ExtraTelcoOptionsResource
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoSubscriptionPlanResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoSubscriptionPlanResource

    search_fields = [
        "game_type",
        "phone_number",
        "subscription_type",
        "subscription_status",
        "service_id",
        "product_id",
    ]

    list_filter = (
        "verification_method",
        "transaction_verified",
        "renewed",
        "created_at",
        "network_provider",
        "subscription_type",
        "game_type",
        "subscription_status",
    )

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserTelcoSubscriptionResourceAdmin(ImportExportModelAdmin):
    resource_class = UserTelcoSubscriptionResource
    date_hierarchy = "created_at"

    search_fields = ["user__phone_number", "user__email"]

    list_filter = ("is_active",)

    raw_id_fields = ("user",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserTelcoSubscriptionTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = UserTelcoSubscriptionTransactionResource
    date_hierarchy = "created_at"

    search_fields = ["user__phone_number", "user__email", "transaction_ref"]

    list_filter = ("transaction_status",)

    raw_id_fields = ("user",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoChargeResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoChargeResource
    date_hierarchy = "created_at"

    search_fields = ["phone_number", "reference"]

    list_filter = ("created_at", "status", "channel")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoDataSyncResourceAdmin(ImportExportModelAdmin):
    using = "external2"

    resource_class = TelcoDataSyncResource
    date_hierarchy = "created_at"

    # search_fields = ["phone_number", "reference"]

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_queryset(self, request):
        # Tell Django to look for objects on the 'other' database.
        return super(TelcoDataSyncResourceAdmin, self).get_queryset(request).using(self.using)


class TelcoDataSyncJsonsResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoDataSyncJsonsResource
    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_queryset(self, request):
        # Tell Django to look for objects on the 'other' database.
        return super(TelcoDataSyncJsonsResourceAdmin, self).get_queryset(request).using(TelcoDataSyncJsons.using)


class TelcoConstantResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoConstantResource
    date_hierarchy = "created_at"

    # search_fields = ["phone_number", "reference"]

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoUsersResourceAdmin(ImportExportModelAdmin):
    using = "external2"

    resource_class = TelcoUsersResource
    date_hierarchy = "created_at"

    search_fields = [
        "phone_number",
    ]

    list_filter = ("created_at", "paid", "won", "winning_withdrawn", "network")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_queryset(self, request):
        # Tell Django to look for objects on the 'other' database.
        return super(TelcoUsersResourceAdmin, self).get_queryset(request).using(self.using)


class TelcoUssdSessionIdsResourceAdmin(ImportExportModelAdmin):
    using = "external"

    resource_class = TelcoUssdSessionIdsResource
    date_hierarchy = "created_at"

    # search_fields = [
    #     "phone_number",
    # ]

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_queryset(self, request):
        # Tell Django to look for objects on the 'other' database.
        return super(TelcoUssdSessionIdsResourceAdmin, self).get_queryset(request).using(self.using)


class SecureDDataDumpResourceAdmin(ImportExportModelAdmin):
    resource_class = SecureDDataDumpResource
    date_hierarchy = "created_at"

    # search_fields = ["phone_number", "reference"]

    list_filter = ("created_at", "source")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoSubscriptionRequestResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoSubscriptionRequestResource
    date_hierarchy = "created_at"

    search_fields = [
        "phone_number",
    ]

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SecureDTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = SecureDTransactionResource
    date_hierarchy = "created_at"

    search_fields = [
        "phone_number",
        "reference",
    ]

    list_filter = ("created_at", "is_successful", "activation")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def manual_update_phoenix_morbidtracker(self, request, queryset):
        for query in queryset:
            phone_number = query.phone_number
            all_subscribers_phones_list = get_lotto_subscribers_phone_list(phone_number=phone_number)
            if MobidTracker.objects.filter(phone_number=phone_number):
                pass
            elif phone_number in all_subscribers_phones_list:
                pass
            else:
                try:
                    tracker_record, created = MobidTracker.objects.get_or_create(
                        click_id=query.reference,
                        phone_number=query.phone_number,
                        converted=True,
                        amount_played=query.subscription_amount,
                        game_type=query.game_type,
                        source="Phoenix_MTN",
                    )
                except (IntegrityError, UnboundLocalError):
                    pass

                send_ads_tracker_postback_url(source="Phoenix_MTN", click_id=query.reference, amount=query.subscription_amount)
        self.message_user(request, "Resolved successfully")

    def manual_update_morbidtek_media_morbidtracker(self, request, queryset):
        import json

        from django.db.utils import IntegrityError

        # all_subscribers_phones_list = get_lotto_subscribers_phone_list()
        for query in queryset:
            if query.reference.startswith("aazz") and len(query.reference) == 55:
                phone_number = query.phone_number
                morbid_tracker_instance = MobidTracker.objects.filter(phone_number=phone_number).last()

                if morbid_tracker_instance and morbid_tracker_instance.postback_sent:
                    continue
                else:
                    try:
                        send_morbidtek_postback = send_ads_tracker_postback_url(
                            source="MORBIDTEK_MEDIA", click_id=query.reference, amount=query.subscription_amount
                        )
                        postback_response = send_morbidtek_postback

                        if send_morbidtek_postback == "Failed":
                            continue

                        send_morbidtek_postback = json.loads(send_morbidtek_postback)

                        tracker_record, created = MobidTracker.objects.get_or_create(
                            click_id=query.reference,
                            phone_number=query.phone_number,
                            postback_sent=True,
                            amount_played=query.subscription_amount,
                            game_type=query.game_type,
                            source="MORBIDTEK_MEDIA",
                            postback_response=postback_response,
                        )

                        # if (
                        #     send_morbidtek_postback.get("error") == 0 and
                        #     send_morbidtek_postback.get("info") == "Conversion Received."
                        #     ):
                        # tracker_record.postback_sent = True
                        # tracker_record.save()
                    except (IntegrityError, UnboundLocalError):
                        pass
            else:
                continue
        self.message_user(request, "Resolved successfully")

    manual_update_phoenix_morbidtracker.short_description = "Update on MorbidTracker"
    manual_update_morbidtek_media_morbidtracker.short_description = "Update MorbidtekMedia Records"

    actions = [manual_update_phoenix_morbidtracker, manual_update_morbidtek_media_morbidtracker]


class TelcoAggregatorNotificationResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoAggregatorNotificationResource
    date_hierarchy = "created_at"

    # search_fields = [
    #     "phone_number",
    # ]

    list_filter = ("created_at", "aggregator")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SubscriptionViaSmsDataDumpResourceAdmin(ImportExportModelAdmin):
    resource_class = SubscriptionViaSmsDataDumpResource
    date_hierarchy = "created_at"

    search_fields = [
        "phone_number",
    ]

    list_filter = ("created_at", "is_successful", "game_type")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoGameWebSubscribersResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoGameWebSubscribersResource
    date_hierarchy = "created_at"

    search_fields = [
        "phone_number",
    ]

    list_filter = ("created_at", "is_successful", "game_type")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoDailySubscriptionAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoDailySubscriptionAnalyticsResource
    date_hierarchy = "created_at"

    search_fields = [
        "phone_number",
    ]

    list_filter = ("created_at", "month")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoAirtimeRewardResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoAirtimeRewardResource
    date_hierarchy = "created_at"

    search_fields = ["phone_number", "reference"]

    list_filter = ("created_at", "is_successful", "reward_reason")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoAvailableNetworkResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoAvailableNetworkResource
    date_hierarchy = "created_at"

    search_fields = [
        "name",
    ]

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoDailyAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoDailyAnalyticsResource
    date_hierarchy = "created_at"

    # search_fields = ["name",]

    list_filter = ("created_at", "network_provider")

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("id")
        return data


class TelcoDailySubscriptionResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoDailySubscriptionResource
    date_hierarchy = "created_at"

    search_fields = [
        "phone_number",
    ]

    list_filter = ("created_at", "is_successful", "network_provider", "game_type", "subscription_type")

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("id")
        return data


class NitroSwitchDataSyncResourceAdmin(ImportExportModelAdmin):
    resource_class = NitroSwitchDataSyncResource
    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NitroSwitchDataResourceAdmin(ImportExportModelAdmin):
    resource_class = NitroSwitchDataResource
    date_hierarchy = "created_at"

    search_fields = ["cp_trans_id", "phone_number"]

    list_filter = [
        "created_at",
        "expiry_date",
        "request_date",
        NitroswitchActionFilter,
        "subscription_type",
        "subscription_status",
        NitroswitchSericeType,
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NitroSwitchDailySubscriptionResourceAdmin(ImportExportModelAdmin):
    resource_class = NitroSwitchDailySubscriptionResource
    date_hierarchy = "created_at"

    search_fields = ["cp_trans_id", "phone_number"]

    list_filter = [
        "created_at",
        "subscription_status",
        "subscription_type",
        NitroswitchActionFilter,
        NitroswitchSericeType,
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NitroSwitchDailyGameBreakdownResourceAdmin(ImportExportModelAdmin):
    resource_class = NitroSwitchDailyGameBreakdownResource
    date_hierarchy = "created_at"

    search_fields = ["cp_trans_id", "phone_number"]

    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NitroswitchContentDeliveryLogsResourceAdmin(ImportExportModelAdmin):
    resource_class = NitroswitchContentDeliveryLogsResource
    date_hierarchy = "created_at"

    # search_fields = ["cp_trans_id", "phone_number"]

    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NitroSwitchSMSMOResourceAdmin(ImportExportModelAdmin):
    resource_class = NitroSwitchSMSMOResource
    date_hierarchy = "created_at"

    # search_fields = ["cp_trans_id", "phone_number"]

    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NitroswitchAiFortuneSubscriptionResourceAdmin(ImportExportModelAdmin):
    resource_class = NitroswitchAiFortuneSubscriptionResource
    date_hierarchy = "created_at"

    search_fields = ["phone_number"]

    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class GameShowParticipantResourceAdmin(ImportExportModelAdmin):
    resource_class = GameShowParticipantResource
    date_hierarchy = "created_at"

    search_fields = ["phone_number", "full_name"]

    list_filter = ["created_at", "interest_status", "based_in_lagos"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PendingAsyncTaskResourceAdmin(ImportExportModelAdmin):
    resource_class = TelcoUnsubscriptionRequestResource
    date_hierarchy = "created_at"
    search_fields = [
        "task_id",
    ]

    list_filter = ["is_treated", "purpose", "is_a_free_trial"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoUnsubscriptionRequestAdmin(ImportExportModelAdmin):
    resource_class = TelcoUnsubscriptionRequest
    date_hierarchy = "created_at"
    search_fields = ["phone_number", "service_id"]

    list_filter = ["treated", "network"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MonetizeAiSubscriptionAdmin(ImportExportModelAdmin):
    resource_class = MonetizeAiSubscriptionResource
    date_hierarchy = "created_at"
    search_fields = [
        "phone_number",
    ]


class SoccerPredictionRequestLogsAdmin(ImportExportModelAdmin):
    resource_class = SoccerPredictionRequestLogsResource
    date_hierarchy = "created_at"
    search_fields = ["phone_number"]

    list_filter = ["created_at", "treated"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SuccessfullyChargeSoccerPredictionRequestLogsAdmin(ImportExportModelAdmin):
    resource_class = SuccessfullyChargeSoccerPredictionRequestLogsResource
    date_hierarchy = "created_at"
    search_fields = ["phone_number"]

    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TelcoLibertyLifeSubscriptionAdmin(ImportExportModelAdmin):
    resource_class = TelcoLibertyLifeSubscriptionResource
    date_hierarchy = "created_at"
    search_fields = [
        "phone_number",
    ]

    list_filter = ["created_at", "subscription_used"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TempTelcoDataSyncAdmin(ImportExportModelAdmin):
    resource_class = TempTelcoDataSyncResource
    date_hierarchy = "created_at"
    # search_fields = ["phone_number",]

    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class PendingNitroswitchAsyncTaskResourceAdmin(ImportExportModelAdmin):
    resource_class = PendingNitroswitchAsyncTaskResource
    search_fields = ["click_id", "phone_number", "number_of_renewals", "game_type"]
    list_filter = ["is_treated", "created_at", "purpose"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class RedoceanAfricaSubscriptionDataSyncResourceAdmin(ImportExportModelAdmin):
    resource_class = RedoceanAfricaSubscriptionDataSyncResource

    list_filter = ["created_at"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class RedoceanAfricaContentDeliveryDataSyncResourceAdmin(ImportExportModelAdmin):
    resource_class = RedoceanAfricaContentDeliveryDataSyncResource

    list_filter = ["created_at"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class RedoceanAfricaSubscriberResourceAdmin(ImportExportModelAdmin):
    resource_class = RedoceanAfricaSubscriberResource

    search_fields = ["phone_number",]
    
    list_filter = ["created_at", "is_subscribed"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class RedoceanAfricaDailySubscriptionResourceAdmin(ImportExportModelAdmin):
    resource_class = RedoceanAfricaDailySubscriptionResource

    search_fields = ["phone_number",]

    list_filter = ["created_at", "subscription_status", "subscription_type"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class RedoceanAfricaRequestLogsResourceAdmin(ImportExportModelAdmin):
    resource_class = RedoceanAfricaRequestLogsResource

    search_fields = ["phone_number",]

    list_filter = ["created_at", "type_of_request"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(CoralpayTransactions, CoralpayTransactionsResourceAdmin)
admin.site.register(CoralpayUserCode, CoralpayUserCodeResourceAdmin)
admin.site.register(UssdLotteryPayment, UssdLotteryPaymentResourceAdmin)
admin.site.register(SoccerPrediction, SoccerPredictionResourceAdmin)
admin.site.register(UssdPayment, UssdPaymentResourceAdmin)
admin.site.register(UssdConstantVariable, UssdConstantVariableResourceAdmin)
admin.site.register(ExtraTelcoOptions, ExtraTelcoOptionsResourceAdmin)
admin.site.register(TelcoSubscriptionPlan, TelcoSubscriptionPlanResourceAdmin)
admin.site.register(UserTelcoSubscription, UserTelcoSubscriptionResourceAdmin)
admin.site.register(UserTelcoSubscriptionTransaction, UserTelcoSubscriptionTransactionResourceAdmin)
admin.site.register(TelcoCharge, TelcoChargeResourceAdmin)
admin.site.register(TelcoDataSync, TelcoDataSyncResourceAdmin)
admin.site.register(TelcoDataSyncJsons, TelcoDataSyncJsonsResourceAdmin)
admin.site.register(TelcoConstant, TelcoConstantResourceAdmin)
admin.site.register(TelcoUsers, TelcoUsersResourceAdmin)
admin.site.register(TelcoUssdSessionIds, TelcoUssdSessionIdsResourceAdmin)
admin.site.register(SecureDDataDump, SecureDDataDumpResourceAdmin)
admin.site.register(TelcoSubscriptionRequest, TelcoSubscriptionRequestResourceAdmin)
admin.site.register(TelcoAggregatorNotification, TelcoAggregatorNotificationResourceAdmin)
admin.site.register(SecureDTransaction, SecureDTransactionResourceAdmin)
admin.site.register(SubscriptionViaSmsDataDump, SubscriptionViaSmsDataDumpResourceAdmin)
admin.site.register(TelcoGameWebSubscribers, TelcoGameWebSubscribersResourceAdmin)
admin.site.register(TelcoDailySubscriptionAnalytics, TelcoDailySubscriptionAnalyticsResourceAdmin)
admin.site.register(TelcoAirtimeReward, TelcoAirtimeRewardResourceAdmin)
admin.site.register(TelcoAvailableNetwork, TelcoAvailableNetworkResourceAdmin)
admin.site.register(TelcoDailyAnalytics, TelcoDailyAnalyticsResourceAdmin)
admin.site.register(TelcoDailySubscription, TelcoDailySubscriptionResourceAdmin)
admin.site.register(NitroSwitchDataSync, NitroSwitchDataSyncResourceAdmin)
admin.site.register(NitroSwitchData, NitroSwitchDataResourceAdmin)
admin.site.register(NitroSwitchDailySubscription, NitroSwitchDailySubscriptionResourceAdmin)
admin.site.register(NitroSwitchDailyGameBreakdown, NitroSwitchDailyGameBreakdownResourceAdmin)
admin.site.register(NitroswitchContentDeliveryLogs, NitroswitchContentDeliveryLogsResourceAdmin)
admin.site.register(NitroSwitchSMSMO, NitroSwitchSMSMOResourceAdmin)
admin.site.register(NitroswitchAiFortuneSubscription, NitroswitchAiFortuneSubscriptionResourceAdmin)
admin.site.register(GameShowParticipant, GameShowParticipantResourceAdmin)
admin.site.register(PendingAsyncTask, PendingAsyncTaskResourceAdmin)
admin.site.register(TelcoUnsubscriptionRequest, TelcoUnsubscriptionRequestAdmin)
admin.site.register(MonetizeAiSubscription, MonetizeAiSubscriptionAdmin)
admin.site.register(TelcoLibertyLifeSubscription, TelcoLibertyLifeSubscriptionAdmin)
admin.site.register(SoccerPredictionRequestLogs, SoccerPredictionRequestLogsAdmin)
admin.site.register(SuccessfullyChargeSoccerPredictionRequestLogs, SuccessfullyChargeSoccerPredictionRequestLogsAdmin)
admin.site.register(TempTelcoDataSync, TempTelcoDataSyncAdmin)
admin.site.register(PendingNitroswitchAsyncTask, PendingNitroswitchAsyncTaskResourceAdmin)
admin.site.register(RedoceanAfricaSubscriptionDataSync, RedoceanAfricaSubscriptionDataSyncResourceAdmin)
admin.site.register(RedoceanAfricaContentDeliveryDataSync, RedoceanAfricaContentDeliveryDataSyncResourceAdmin)
admin.site.register(RedoceanAfricaSubscriber, RedoceanAfricaSubscriberResourceAdmin)
admin.site.register(RedoceanAfricaDailySubscription, RedoceanAfricaDailySubscriptionResourceAdmin)
admin.site.register(RedoceanAfricaRequestLogs, RedoceanAfricaRequestLogsResourceAdmin)
