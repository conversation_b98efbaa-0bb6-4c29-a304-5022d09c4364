import json
import random
import string
import time
import uuid
from concurrent.futures import <PERSON>hr<PERSON>PoolExecutor
from datetime import datetime, timed<PERSON>ta
from queue import Queue
from random import choice
from string import Template
from time import sleep
from typing import List

import pytz
import requests
from celery import shared_task
from django.conf import settings
from django.db import connection
from django.db.models import F, Q, Sum

from ads_tracker.models import MobidTracker
from awoof_app.models import AskAiData, AwoofGameTable
from broad_base_communication.helpers import xml_data_formater
from function_profiler import time_execution_decorator
from main.helpers.helper_functions import currency_formatter, link_shortener
from main.helpers.pabbly_manager import create_pabbly_plan
from main.helpers.redis_storage import RedisStorage
from main.helpers.watupay_manager import generate_ussd_collection_code
from main.helpers.whisper_sms_managers import (
    send_simple_sms_campaign,
    send_sms_for_ussd_topup,
)
from main.models import (
    ConstantVariable,
    LotteryBatch,
    LotteryGlobalJackPot,
    LotteryModel,
    LottoTicket,
    PayoutTransactionTable,
    UserProfile,
)
from main.tasks import (
    celery_send_whatsapp_payment_notification_admin,
    celery_send_whatsapp_payment_notification_admin_for_soccer_payment,
    create_collection_details_for_lottery_player,
    lottery_play_engange_event,
)
from main.ussd.helpers import Utility

from overide_print import print
from prices.game_price import (
    ON_DEMAND_SERVICE_IDS,
    SUBSCRIPTION_SERVICE_IDS,
    InstantCashOutPriceModel,
    QuikaPriceModel,
    SalaryForLifePriceModel,
    VirtualSoccerPriceModel,
    WyseCashPriceModel,
    new_secure_d_and_upstream_service_and_prodct_details,
    secure_d_and_upstream_service_and_prodct_details,
    telco_subscription_service_id_game_identification,
)
from wallet_app.models import DebitCreditRecord, ServiceChargeWallet, UserWallet
from wallet_app.tasks import notify_admin_of_user_funding
from wyse_ussd.enums import PurposeChoices
from wyse_ussd.helper.airtime_reward import AgencyBankingAirtimeReward
from wyse_ussd.helper.nitroswitch_helper import (
    nitroswitch_sms_content_delivery,
    nitroswitch_sms_gateway,
    request_and_expiry_date_helper,
)
from wyse_ussd.helper.redbiller_ussd_helper import redbiller_ussd_payment_code
from wyse_ussd.models import (
    MonetizeAiSubscription,
    NitroSwitchDailyGameBreakdown,
    NitroSwitchDailySubscription,
    NitroSwitchData,
    NitroSwitchSmsToAiSubscription,
    PendingAsyncTask,
    SecureDTransaction,
    SoccerPrediction,
    SubscriptionViaSmsDataDump,
    SuccessfullyChargeSoccerPredictionRequestLogs,
    TelcoAggregatorNotification,
    TelcoCharge,
    TelcoConstant,
    TelcoDataSync,
    TelcoLibertyLifeSubscription,
    TelcoSubscriptionPlan,
    TelcoUnsubscriptionRequest,
    UssdLotteryPayment,
    create_wyse_lotto_ticket,
)

USER_WALLET_TRANSACTION_ROUTE = {
    "WYSE_CASH": "WYSE_CASH_GAME_PLAY",
    "SALARY_FOR_LIFE": "SAL_4_LIFE_GAME_PLAY",
    "INSTANT_CASHOUT": "INSTANT_CASHOUT_GAME_PLAY",
}


def replace_underscore(input_string):
    if "_" in input_string:
        return input_string.replace("_", "").title()
    else:
        return input_string.title()


def get_lottery_stake_amount(lottery_instance, ticket_qs):
    # sum lottery stake amount
    if lottery_instance.lottery_type == "INSTANT_CASHOUT":
        # stake_amt = LottoTicket().instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
        #     ticket_count=ticket_qs.count()
        # )
        # lottery_stake_amount = stake_amt.get("stake_amount")
        lottery_stake_amount = lottery_instance.stake_amount * ticket_qs.count()

    elif lottery_instance.lottery_type == "QUIKA":
        lottery_stake_amount = lottery_instance.stake_amount * ticket_qs.count()

    elif lottery_instance.lottery_type == "SALARY_FOR_LIFE":
        stake_amt = LottoTicket().salary_for_life_stake_amount_pontential_winning_for_lotto_agents(ticket_count=ticket_qs.count())
        lottery_stake_amount = stake_amt.get("stake_amount")

    elif lottery_instance.lottery_type == "BANKER":
        lottery_stake_amount = lottery_instance.stake_amount * ticket_qs.count()

    elif lottery_instance.lottery_type == "AWOOF":
        lottery_stake_amount = lottery_instance.band * ticket_qs.count()

    elif lottery_instance.lottery_type == "SOCCER_CASH":
        lottery_stake_amount = lottery_instance.stake_amount * ticket_qs.count()

    elif lottery_instance.lottery_type == "WYSE_CASH":
        lottery_stake_amount = lottery_instance.stake_amount * ticket_qs.count()

    else:
        lottery_stake_amount = None

    return lottery_stake_amount


def update_wallet_and_engage_event_handler(
    game_play_id,
    ticket_instance,
    user_wallet,
    amount,
    transfrom=None,
    from_telco_channel=False,
):
    model_fields = [field.name for field in ticket_instance._meta.get_fields()]

    # trans_from = "GAME"

    if "freemium" in model_fields:
        pass
    else:
        USER_WALLET_TRANSACTION_ROUTE.get(str(ticket_instance.lottery_type), None)

    # if ticket_instance.identity_id is None:

    # else:
    #     transaction_ref = ticket_instance.identity_id

    transaction_ref = f"{uuid.uuid4()}{int(time.time())}"

    debit_credit_record = DebitCreditRecord.create_record(
        phone_number=ticket_instance.user_profile.phone_number,
        amount=amount,
        channel="WEB",
        reference=transaction_ref,
        transaction_type="DEBIT",
    )
    wallet_payload = {"transaction_from": transfrom}

    UserWallet.deduct_wallet(
        user=ticket_instance.user_profile,
        amount=amount,
        channel="WEB",
        transaction_id=debit_credit_record.reference,
        user_wallet_type="GAME_PLAY_WALLET",
        from_telco_channel=from_telco_channel,
        **wallet_payload,
    )

    # UserWallet.update_wallet_after_payment(
    #     user_profile=ticket_instance.user_profile,
    #     wallet_tag="WEB",
    #     trans_from=transfrom,
    #     amount=amount,
    # )

    # lottery_type = str(ticket_instance.lottery_type).replace('_', ' ').title()
    lottery_type = replace_underscore(ticket_instance.lottery_type)
    engage_event_payload = {
        "event": f"{lottery_type} LOTTERY PAYMENT",
        "properties": {
            "game_id": game_play_id,
        },
    }
    lottery_play_engange_event.delay(
        user_id=user_wallet.user.id,
        is_user_profile_id=True,
        **engage_event_payload,
    )


def possible_lottery_or_game_played(game_play_id):
    from sport_app.models import SoccerOddPredictionTable

    if AwoofGameTable.objects.filter(game_play_id=game_play_id).exists():
        # found  AwoofGameTable
        return AwoofGameTable.objects.filter(game_play_id=game_play_id)

    elif LottoTicket.objects.filter(game_play_id=game_play_id, is_duplicate=False).exists():
        # found LottoTicket
        return LottoTicket.objects.filter(game_play_id=game_play_id, is_duplicate=False)

    elif LotteryModel.objects.filter(game_play_id=game_play_id, is_duplicate=False).exists():
        # found LotteryModel
        return LotteryModel.objects.filter(game_play_id=game_play_id, is_duplicate=False)

    elif SoccerPrediction.objects.filter(game_id=game_play_id).exists():
        # found  SoccerPrediction
        return SoccerPrediction.objects.filter(game_id=game_play_id)

    elif SoccerOddPredictionTable.objects.filter(game_pay_id=game_play_id).exists():
        # found prediction subscription
        return SoccerOddPredictionTable.objects.filter(game_pay_id=game_play_id)
    # elif SureBanker.objects.filter(game_play_id=game_play_id, is_duplicate=False).exists():
    #     # found  SureBanker
    #     return SureBanker.objects.filter(game_play_id=game_play_id, is_duplicate=False)

    else:
        # not found in any of the models
        return None


@shared_task
def share_ussd_payment_across_lottery_pool(
    phone_number,
    amount,
    game_play_id,
    from_web=False,
    transfrom=None,
    transaction_unique_id=None,
    from_telco_channel=False,
):
    from sport_app.models import SoccerOddPredictionTable

    """
    This task is called when a user has successfully paid for a lottery ticket
    """
    print("game_play_id", game_play_id, "\n\n\n\n")
    print("ussd_lottery_payment_via_coralpay_update called")

    user_profile = UserProfile.objects.filter(phone_number=phone_number).last()

    # get or create user active wallet
    user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
    if user_wallet is None:
        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

    if from_web is True:
        if from_telco_channel is False:
            user_wallet_bal = user_wallet.game_available_balance
        else:
            user_wallet_bal = user_wallet.telco_wallet_balance
    else:
        # fund user wallet

        transaction_unique_id = None

        if transaction_unique_id is None:
            transaction_ref = f"{uuid.uuid4()}{int(time.time())}"
        else:
            transaction_ref = transaction_unique_id

        wallet_payload = {
            "transaction_from": transfrom,
        }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=user_wallet.user.phone_number,
            amount=amount,
            channel="USSD",
            reference=transaction_ref,
            transaction_type="CREDIT",
        )

        UserWallet.fund_wallet(
            user=user_wallet.user,
            amount=int(amount),
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="GAME_PLAY_WALLET",
            from_telco_channel=from_telco_channel,
            **wallet_payload,
        )

        # notify admin
        notify_admin_of_user_funding.delay(
            amount=int(amount),
            phone_number=user_profile.phone_number,
            channel=transfrom,
        )

        user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()

        if from_telco_channel is False:
            user_wallet_bal = user_wallet.game_available_balance
        else:
            user_wallet_bal = user_wallet.telco_wallet_balance

        # user_wallet_bal = user_wallet.game_available_balance

    # get the lottery game play id that this payment was made for
    # game_play_id = (
    #     UssdLotteryPayment.objects.filter(user=user_profile).last().game_play_id
    # )

    # get game lottery qs
    lotto_query_set = possible_lottery_or_game_played(game_play_id=game_play_id)

    if lotto_query_set:
        # print(
        #     f"""
        # :::::::::::::::::::::::::
        # ticket_instance.lottery_type: {lotto_query_set.last().lottery_type}
        # :::::::::::::::::::::::::
        # :::::::::::::::::::::::::
        # """
        # )
        # get a single ticket instance
        ticket_instance = lotto_query_set.last()
        # get stake_amount
        lottery_stake_amount = get_lottery_stake_amount(lottery_instance=ticket_instance, ticket_qs=lotto_query_set)
        # check if queryset batch is still active
        if (
            ticket_instance.lottery_type == "AWOOF"
            or ticket_instance.lottery_type == "SOCCER_CASH"
            or ticket_instance.lottery_type == "PREDICTION_SUBSCRIPTION"
        ):
            if ticket_instance.lottery_type == "AWOOF":
                game_play_id_instance_id = UssdLotteryPayment.objects.filter(game_play_id=game_play_id, is_successful=False, is_verified=False).last()

                if game_play_id_instance_id is None:
                    return None
                else:
                    game_play_id_instance_id.is_successful = True
                    game_play_id_instance_id.is_verified = True
                    game_play_id_instance_id.save()

                    return AwoofGameTable().handle_ussd_awwof_payment(
                        ussd_lottery_payment_instance=game_play_id_instance_id,
                        from_web=from_web,
                    )

                    # return None
            if ticket_instance.lottery_type == "PREDICTION_SUBSCRIPTION":
                game_pay_instance_id = UssdLotteryPayment.objects.filter(game_play_id=game_play_id, is_successful=False, is_verified=False).last()

                if game_pay_instance_id is None:
                    return None
                else:
                    game_pay_instance_id.is_successful = True
                    game_pay_instance_id.is_verified = True
                    game_pay_instance_id.save()

                    return SoccerOddPredictionTable().handle_ussd_prediction_subscription_payment(
                        ussd_lottery_payment_instance=game_pay_instance_id,
                        from_web=from_web,
                    )

        else:
            if ticket_instance.batch.is_active is True:
                pass

            else:
                # get current batch
                active_batch = LotteryBatch.objects.filter(lottery_type=ticket_instance.lottery_type, is_active=True).last()
                if active_batch:
                    pass
                else:
                    active_batch = LotteryBatch.create_batch(lottery_type=ticket_instance.lottery_type)
                    active_batch.save()

                    sleep(2)
                    lotto_query_set.update(batch=active_batch)

        game_play_id_instance_id = UssdLotteryPayment.objects.filter(game_play_id=game_play_id).last()

        if game_play_id_instance_id.lottery_type == "AWOOF":
            transfrom = "AWOOF_GAME_PLAY"
        elif game_play_id_instance_id.lottery_type == "SOCCER_CASH":
            transfrom = "SOCCER_CASH_GAME_PLAY"
        elif game_play_id_instance_id.lottery_type == "VIRTUAL_SOCCER":
            transfrom = "VIRTUAL_SOCCER_GAME_PLAY"
        elif game_play_id_instance_id.lottery_type == "QUIKA":
            transfrom = "QUIKA_GAME_PLAY"
        elif game_play_id_instance_id.lottery_type == "INSTANT_CASHOUT":
            transfrom = "INSTANT_CASHOUT_GAME_PLAY"
        elif game_play_id_instance_id.lottery_type == "SALARY_FOR_LIFE":
            transfrom = "SAL_4_LIFE_GAME_PLAY"
        elif game_play_id_instance_id.lottery_type == "WYSE_CASH":
            transfrom = "WYSE_CASH_GAME_PLAY"
        elif game_play_id_instance_id.lottery_type == "BANKER":
            transfrom = "BANKER_GAME_PLAY"
        elif game_play_id_instance_id.lottery_type == "PREDICTION_SUBSCRIPTION":
            transfrom = "PREDICTION_SUBSCRIPTION_GAME_PLAY"
        else:
            transfrom = "GAME_PLAY"

        # lottery_stake_amount

        if (user_wallet_bal < lottery_stake_amount) and (user_wallet_bal < 150):
            if from_web is False:
                pass
            else:
                transaction_ref = f"{uuid.uuid4()}-{int(time.time())}"

                debit_credit_record = DebitCreditRecord.create_record(
                    phone_number=user_profile.phone_number,
                    amount=amount,
                    channel="WEB",
                    reference=transaction_ref,
                    transaction_type="CREDIT",
                )

                wallet_payload = {
                    "transaction_from": "GAME_PLAY_REVERSAL",
                    "game_type": ticket_instance.lottery_type,
                    "game_play_id": game_play_id,
                }
                UserWallet.fund_wallet(
                    user=user_profile,
                    amount=amount,
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="GAME_PLAY_WALLET",
                    from_telco_channel=from_telco_channel,
                    **wallet_payload,
                )

            return {"status": "failed", "message": "Insufficient amount charged"}

        else:
            update_wallet_and_engage_event_handler(
                game_play_id=game_play_id,
                ticket_instance=ticket_instance,
                user_wallet=user_wallet,
                amount=amount,
                transfrom=transfrom,
                from_telco_channel=from_telco_channel,
            )

            amount_paid = round((lottery_stake_amount / lotto_query_set.count()), 2)
            if (game_play_id_instance_id.lottery_type == "INSTANT_CASHOUT") and (ticket_instance.played_via_telco_channel is True):
                if lottery_stake_amount == 200:
                    amount_paid = 150

            for lottery in lotto_query_set:
                lottery.paid = True
                lottery.amount_paid = amount_paid
                lottery.save()

            """ TEMP FEATURE FOR INSTANT CASHOUT PLAYED VIA TELCO CHANNEL
            THIS FEATURE WAS ADDED BECAUSE WE'RE CHARGING 300 FOR INSTANT CASHOUT PLAYED VIA TELCO CHANNEL
            BUT THE TICKET ARE TO BE CREATED TWICE AND MARK AS PAID AND AMOUNT PAID AS 150
              """
            if (
                (game_play_id_instance_id.lottery_type == "INSTANT_CASHOUT")
                and (ticket_instance.played_via_telco_channel is True)
                and (amount_paid == 150)
                and (amount != 150)
            ):
                unpaid_instant_cashout_ticket = LottoTicket.objects.filter(
                    user_profile=ticket_instance.user_profile,
                    paid=False,
                ).last()

                unpaid_ticket_pick = ""
                unpaid_ticket_game_id = ""

                if unpaid_instant_cashout_ticket:
                    unpaid_ticket_pick = unpaid_instant_cashout_ticket.ticket
                    unpaid_ticket_game_id = unpaid_instant_cashout_ticket.game_play_id

                    unpaid_instant_cashout_ticket.paid = True
                    unpaid_instant_cashout_ticket.amount_paid = amount_paid
                    unpaid_instant_cashout_ticket.save()

                """ I KNOW SENDING TELCO PAYMENT RECEIPT FROM HERE IS A BAD IMPLEMENTATION.
                THE REASON WHY I'M SENDING IT FROM HERE IS BEACUSE I CAN ONLY GET THE GAME ID FOR THE TWO TICKET THAT WAS PAID AS ONE
                AND THE SYSTEM NEEDS TO SEND THE RECEIPT TO THE USER CONTAINING THE TICKETS AND TWO GAME IDS

                """
                get_current_time = ((datetime.now() + timedelta(minutes=5)).time()).strftime("%H:%M")

                current_time = datetime.strptime(str(get_current_time), "%H:%M")
                current_time = current_time.strftime("%I:%M %p")

                now = datetime.now()
                week_name = str(now.strftime("%A"))[:3]
                get_current_date = now.strftime("%d/%m/%y")

                _message = f"Win up to {str(currency_formatter(ticket_instance.potential_winning)).replace('.00', '')}! You're in the INSTANT CASH draw. your first pick : ({ticket_instance.ticket}),your second pick: ({unpaid_ticket_pick}) Game id-1: {ticket_instance.game_play_id}. 2: {unpaid_ticket_game_id}. Next draw: {week_name} {str(get_current_date).replace('/','-')} {current_time}.\nJoin us on Telegram https://bit.ly/3Qeuor2"

                print("handle_telco_payemnt_receipt called for two sms :::::::::::::::::::::::::::: n\n\n\n\n")
                handle_telco_payemnt_receipt.apply_async(
                    queue="telcocharge1",
                    args=["d97f0fd3-4521-44f4-940b-a4ffa8035109"],
                    kwargs={
                        "receiver": ticket_instance.user_profile.phone_number,
                        "_message": _message,
                        "pontential_winning": str(currency_formatter(ticket_instance.potential_winning)).replace(".00", ""),
                        "ticket_1": ticket_instance.ticket,
                        "ticket_2": unpaid_ticket_pick,
                        "game_id_1": ticket_instance.game_play_id,
                        "game_id_2": unpaid_ticket_game_id,
                        "week_name": week_name,
                        "current_date": str(get_current_date).replace("/", "-"),
                        "current_time": str(current_time),
                    },
                    countdown=30,
                )

            """ END OF TEMP FEATURE FOR INSTANT CASHOUT PLAYED VIA TELCO CHANNEL """

            """ CATERING FOR SERVICE CHARGES AND VALIDATING PAYMENT AMOUNT """
            _lottery_types = [
                "INSTANT_CASHOUT",
                "WYSE_CASH",
                "SALARY_FOR_LIFE",
                "VIRTUAL_SOCCER",
                "QUIKA",
            ]
            if game_play_id_instance_id.lottery_type in _lottery_types:
                if game_play_id_instance_id.lottery_type == "QUIKA":
                    quika_price_model = QuikaPriceModel()
                    price_model = quika_price_model = quika_price_model.get_ticket_price_details_with_stake_amount(
                        channel=game_play_id_instance_id.channel,
                        stake_amount=game_play_id_instance_id.amount,
                    )
                    if isinstance(price_model, dict):
                        africastalking_service_charge = price_model.get("africastalking_charge", 0)
                        woven_charge = price_model.get("woven_service_charge", 0)

                        if africastalking_service_charge > 0:
                            ServiceChargeWallet().add_fund(
                                amount=africastalking_service_charge,
                                service="AFRICASTALKING",
                            )

                        if woven_charge > 0:
                            ServiceChargeWallet().add_fund(amount=woven_charge, service="WOVEN")

                elif game_play_id_instance_id.lottery_type == "INSTANT_CASHOUT":
                    i_cash_price_model = InstantCashOutPriceModel()

                    # print(f"įffffffskkskfkjrnwHJRWE")

                    price_model = i_cash_price_model.get_ticket_price_details_with_stake_amount(
                        channel=game_play_id_instance_id.channel,
                        stake_amount=game_play_id_instance_id.amount,
                    )

                    # print(f"price_model", price_model)
                    if isinstance(price_model, dict):
                        africastalking_service_charge = price_model.get("africastalking_charge", 0)
                        woven_charge = price_model.get("woven_service_charge", 0)

                        # print(
                        #     f"africastalking_service_charge {africastalking_service_charge}"
                        # )
                        # print(f"woven_charge {woven_charge},\n\n\n\n\n")

                        if africastalking_service_charge > 0:
                            ServiceChargeWallet().add_fund(
                                amount=africastalking_service_charge,
                                service="AFRICASTALKING",
                            )

                        if woven_charge > 0:
                            ServiceChargeWallet().add_fund(amount=woven_charge, service="WOVEN")

                elif game_play_id_instance_id.lottery_type == "WYSE_CASH":
                    wyse_cash_model = WyseCashPriceModel()

                    for _wyse_cash in lotto_query_set:
                        price_model = wyse_cash_model.ticket_price(
                            channel=game_play_id_instance_id.channel,
                            band=_wyse_cash.band,
                            no_of_line=1,
                        )

                        if isinstance(price_model, dict):
                            africastalking_service_charge = price_model.get("africastalking_charge", 0)
                            woven_charge = price_model.get("woven_service_charge", 0)

                            if africastalking_service_charge > 0:
                                ServiceChargeWallet().add_fund(
                                    amount=africastalking_service_charge,
                                    service="AFRICASTALKING",
                                )

                            if woven_charge > 0:
                                ServiceChargeWallet().add_fund(amount=woven_charge, service="WOVEN")

                elif game_play_id_instance_id.lottery_type == "SALARY_FOR_LIFE":
                    sal_4_life_price_model = SalaryForLifePriceModel()
                    price_model = sal_4_life_price_model.get_ticket_price_details_with_stake_amount(
                        channel=game_play_id_instance_id.channel,
                        stake_amount=game_play_id_instance_id.amount,
                    )
                    if isinstance(price_model, dict):
                        africastalking_service_charge = price_model.get("africastalking_charge", 0)
                        woven_charge = price_model.get("woven_service_charge", 0)

                        if africastalking_service_charge > 0:
                            ServiceChargeWallet().add_fund(
                                amount=africastalking_service_charge,
                                service="AFRICASTALKING",
                            )

                        if woven_charge > 0:
                            ServiceChargeWallet().add_fund(amount=woven_charge, service="WOVEN")

                elif game_play_id_instance_id.lottery_type == "VIRTUAL_SOCCER":
                    virtual_soccer_price_model = VirtualSoccerPriceModel()
                    price_model = virtual_soccer_price_model.get_ticket_price_details_with_stake_amount(
                        channel=game_play_id_instance_id.channel,
                        stake_amount=game_play_id_instance_id.amount,
                    )
                    if isinstance(price_model, dict):
                        africastalking_service_charge = price_model.get("africastalking_charge", 0)
                        woven_charge = price_model.get("woven_service_charge", 0)

                        if africastalking_service_charge > 0:
                            ServiceChargeWallet().add_fund(
                                amount=africastalking_service_charge,
                                service="AFRICASTALKING",
                            )

                        if woven_charge > 0:
                            ServiceChargeWallet().add_fund(amount=woven_charge, service="WOVEN")

            game_play_id_instance_id.is_verified = True
            game_play_id_instance_id.is_successful = True
            game_play_id_instance_id.save()

            game_play_id = UssdLotteryPayment.objects.filter(~Q(id=game_play_id_instance_id.id), Q(game_play_id=game_play_id)).delete()

    else:
        return {
            "status": "failed",
            "message": f"there's issue with this game play id {game_play_id}",
        }


@shared_task
def share_web_payment_across_lottery_pool(phone_number, amount, game_play_id, from_web=False, transfrom=None):
    """
    This task is called when a user has successfully paid for a lottery ticket
    """
    print("game_play_id", game_play_id, "\n\n\n\n")
    print("ussd_lottery_payment_via_coralpay_update called")

    user_profile = UserProfile.objects.filter(phone_number=phone_number).last()

    # get or create user active wallet
    user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
    if user_wallet is None:
        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

    if from_web is True:
        user_wallet_bal = user_wallet.game_available_balance
    else:
        # fund user wallet
        user_wallet.game_available_balance += int(amount)
        user_wallet.transaction_from = transfrom
        user_wallet.save()

        # notify admin
        notify_admin_of_user_funding.delay(
            amount=int(amount),
            phone_number=user_profile.phone_number,
            channel=transfrom,
        )

        user_wallet_bal = user_wallet.game_available_balance

    # get the lottery game play id that this payment was made for
    # game_play_id = (
    #     UssdLotteryPayment.objects.filter(user=user_profile).last().game_play_id
    # )

    # get game lottery qs
    lottoticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id)
    print("lottoticket_qs tottoticket", lottoticket_qs, "\n\n\n\n")
    if not lottoticket_qs.exists():
        lottoticket_qs = LotteryModel.objects.filter(game_play_id=game_play_id)

        if not lottoticket_qs.exists():
            # soccer cash query
            lottoticket_qs = SoccerPrediction.objects.filter(game_id=game_play_id)

            if not lottoticket_qs.exists():
                return "No game found"

            lottery_stake_amount = lottoticket_qs.aggregate(Sum("stake_amount"))["stake_amount__sum"]

            lottery_instance = lottoticket_qs.last()

            if user_wallet_bal < lottery_stake_amount:
                return "Insufficient funds"
            else:
                for lottery in lottoticket_qs:
                    lottery.paid = True
                    lottery.amount_paid = lottery.stake_amount
                    lottery.save()

                ticket_instance = lottoticket_qs.last()

                model_fields = [field.name for field in ticket_instance._meta.get_fields()]

                trans_from = "GAME"
                if "freemium" in model_fields:
                    trans_from = "SOCCER_CASH_GAME_PLAY"
                else:
                    if ticket_instance.lottery_type == "WYSE_CASH":
                        trans_from = "WYSE_CASH_GAME_PLAY"
                    elif ticket_instance.lottery_type == "SALARY_FOR_LIFE":
                        trans_from = "SAL_4_LIFE_GAME_PLAY"

                    elif ticket_instance.lottery_type == "INSTANT_CASHOUT":
                        trans_from = "INSTANT_CASHOUT_GAME_PLAY"

                user_wallet.game_available_balance -= amount
                user_wallet.transaction_from = trans_from
                user_wallet.save()

                game_play_id_instance_id = UssdLotteryPayment.objects.filter(game_play_id=game_play_id).last()

                game_play_id_instance_id.is_verified = True
                game_play_id_instance_id.is_successful = True
                game_play_id_instance_id.save()

                game_play_id_instance_id = game_play_id_instance_id.id

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #
                engage_event_payload = {
                    "event": "SOCCER CASH GAME PAYMENT",
                    "properties": {
                        "game_id": game_play_id,
                    },
                }
                lottery_play_engange_event.delay(
                    user_id=user_wallet.user.id,
                    is_user_profile_id=True,
                    **engage_event_payload,
                )

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #

                game_play_id = UssdLotteryPayment.objects.filter(~Q(id=game_play_id_instance_id), Q(game_play_id=game_play_id)).delete()

                print("done with soccer cash payment")

            print("no lottery ticket found")
            return "No lottery ticket found"

        else:
            print("lotterymodel ticket found")
            # sum lottery stake amount
            lottery_stake_amount = lottoticket_qs.aggregate(Sum("stake_amount"))["stake_amount__sum"]

            print("aggregated lottery_stake_amount", lottery_stake_amount, "\n\n\n\n")

            # check if lottery batch is still active
            lottery_instance = lottoticket_qs.last()
            if lottery_instance.batch.is_active is False:
                print("lottery model batch is not active")

                # wyse cash active bash
                lottery_batch = LotteryBatch.objects.filter(lottery_type=lottery_instance.batch.lottery_type, is_active=True).last()
                if lottery_batch is None:
                    lottery_batch = LotteryBatch.objects.create(lottery_type="WYSE_CASH", is_active=True)
                    lottoticket_qs.update(batch=lottery_batch)

            if user_wallet_bal < lottery_stake_amount:
                print("user wallet balance is less than lottery stake amount")
                amount_to_deduct_from_wallet = 0

                for lottery in lottoticket_qs:
                    if user_wallet_bal < lottery.stake_amount:
                        continue
                    else:
                        amount_to_deduct_from_wallet += lottery.stake_amount
                        user_wallet_bal -= lottery.stake_amount

                        lottery.paid = True
                        lottery.amount_paid = lottery.stake_amount
                        lottery.save()

                print(
                    "amount_to_deduct_from_wallet",
                    amount_to_deduct_from_wallet,
                    "\n\n\n\n",
                )
                if amount_to_deduct_from_wallet > 0:
                    ticket_instance = lottoticket_qs.last()

                    model_fields = [field.name for field in ticket_instance._meta.get_fields()]
                    trans_from = "GAME"
                    if "freemium" in model_fields:
                        trans_from = "SOCCER_CASH_GAME_PLAY"
                    else:
                        if ticket_instance.lottery_type == "WYSE_CASH":
                            trans_from = "WYSE_CASH_GAME_PLAY"
                        elif ticket_instance.lottery_type == "SALARY_FOR_LIFE":
                            trans_from = "SAL_4_LIFE_GAME_PLAY"

                        elif ticket_instance.lottery_type == "INSTANT_CASHOUT":
                            trans_from = "INSTANT_CASHOUT_GAME_PLAY"

                    user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                    user_wallet.game_available_balance -= amount_to_deduct_from_wallet
                    user_wallet.transaction_from = trans_from
                    user_wallet.save()

                game_play_id_instance_id = UssdLotteryPayment.objects.filter(game_play_id=game_play_id).last().id

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #
                engage_event_payload = {
                    "event": "WYSE CASH LOTTERY PAYMENT",
                    "properties": {
                        "game_id": game_play_id,
                    },
                }
                lottery_play_engange_event.delay(
                    user_id=user_wallet.user.id,
                    is_user_profile_id=True,
                    **engage_event_payload,
                )

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #

                game_play_id = UssdLotteryPayment.objects.filter(~Q(id=game_play_id_instance_id), Q(game_play_id=game_play_id)).delete()

            else:
                for lottery in lottoticket_qs:
                    lottery.paid = True
                    lottery.amount_paid = lottery.stake_amount
                    lottery.save()

                user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()

                if user_wallet is None:
                    user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                ticket_instance = lottoticket_qs.last()

                model_fields = [field.name for field in ticket_instance._meta.get_fields()]

                trans_from = "GAME"
                if "freemium" in model_fields:
                    trans_from = "SOCCER_CASH_GAME_PLAY"
                else:
                    if ticket_instance.lottery_type == "WYSE_CASH":
                        trans_from = "WYSE_CASH_GAME_PLAY"
                    elif ticket_instance.lottery_type == "SALARY_FOR_LIFE":
                        trans_from = "SAL_4_LIFE_GAME_PLAY"

                    elif ticket_instance.lottery_type == "INSTANT_CASHOUT":
                        trans_from = "INSTANT_CASHOUT_GAME_PLAY"

                user_wallet.game_available_balance -= lottery_stake_amount
                user_wallet.transaction_from = trans_from
                user_wallet.save()

                game_play_id_instance_id = UssdLotteryPayment.objects.filter(game_play_id=game_play_id).last().id

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #
                engage_event_payload = {
                    "event": "WYSE CASH LOTTERY PAYMENT",
                    "properties": {
                        "game_id": game_play_id,
                    },
                }
                lottery_play_engange_event.delay(
                    user_id=user_wallet.user.id,
                    is_user_profile_id=True,
                    **engage_event_payload,
                )

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #

                game_play_id = UssdLotteryPayment.objects.filter(~Q(id=game_play_id_instance_id), Q(game_play_id=game_play_id)).delete()

    else:
        # sum lottery stake amount
        lottery_stake_amount = lottoticket_qs.aggregate(Sum("stake_amount"))["stake_amount__sum"]

        # check if lottery batch is still active
        lottery_instance = lottoticket_qs.last()
        if not lottery_instance.batch.is_active is False:  # noqa
            # check lottery type
            if lottery_instance.lottery_type == "INSTANT_CASHOUT":
                active_batch = LotteryBatch.objects.filter(is_active=True, lottery_type="INSTANT_CASHOUT").last()
                if active_batch is None:
                    active_batch = LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT")
                    lottoticket_qs.update(batch=active_batch)

            elif lottery_instance.lottery_type == "SALARY_FOR_LIFE":
                active_batch = LotteryBatch.objects.filter(is_active=True, lottery_type="SALARY_FOR_LIFE").last()

                if active_batch is None:
                    global_jackpot = LotteryGlobalJackPot.get_jackpot_instance()

                    active_batch = LotteryBatch.objects.create(lottery_type="SALARY_FOR_LIFE", global_jackpot=global_jackpot)

                    lottoticket_qs.update(batch=active_batch)
        # print("user_wallet_bal", user_wallet_bal, "\n\n\n\n")
        # print("lottery_stake_amount", lottery_stake_amount, "\n\n\n\n")
        if user_wallet_bal < lottery_stake_amount:
            amount_to_deduct_from_wallet = 0

            for lottery in lottoticket_qs:
                if user_wallet_bal < lottery.stake_amount:
                    continue
                else:
                    amount_to_deduct_from_wallet += lottery.stake_amount
                    user_wallet_bal -= lottery.stake_amount

                    lottery.paid = True
                    lottery.amount_paid = lottery.stake_amount
                    lottery.save()

            # print(
            #     "amount_to_deduct_from_wallet", amount_to_deduct_from_wallet, "\n\n\n\n"
            # )
            # print("user_wallet_bal", user_wallet_bal, "\n\n\n\n")
            if amount_to_deduct_from_wallet > 0:
                ticket_instance = lottoticket_qs.last()

                model_fields = [field.name for field in ticket_instance._meta.get_fields()]

                trans_from = "GAME"

                if "freemium" in model_fields:
                    trans_from = "SOCCER_CASH_GAME_PLAY"
                else:
                    if ticket_instance.lottery_type == "WYSE_CASH":
                        trans_from = "WYSE_CASH_GAME_PLAY"
                    elif ticket_instance.lottery_type == "SALARY_FOR_LIFE":
                        trans_from = "SAL_4_LIFE_GAME_PLAY"

                    elif ticket_instance.lottery_type == "INSTANT_CASHOUT":
                        trans_from = "INSTANT_CASHOUT_GAME_PLAY"

                user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                user_wallet.game_available_balance -= user_wallet_bal
                user_wallet.transaction_from = trans_from
                user_wallet.save()

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #
                engage_event_payload = {
                    "event": f"{str(lottery.lottery_type).replace('_', ' ').title()} LOTTERY PAYMENT",
                    "properties": {
                        "game_id": lottery.game_play_id,
                    },
                }
                lottery_play_engange_event.delay(
                    user_id=user_wallet.user.id,
                    is_user_profile_id=True,
                    **engage_event_payload,
                )

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #

        else:
            for lottery in lottoticket_qs:
                lottery.paid = True
                lottery.amount_paid = lottery.stake_amount
                lottery.save()

            ticket_instance = lottoticket_qs.last()

            model_fields = [field.name for field in ticket_instance._meta.get_fields()]

            trans_from = "GAME"

            if "freemium" in model_fields:
                trans_from = "SOCCER_CASH_GAME_PLAY"
            else:
                if ticket_instance.lottery_type == "WYSE_CASH":
                    trans_from = "WYSE_CASH_GAME_PLAY"
                elif ticket_instance.lottery_type == "SALARY_FOR_LIFE":
                    trans_from = "SAL_4_LIFE_GAME_PLAY"

                elif ticket_instance.lottery_type == "INSTANT_CASHOUT":
                    trans_from = "INSTANT_CASHOUT_GAME_PLAY"

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            user_wallet.game_available_balance -= lottery_stake_amount
            user_wallet.transaction_from = trans_from
            user_wallet.save()

            game_play_id_instance_id = UssdLotteryPayment.objects.filter(game_play_id=game_play_id).last().id

            # ----------------------------------- ENGAGE EVENT ----------------------------------- #

            engage_event_payload = {
                "event": f"{str(lottery.lottery_type).replace('_', ' ').title()} LOTTERY PAYMENT",
                "properties": {
                    "game_id": lottery.game_play_id,
                },
            }
            lottery_play_engange_event.delay(
                user_id=user_wallet.user.id,
                is_user_profile_id=True,
                **engage_event_payload,
            )

            # ----------------------------------- ENGAGE EVENT ----------------------------------- #

            game_play_id = UssdLotteryPayment.objects.filter(~Q(id=game_play_id_instance_id), Q(game_play_id=game_play_id)).delete()


@shared_task
def create_ussd_virtual_account(phone_number):
    pass

    # get_old_woven = WovenAccountDetail.objects.filter(
    #     Q(phone_number=phone_number) & Q(is_active=True) & Q(wallet_tag="USSD")
    # ).last()

    # if str(phone_number).isdigit() is False:
    #     return None

    # user_profile = UserProfile.objects.filter(phone_number=phone_number).last()

    # if get_old_woven:
    #     # check if accout details exist
    #     woven_account_number = get_old_woven.vnuban
    #     woven_bank_name = get_old_woven.bank_name
    #     woven_account_name = get_old_woven.acct_name
    #     woven_account_reference = get_old_woven.account_ref

    #     # check if user has a wallet
    #     user_wallet = UserWallet.objects.filter(
    #         user=user_profile, wallet_tag="USSD"
    #     ).last()

    #     if user_wallet:
    #         user_wallet.account_ref = woven_account_reference
    #         user_wallet.woven_account = get_old_woven
    #         user_wallet.save()

    #         return {"status": "success", "message": "User already has a wallet"}

    #     else:
    #         # create wallet
    #         user_wallet = UserWallet.objects.create(
    #             user=user_profile,
    #             wallet_tag="USSD",
    #             account_ref=get_old_woven.account_ref,
    #             woven_account=get_old_woven,
    #         )

    #         return {"status": "success", "message": "User wallet created"}

    # else:
    #     generate_woven_collection_account_number(
    #         phone_number=phone_number, func_count=3, tag="USSD"
    #     )


@shared_task
def ussd_lottery_winner_reward(user_id, amount, trans_from="WINNINGS", played_via_telco_channel=False):
    user_profile = UserProfile.objects.filter(id=user_id).last()

    # get user wallet
    user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
    if user_wallet:
        # user_wallet.withdrawable_available_balance += amount
        # user_wallet.transaction_from = trans_from
        # user_wallet.save()

        payload = {
            "transaction_from": trans_from,
        }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=user_wallet.user.phone_number,
            amount=amount,
            channel="WEB",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="CREDIT",
        )

        if played_via_telco_channel is False:
            UserWallet.fund_wallet(
                user=user_wallet.user,
                amount=amount,
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="WINNINGS_WALLET",
                **payload,
            )
        else:
            if amount <= 300:
                UserWallet.fund_wallet(
                    user=user_wallet.user,
                    amount=amount,
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="AIRTIME_WALLET",
                    **payload,
                )

                ussd_airtime_reward.delay(amount=amount, phone_number=user_profile.phone_number)

            else:
                UserWallet.fund_wallet(
                    user=user_wallet.user,
                    amount=amount,
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="WINNINGS_WALLET",
                    **payload,
                )

    else:
        # user_wallet_instance = UserWallet.objects.create(
        #     user=user_profile, wallet_tag="WEB"
        # )

        # user_wallet_instance.withdrawable_available_balance = amount
        # user_wallet_instance.transaction_from = trans_from
        # user_wallet_instance.save()

        pass

    return {"status": "success", "message": "User wallet created"}


@shared_task
def ussd_payout(user_id, amount, bank, account_number):
    user_profile = UserProfile.objects.filter(id=user_id).last()

    if ConstantVariable.get_constant_variable().get("payout_source") == "WOVEN":
        return PayoutTransactionTable().user_woven_disbursement(
            amount=amount,
            user_profile_instance=user_profile,
            user_id=user_id,
            bank_name=bank,
            account_number=account_number,
        )

    elif (
        ConstantVariable.get_constant_variable().get("payout_source") == "VFD"
        or ConstantVariable.get_constant_variable().get("payout_source") == "BUDDY"
    ):
        payout_response = PayoutTransactionTable().user_vfd_payout(
            amount=amount,
            user_profile_instance=user_profile,
            user_id=user_id,
            bank_name=bank,
            account_number=account_number,
        )

        return {
            "status": "success",
            "message": "Payout successful",
            "data": payout_response,
        }


@shared_task
def ussd_lottery_payment(
    phone,
    stake_amount,
    bank_code,
    pontential_winning,
    lottery_type,
    game_play_id,
    from_ussd_web=False,
):
    if stake_amount is None:
        return None

    user_phone = LotteryModel.format_number_from_back_add_234(phone)
    user_profile = UserProfile.objects.filter(phone_number=user_phone).last()

    # get user wallet
    user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()

    if user_wallet is None:
        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

    # game type
    game_type = "LOTTERY"
    if lottery_type == "SOCCER_CASH":
        game_type = "SOCCER"

    if user_wallet is None and from_ussd_web is False:
        create_collection_details_for_lottery_player.delay(
            user_phone,
            stake_amount,
            bank_code,
            pontential_winning,
            lottery_type,
            game_play_id,
        )
        # create_collection_details_for_lottery_player.apply_async(
        #     queue="celery1",
        #     args=[
        #         user_phone,
        #         stake_amount,
        #         bank_code,
        #         pontential_winning,
        #         lottery_type,
        #         game_play_id,
        #     ],
        # )

        return {
            "status": "success",
            "message": "User wallet collection detail initiated",
        }

    else:
        if user_wallet.game_available_balance < 50 and from_ussd_web is False:
            # create_collection_details_for_lottery_player.apply_async(
            #     queue="celery1",
            #     args=[
            #         user_phone,
            #         stake_amount,
            #         bank_code,
            #         pontential_winning,
            #         lottery_type,
            #         game_play_id,
            #     ],
            # )
            create_collection_details_for_lottery_player(
                user_phone,
                stake_amount,
                bank_code,
                pontential_winning,
                lottery_type,
                game_play_id,
            )

            return {
                "status": "success",
                "message": "User wallet collection detail initiated",
            }

        elif float(stake_amount) > user_wallet.game_available_balance and from_ussd_web is False:
            # get difference in amount
            amount_to_pay = float(stake_amount) - user_wallet.game_available_balance

            UssdLotteryPayment.objects.create(
                user=user_profile,
                amount=stake_amount,
                game_play_id=game_play_id,
                game_type=game_type,
                lottery_type=lottery_type,
            )

            # create_collection_details_for_ussd_wallet_top_up.apply_async(
            #     queue="celery1",
            #     args=[
            #         user_phone,
            #         amount_to_pay,
            #         bank_code,
            #         lottery_type,
            #     ],
            # )
            create_collection_details_for_ussd_wallet_top_up.delay(
                user_phone,
                amount_to_pay,
                bank_code,
                lottery_type,
            )

        else:
            UssdLotteryPayment.objects.create(
                user=user_profile,
                amount=stake_amount,
                game_play_id=game_play_id,
                game_type=game_type,
                lottery_type=lottery_type,
            )

            sleep(2)

            if lottery_type == "SOCCER_CASH":
                pass
            elif lottery_type == "SALARY_FOR_LIFE":
                pass
            elif lottery_type == "INSTANT_CASHOUT":
                pass
            elif lottery_type == "WYSE_CASH":
                pass
            elif lottery_type == "PREDICTION_SUBSCRIPTION":
                pass

            # user_wallet.game_available_balance -= float(stake_amount)
            # user_wallet.transaction_from = trans_from
            # user_wallet.save()

            share_ussd_payment_across_lottery_pool(
                user_profile.phone_number,
                int(stake_amount),
                game_play_id,
                from_web=True,
            ),

            if lottery_type == "SALARY_FOR_LIFE" or lottery_type == "INSTANT_CASHOUT":
                # Send Whatsapp Notification
                lotto_qs = LottoTicket.objects.filter(game_play_id=game_play_id)

                if lotto_qs.exists():
                    lotto_qs = lotto_qs.last()

                    celery_send_whatsapp_payment_notification_admin.delay(
                        phone_number=user_profile.phone_number,
                        batch_id=lotto_qs.batch.batch_uuid,
                        amount=int(stake_amount),
                        paid_via="WALLET",
                    )

            elif lottery_type == "WYSE_CASH":
                lotto_qs = LotteryModel.objects.filter(game_play_id=game_play_id).last()

                if lotto_qs:
                    celery_send_whatsapp_payment_notification_admin.delay(
                        phone_number=user_profile.phone_number,
                        batch_id=lotto_qs.batch.batch_uuid,
                        amount=int(stake_amount),
                        paid_via="WALLET",
                    )

            elif lottery_type == "SOCCER_CASH":
                celery_send_whatsapp_payment_notification_admin_for_soccer_payment.delay(
                    phone_number=user_profile.phone_number,
                    amount=int(stake_amount),
                    paid_via="WALLET",
                )

            elif lottery_type == "PREDICTION_SUBSCRIPTION":
                celery_send_whatsapp_payment_notification_admin_for_soccer_payment.delay(
                    phone_number=user_profile.phone_number,
                    amount=int(stake_amount),
                    paid_via="WALLET",
                )


@shared_task
def create_collection_details_for_ussd_wallet_top_up(phone, amount, bank_code, game_type):
    from main.models import (
        ConstantVariable,
        PaymentCollectionDetail,
        UserProfile,
        WovenAccountDetail,
    )
    from wallet_app.models import UserWallet
    from wyse_ussd.models import CoralpayTransactions, CoralpayUserCode, UssdPayment

    lottery_player = UserProfile.objects.filter(phone_number=phone).last()

    # Watupay, coralpay or redbiller
    if ConstantVariable.get_constant_variable().get("ussd_bank_payment_method") == "WATU_PAY":
        ussd_payment_code = generate_ussd_collection_code(bank_code=bank_code, amount=amount)
    elif ConstantVariable.get_constant_variable().get("ussd_bank_payment_method") == "CORAL_PAY":  # coralpay
        coral_pay_user_code = CoralpayUserCode.objects.create(user=lottery_player)
        CoralpayTransactions.objects.create(
            ussd_code_extension=coral_pay_user_code,
            amount=amount,
            customer_name=lottery_player.first_name,
            customer_phone=lottery_player.phone_number,
        )

        ussd_payment_code = (
            f"*966*000*{ConstantVariable.get_constant_variable().get('coralpay_biller_code')}+{coral_pay_user_code.code}+{int(amount)}#"
        )

    elif ConstantVariable.get_constant_variable().get("ussd_bank_payment_method") == "REDBILLER":
        ussd_payment_code = UssdPayment.initiate_transaction(
            user=lottery_player,
            amount=amount,
            payment_for="LOTTERY_PAYMENT",
            source="REDBILLER",
        )

    else:
        ussd_payment_code = None

    # Pabbly
    check_out_gateway = create_pabbly_plan(phone=phone, amount=amount)
    print(check_out_gateway)
    if check_out_gateway is not None:
        pabbly_plan_id = check_out_gateway["plan_id"]
        pabbly_link = check_out_gateway["plan_checkout_link"]
        # check_bitly_checkout_link = pabbly_link
        check_bitly_checkout_link = link_shortener(pabbly_link)
        print(check_bitly_checkout_link)

        if check_bitly_checkout_link is None:
            bitly_checkout_link = pabbly_link
        else:
            bitly_checkout_link = check_bitly_checkout_link

    else:
        pabbly_link = None
        pabbly_plan_id = None
        bitly_checkout_link = None

    collection_detail = PaymentCollectionDetail.objects.create(
        lottery_player=lottery_player,
        ussd_code=ussd_payment_code,
        pabbly_link=pabbly_link,
        pabbly_plan_id=pabbly_plan_id,
        bitly_checkout_link=bitly_checkout_link,
    )

    # Woven
    # get_old_woven = WovenAccountDetail.objects.filter(
    #     Q(phone_number=phone) & Q(is_active=True) & Q(wallet_tag="USSD")
    # ).last()
    # if get_old_woven:
    #     # check if accout details exist
    #     woven_account_number = get_old_woven.vnuban
    #     woven_bank_name = get_old_woven.bank_name
    #     woven_account_name = get_old_woven.acct_name
    #     woven_account_reference = get_old_woven.account_ref

    # else:
    #     check_woven_account = generate_woven_collection_account_number(
    #         phone_number=phone, func_count=3, tag="USSD"
    #     )
    #     if check_woven_account is not None:
    #         woven_account_number = check_woven_account["vnuban"]
    #         woven_bank_name = check_woven_account["bank_name"]

    #         # Use this to fill model
    #         woven_account_name = check_woven_account["account_name"]
    #         woven_account_reference = check_woven_account["account_reference"]
    #         woven_payload = check_woven_account["woven_payload"]

    #     else:
    #         woven_account_number = None
    #         woven_bank_name = None
    #         woven_account_name = None
    #         woven_account_reference = None

    woven_account_number = None
    woven_bank_name = None
    woven_account_name = None
    woven_account_reference = None

    _woven = WovenAccountDetail.objects.filter(Q(phone_number=phone) & Q(is_active=True) & Q(wallet_tag="USSD")).last()

    # update or create user_wallet
    user_profile = UserProfile.objects.filter(phone_number=phone).last()
    user_wallet = UserWallet.objects.filter(user__id=user_profile.id, wallet_tag="USSD").last()

    if user_wallet:
        user_wallet.woven_account = _woven
        user_wallet.account_ref = _woven.account_ref
        user_wallet.save()

    else:
        UserWallet.objects.create(
            user=user_profile,
            woven_account=_woven,
            account_ref=_woven.account_ref,
            wallet_tag="USSD",
        )

    # Update Collection Details
    collection_detail.vnuban = woven_account_number
    collection_detail.bank_name = woven_bank_name
    collection_detail.acct_name = woven_account_name
    collection_detail.woven_account_ref = woven_account_reference
    collection_detail.save()

    send_sms_for_ussd_topup(
        phone,
        amount,
        game_type,
        ussd_payment_code,
        woven_account_number,
        woven_bank_name,
        bitly_checkout_link,
    )


@shared_task
def celery_telco_ussd_backgroud(**kwargs):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    broad_base_helper = BBCTelcoAggregator()
    return broad_base_helper.send_ussd_message(**kwargs)


@shared_task
def celery_telco_airtime_charge(**kwargs):
    print("PROCESSING AIRTIME CHARGE")
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from main.models import UserProfile
    from wallet_app.models import UserWallet

    broad_base_helper = BBCTelcoAggregator()

    is_telco_subscription = kwargs.get("is_telco_subscription", False)

    if is_telco_subscription is True:
        if kwargs.get("channel") is None:
            kwargs["channel"] = "USSD"

        print("TELCO SUBSCRIPTION CHARGE")
        res = broad_base_helper.telco_airtime_subscription_activation(**kwargs)

        return res

    phone = kwargs.get("phone_number")

    user_phone = LotteryModel.format_number_from_back_add_234(phone)
    user_profile = UserProfile.objects.filter(phone_number=user_phone).last()
    if user_profile is None:
        user_profile = UserProfile.objects.create(phone_number=user_phone, from_telco=True)

    user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
    if user_wallet is None:
        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

    charge_iteration = kwargs.get("charge_iteration")

    if charge_iteration is None:
        charge_iteration = 1

    for i in range(charge_iteration):
        kwargs["use_json_format"] = True
        kwargs["channel"] = "USSD"
        airtime_charge_response = broad_base_helper.telco_airtime_charge(**kwargs)

    return airtime_charge_response


def handle_on_demand_airtime_payment(request_id_index_position, **kwargs):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    from_new_data_sync_implementation = kwargs.get("from_new_data_sync_implementation", False)

    BBCTelcoAggregator()

    amount_charged = 0
    successfully_charged = False

    if from_new_data_sync_implementation is False:
        item = kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns1:syncOrderRelation", {}).get("ns1:extensionInfo", {}).get("item")

        phone_number = (
            kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns1:syncOrderRelation", {}).get("ns1:userID", {}).get("ID", None)
        )

        # try:
        #     with open("on-demand_datasync.txt", "a") as file:
        #         file.write(f"{kwargs}\n\n")
        # except Exception:
        #     pass

        for i in item:
            if i.get("key") == "chargeResultCode":
                if i.get("value") == "0":
                    successfully_charged = True

            if i.get("key") == "chargeAmount":
                amount_charged = int(i.get("value")) / 100

            if i.get("key") == "soapenv:Fault":
                successfully_charged = False
                amount_charged = 0

        effective_time = str(datetime.now())
    else:
        service_id = kwargs.get("serviceId")
        product_id = kwargs.get("requestedPlan")
        phone_number = kwargs.get("callingParty")
        update_desc = kwargs.get("chargingMode")
        update_time = kwargs.get("processingTime")
        effective_time = kwargs.get("processingTime")

        if update_desc == "S":
            update_desc = "Addition"

        elif update_desc == "U":
            update_desc = "Deletion"

        elif update_desc == "D":
            update_desc = "Modification"

        if kwargs.get("resultCode") == "0":
            successfully_charged = True

        amount_charged = float(kwargs.get("chargeAmount"))

        if amount_charged > 0:
            successfully_charged = True

        if kwargs.get("chargingMode") != "S":
            subscription_type = "ONE_TIME"

        if "insufficient" in kwargs.get("result") or "balance" in kwargs.get("result"):
            # if amount_charged > 0:
            successfully_charged = False

        if "deactivate" in kwargs.get("result") or "service" in kwargs.get("result"):
            successfully_charged = False
            update_desc = "Deletion"

    effective_time_and_phone = f"{effective_time}{phone_number}"
    try:
        TelcoSubscriptionPlan.objects.get(phone_number="phone_number", effective_time_and_phone=effective_time_and_phone)

        return {"message": "this request has been recorded before"}
    except TelcoSubscriptionPlan.DoesNotExist:
        pass

    if successfully_charged is False:
        return {"message": "Charge was not successful"}

    TelcoSubscriptionPlan.update_subscription_interval(
        phone_number=phone_number,
        service_id=service_id,
        product_id=product_id,
        amount=amount_charged,
        network_provider="MTN",
        subscription_type=subscription_type,
        response_payload=kwargs,
        data_sync_verified=True,
        update_time=update_time,
        effective_time=effective_time,
        effective_time_and_phone=effective_time_and_phone,
    )

    lottery_type = telco_subscription_service_id_game_identification(product_id=product_id)

    match_and_play_telco_subscription_ticket(
        lottery_type=lottery_type,
        phone_number=phone_number,
        amount=amount_charged,
        network="MTN",
        product_id=product_id,
    )

    return


def match_and_play_telco_subscription_ticket(
    lottery_type,
    phone_number,
    amount,
    is_a_new_subscription=True,
    network="MTN",
    product_id=None,
):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from sport_app.models import SoccerOddPredictionTable
    from wyse_ussd.subscription_game_plays import (
        awoof_telco_subscription_game_play,
        instant_cashout_telco_subscription_game_play,
        salary_for_life_telco_subscription_game_play,
        soccer_cash_telco_subscription_game_play,
        wyse_cash_telco_subscription_game_play,
    )

    print("lottery_type", lottery_type)

    if lottery_type == "SALARY_FOR_LIFE":
        # print("SALARY_FOR_LIFE")
        salary_for_life_telco_subscription_game_play(
            amount=amount,
            phone_number=phone_number,
            is_a_new_subscription=is_a_new_subscription,
            network=network,
            product_id=product_id,
        )

        return

    if lottery_type == "WYSE_CASH":
        wyse_cash_telco_subscription_game_play(amount=amount, phone_number=phone_number, network=network)

        return

    if lottery_type == "INSTANT_CASH":
        instant_cashout_telco_subscription_game_play(
            amount=amount,
            phone_number=phone_number,
            is_a_new_subscription=is_a_new_subscription,
            network=network,
            product_id=product_id,
        )

        return

    if lottery_type == "SOCCER_CASH":
        if is_a_new_subscription is True:
            soccer_cash_telco_subscription_game_play(
                amount=amount,
                phone_number=phone_number,
                is_a_new_subscription=is_a_new_subscription,
                network=network,
            )
        else:
            SoccerOddPredictionTable.send_prediction_to_telco_subscriber(phone_number=phone_number)

        return

    if lottery_type == "ASK_AI":
        awoof_telco_subscription_game_play(
            amount=amount,
            phone_number=phone_number,
            is_a_new_subscription=is_a_new_subscription,
            network=network,
            product_id=product_id,
            service_type="ASK_AI",
        )

        PendingAsyncTask.objects.create(
            purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
            phone_number=phone_number,
            game_type="ASK_AI",
            is_a_new_subscription=is_a_new_subscription,
            network=network,
            product_id=product_id,
        )
        product_amount = 0
        service_id = None
        if product_id is not None:
            soccer_cash_game_details = secure_d_and_upstream_service_and_prodct_details(product_id=product_id)
            if soccer_cash_game_details is not None:
                product_amount = soccer_cash_game_details.get("amount")
                service_id = soccer_cash_game_details.get("service_id")

        SuccessfullyChargeSoccerPredictionRequestLogs.objects.create(
            phone_number=phone_number,
            amount=product_amount,
            amount_paid=amount,
            successfully_charged=True,
            service_id=service_id,
            product_id=product_id,
        )

        SoccerOddPredictionTable.send_prediction_to_telco_subscriber(phone_number=phone_number)

        # if OpenAiDataDump.last_user_chat_has_passed_24_hours(phone_number=phone_number) is True:

        payload = {
            "phone_number": phone_number,
        }
        if network == "MTN":
            pass
        else:
            payload["service_id"] = "0017182000003867"

        payload["use_json_format"] = True

        ask_ai_sms_response = BBCTelcoAggregator().ask_ai_first_prompt_sms(**payload)
        print("ask_ai_sms_response", ask_ai_sms_response, "\n\n\n")

        return {"message": "ask ai sms sent"}

        # return {"message": "ask ai sms not sent"}

        # type_of_request_instance = SoccerPredictionRequestLogs.objects.filter(
        #     phone_number = phone_number, treated= False
        # ).last()
        # if type_of_request_instance is None:
        #     if is_a_new_subscription is True:
        #         soccer_cash_telco_subscription_game_play(
        #             amount=amount,
        #             phone_number=phone_number,
        #             is_a_new_subscription=is_a_new_subscription,
        #             network=network,
        #         )
        #     else:
        #         SoccerOddPredictionTable.send_prediction_to_telco_subscriber(phone_number=phone_number)

        #     return
        # else:
        #     if type_of_request_instance.type_of_request == "SOCCER_CASH":
        #         soccer_prediction_instance = SoccerPrediction.objects.filter(phone = phone_number, paid = False).last()
        #         if soccer_prediction_instance is None:
        #             pass
        #         else:
        #             soccer_prediction_instance.paid = True
        #             soccer_prediction_instance.save()
        #     else:
        #         if is_a_new_subscription is True:
        #             soccer_cash_telco_subscription_game_play(
        #                 amount=amount,
        #                 phone_number=phone_number,
        #                 is_a_new_subscription=is_a_new_subscription,
        #                 network=network,
        #             )
        #         else:
        #             SoccerOddPredictionTable.send_prediction_to_telco_subscriber(phone_number=phone_number)

        #         return

    # if lottery_type == "SOCCER_CASH":

    #     awoof_telco_subscription_game_play_res = awoof_telco_subscription_game_play(
    #         amount=amount,
    #         phone_number=phone_number,
    #         is_a_new_subscription=is_a_new_subscription,
    #         network=network,
    #         product_id=product_id,
    #         service_type="ASK_AI",
    #     )

    #     print("awoof_telco_subscription_game_play_res", awoof_telco_subscription_game_play_res)

    #     try:
    #         AskAiData.add_or_update_ask_ai_data(phone_number=phone_number)
    #     except Exception:
    #         pass

    #     if OpenAiDataDump.last_user_chat_has_passed_24_hours(phone_number=phone_number) is True:

    #         payload = {
    #             "phone_number": phone_number,
    #         }
    #         if network == "MTN":
    #             pass
    #         else:
    #             payload["service_id"] = "0017182000003867"

    #         ask_ai_sms_response = BBCTelcoAggregator().ask_ai_first_prompt_sms(**payload)
    #         print("ask_ai_sms_response", ask_ai_sms_response, "\n\n\n")

    #         return {"message": "ask ai sms sent"}

    #     return {"message": "ask ai sms not sent"}

    if lottery_type == "AWOOF":
        awoof_telco_subscription_game_play(
            amount=amount,
            phone_number=phone_number,
            is_a_new_subscription=is_a_new_subscription,
            network=network,
            product_id=product_id,
        )

        return

    if lottery_type == "MONETIZE_AI":
        MonetizeAiSubscription.update_record(phone_number=phone_number, amount_paid=amount)

        PendingAsyncTask.objects.create(
            purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
            phone_number=phone_number,
            game_type="MONETIZE_AI",
            is_a_new_subscription=is_a_new_subscription,
            network=network,
            product_id=product_id,
        )

        return

    if lottery_type == "LIBERTY_LIFE":
        TelcoLibertyLifeSubscription.create_record(phone_number=phone_number, amount_paid=amount)

        PendingAsyncTask.objects.create(
            purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
            phone_number=phone_number,
            game_type="LIBERTY_LIFE",
            is_a_new_subscription=is_a_new_subscription,
            network=network,
            product_id=product_id,
        )

        return

    if lottery_type is None:
        return


@time_execution_decorator
def update_telco_charge_table(phone, amount, status="SUCCESSFUL"):
    _telco_charge_instance = TelcoCharge.objects.filter(phone_number=phone, status="PENDING", charge_type="SUBSCRIPTION", amount=amount).last()

    if _telco_charge_instance is not None:
        _telco_charge_instance.status = status
        _telco_charge_instance.sms_sent = True
        _telco_charge_instance.save()


@time_execution_decorator
def handle_airtime_subscription_payment(ip=None, **kwargs):
    import time

    start_time = time.time()
    from_new_data_sync_implementation = kwargs.get("from_new_data_sync_implementation", False)

    from wyse_ussd.models import TelcoGameWebSubscribers, TelcoUsers

    if from_new_data_sync_implementation is False:
        # Step 1: Initial imports and setup
        setup_time = time.time() - start_time
        print(f"Step 1 - Imports and setup: {setup_time:.3f} seconds")

        # Step 2: Extract data from kwargs
        step2_start = time.time()
        service_id = kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:serviceID", None)
        product_id = kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:productID", None)
        phone_number = (
            kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:userID", {}).get("ID", None)
        )
        update_desc = kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:updateDesc", None)
        step2_time = time.time() - step2_start
        print(f"Step 2 - Extract kwargs data: {step2_time:.3f} seconds")

        if (service_id is None) or (product_id is None) or (phone_number is None):
            return {"message": "service_id, product_id or phone_number is none"}

        subscription_item = (
            kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:extensionInfo", {}).get("item")
        )

        update_time = kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:updateTime", None)

        # Step 3: Check for duplicate request
        step3_start = time.time()
        effective_time = kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:effectiveTime", None)
    else:
        setup_time = time.time() - start_time
        print(f"Step 1 - Imports and setup: {setup_time:.3f} seconds")

        # Step 2: Extract data from kwargs
        step2_start = time.time()

        service_id = kwargs.get("serviceType")
        product_id = kwargs.get("requestedPlan")
        phone_number = kwargs.get("callingParty")
        update_desc = kwargs.get("chargingMode")
        update_time = kwargs.get("processingTime")
        effective_time = kwargs.get("processingTime")

        if update_desc == "S":
            update_desc = "Addition"

        elif update_desc == "U":
            update_desc = "Deletion"

        elif update_desc == "D":
            update_desc = "Modification"

        step2_time = time.time() - step2_start
        print(f"Step 2 - Extract kwargs data: {step2_time:.3f} seconds")

        # Step 3: Check for duplicate request
        step3_start = time.time()

    ip = ip

    data_sync_verified = False

    subscription_type = "DAILY"

    amount_charged = 0
    successfully_charged = False

    # check if effective time and phone number have been saved before
    effective_time_and_phone = f"{effective_time}{phone_number}"
    try:
        TelcoSubscriptionPlan.objects.get(phone_number="phone_number", effective_time_and_phone=effective_time_and_phone)
        step3_time = time.time() - step3_start
        print(f"Step 3 - Duplicate check: {step3_time:.3f} seconds")
        print(f"EFFECTIVE TIME :::{effective_time}")
        return {"message": "this request has been recorded before"}
    except TelcoSubscriptionPlan.DoesNotExist:
        step3_time = time.time() - step3_start
        print(f"Step 3 - Duplicate check: {step3_time:.3f} seconds")

    # Step 4: Process broad base games and network
    step4_start = time.time()
    broad_base_games = new_secure_d_and_upstream_service_and_prodct_details(product_id=product_id)

    print("broad_base_games", broad_base_games, "\n\n\n\n")
    print("service_id", service_id, "\n\n\n\n")
    print("product_id", product_id, "\n\n\n\n")
    print("phone_number", phone_number, "\n\n\n\n")
    print("update_desc", update_desc, "\n\n\n\n")
    print("update_time", update_time, "\n\n\n\n")
    print("effective_time", effective_time, "\n\n\n\n")
    print("ip", ip, "\n\n\n\n")
    from_glo_network = False

    if not isinstance(broad_base_games, dict):
        broad_base_games = {}

    # telco_network = broad_base_games.get("network", "MTN")

    telco_network = "MTN"

    step4_time = time.time() - step4_start
    print(f"Step 4 - Process broad base games: {step4_time:.3f} seconds")

    # Step 5: Handle GLO network logic
    step5_start = time.time()
    if telco_network == "GLO":
        amount_charged = broad_base_games.get("amount", 0)
        successfully_charged = True
        from_glo_network = True

        if product_id in [
            "1000005251",
            "1000005388",
            "1000005394",
            "1000005399",
            "1000005404",
        ]:
            subscription_type = "ONE_TIME"

        TelcoUsers().update_record(
            phone_number,
            amount_piad=0,
            successful_charge=0,
            total_amount_won=0,
            network="GLO",
            number_of_daily_subscription=0,
            number_of_one_time_subscription=0,
            number_of_daily_renewals=0,
        )

        if update_desc == "Addition":
            if update_time is not None and effective_time is not None:
                if update_time == effective_time:
                    data_sync_verified = True
    else:
        data_sync_verified = True

        if from_new_data_sync_implementation is False:
            for i in subscription_item:
                if i.get("key") == "resultCode" or i.get("key") == "resultCode":
                    if i.get("value") == "0":
                        successfully_charged = True

                if i.get("key") == "chargeAmount":
                    amount_charged = int(float(i.get("value")))

                    if amount_charged > 0 and amount_charged < 50:
                        successfully_charged = True

                if i.get("key") == "autoRenew":
                    if i.get("value") == "N":
                        subscription_type = "ONE_TIME"

            # check if this request is type of insufficient balance request
            for i in subscription_item:
                if i.get("key") == "result":
                    if "insufficient" in i.get("value") or "balance" in i.get("value"):
                        if amount_charged > 0:
                            break

                        successfully_charged = False
                        break
        else:
            subscription_type = "Modification"

            if kwargs.get("resultCode") == "0":
                successfully_charged = True

            amount_charged = float(kwargs.get("chargeAmount"))

            if amount_charged > 0 and amount_charged < 50:
                successfully_charged = True

            if kwargs.get("chargingMode") != "S":
                subscription_type = "ONE_TIME"

            if "insufficient" in kwargs.get("result") or "balance" in kwargs.get("result"):
                if amount_charged < 1:
                    successfully_charged = False

            if "deactivate" in kwargs.get("result") or "service" in kwargs.get("result"):
                successfully_charged = False
                update_desc = "Deletion"

    step5_time = time.time() - step5_start
    print(f"Step 5 - GLO network processing: {step5_time:.3f} seconds")

    # DEACTIVATION FEATURE
    if update_desc == "Deletion":
        step_deactivation_start = time.time()
        TelcoUnsubscriptionRequest.objects.create(phone_number=phone_number, service_id=service_id, network=broad_base_games.get("network", "MTN"))
        # TelcoSubscriptionPlan.deactivate_subscription(
        #     phone_number=phone_number,
        #     service_id=service_id,
        #     network=broad_base_games.get("network", "MTN"),
        #     ip=ip,
        # )
        step_deactivation_end = time.time() - step_deactivation_start
        print(f"Step - Deactivate subscription: {step_deactivation_end:.3f} seconds")
        return {"message": "subscription deactivated"}

    # END OF DEACTIVATION FEATURE

    # Step 6: Process subscription and charges
    step6_start = time.time()
    if successfully_charged is True:
        # Timing for MobidTracker update
        mobid_start = time.time()
        print(f"GOT TO MORBID TRACKER..:: {phone_number}, {telco_network}")
        try:
            MobidTracker.update_record_with_anount_paid(phone=phone_number, amount=float(amount_charged), telco_network=telco_network)
        except Exception:
            pass
        print(f"MobidTracker Update Time: {time.time() - mobid_start:.3f} seconds")

        if amount_charged == 0:
            # Timing for zero amount handling
            zero_amount_start = time.time()
            if service_id in [
                "234102200006964",
            ]:
                pass
            else:
                PendingAsyncTask.objects.create(
                    purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
                    phone_number=phone_number,
                    game_play_id=None,
                    game_type=subscription_type,
                    is_a_new_subscription=False,
                    network="MTN",
                    product_id=product_id,
                    ticket=None,
                    is_a_free_trial=True,
                )

                update_telco_charge_table(phone=phone_number, amount=0, status="FAILED")
                TelcoSubscriptionPlan.create_record(
                    phone_number=phone_number,
                    amount=amount_charged,
                    service_id=service_id,
                    product_id=product_id,
                    subscription_status="FAILED",
                    subscription_type=subscription_type,
                    response_payload=kwargs,
                    ip=ip,
                    data_sync_verified=data_sync_verified,
                    update_time=update_time,
                    effective_time=effective_time,
                    verification_method="UPDATE_N_EFFECTIVE_TIME",
                    effective_time_and_phone=effective_time_and_phone,
                )
                print(f"Zero Amount Processing Time: {time.time() - zero_amount_start:.3f} seconds")
                return {"message": "subscription failed"}

        # Timing for TelcoGameWebSubscribers update
        telco_game_start = time.time()
        try:
            TelcoGameWebSubscribers.update_subscription_status(
                phone_number=phone_number,
                product_id=product_id,
                network_provider="GLO" if from_glo_network is True else "MTN",
            )
        except Exception:
            pass
        print(f"TelcoGameWebSubscribers Update Time: {time.time() - telco_game_start:.3f} seconds")

        # Timing for SMS subscription update
        sms_sub_start = time.time()
        try:
            SubscriptionViaSmsDataDump.update_subscription_status(phone_number=phone_number, amount=amount_charged)
        except Exception as e:
            print("SubscriptionViaSmsDataDump error", e)
        print(f"SMS Subscription Update Time: {time.time() - sms_sub_start:.3f} seconds")

        # Timing for charge table update
        charge_table_start = time.time()
        update_telco_charge_table(phone=phone_number, amount=amount_charged)
        print(f"Charge Table Update Time: {time.time() - charge_table_start:.3f} seconds")

        print("subscription_type", subscription_type, "\n\n")

        if subscription_type == "ONE_TIME":
            # Timing for one-time subscription processing
            one_time_start = time.time()
            telco_subscription_plan_instance = TelcoSubscriptionPlan.create_record(
                phone_number=phone_number,
                amount=amount_charged,
                service_id=service_id,
                product_id=product_id,
                subscription_type="ONE_TIME",
                response_payload=kwargs,
                subscription_status="STOPPED",
                network_provider="GLO" if from_glo_network is True else "MTN",
                ip=ip,
                data_sync_verified=data_sync_verified,
                update_time=update_time,
                effective_time=effective_time,
                verification_method="UPDATE_N_EFFECTIVE_TIME",
                effective_time_and_phone=effective_time_and_phone,
            )

            TelcoUsers().update_record(
                phone_number,
                amount_piad=amount_charged,
                successful_charge=1,
                total_amount_won=0,
                network=telco_network,
                number_of_one_time_subscription=1,
            )

            if telco_subscription_plan_instance is None:
                print(f"One-Time Subscription Processing Time: {time.time() - one_time_start:.3f} seconds")
                return
            else:
                lottery_type = telco_subscription_service_id_game_identification(product_id=product_id)

                if data_sync_verified is True:
                    # Timing for ticket matching and playing
                    ticket_start = time.time()
                    match_and_play_telco_subscription_ticket(
                        lottery_type=lottery_type,
                        phone_number=phone_number,
                        amount=amount_charged,
                        network=telco_subscription_plan_instance.network_provider,
                        product_id=product_id,
                    )
                    print(f"Ticket Matching and Playing Time: {time.time() - ticket_start:.3f} seconds")

            print(f"One-Time Subscription Processing Time: {time.time() - one_time_start:.3f} seconds")
            return {"message": "subscription activated"}

        else:
            if update_desc == "Addition":
                telco_subscription_plan_instance = TelcoSubscriptionPlan.create_record(
                    phone_number=phone_number,
                    amount=amount_charged,
                    service_id=service_id,
                    product_id=product_id,
                    subscription_type=subscription_type,
                    response_payload=kwargs,
                    network_provider="GLO" if from_glo_network is True else "MTN",
                    ip=ip,
                    data_sync_verified=data_sync_verified,
                    update_time=update_time,
                    effective_time=effective_time,
                    verification_method="UPDATE_N_EFFECTIVE_TIME",
                    effective_time_and_phone=effective_time_and_phone,
                )

                TelcoUsers().update_record(
                    phone_number,
                    amount_piad=amount_charged,
                    successful_charge=1,
                    network=telco_network,
                    number_of_daily_subscription=1,
                )

                if telco_subscription_plan_instance is None:
                    return
                elif isinstance(telco_subscription_plan_instance, dict):
                    return
                else:
                    lottery_type = telco_subscription_service_id_game_identification(product_id=product_id)

                    if data_sync_verified is True:
                        match_and_play_telco_subscription_ticket(
                            lottery_type=lottery_type,
                            phone_number=phone_number,
                            amount=amount_charged,
                            network="MTN",
                            product_id=product_id,
                        )

                    return {"message": "subscription activated"}

            elif update_desc == "Modification":
                update_response = TelcoSubscriptionPlan.update_subscription_interval(
                    phone_number=phone_number,
                    service_id=service_id,
                    product_id=product_id,
                    ip=ip,
                    amount=amount_charged,
                    network_provider=telco_network,
                    subscription_type=subscription_type,
                    response_payload=kwargs,
                    data_sync_verified=True,
                    update_time=update_time,
                    effective_time=effective_time,
                    effective_time_and_phone=effective_time_and_phone,
                )

                TelcoUsers().update_record(
                    phone_number,
                    amount_piad=amount_charged,
                    successful_charge=1,
                    network=telco_network,
                    number_of_daily_renewals=1,
                )

                if update_response.get("succeeded") is True:
                    lottery_type = telco_subscription_service_id_game_identification(product_id=product_id)

                    print("lottery_type", lottery_type, "\n\n\n")

                    if update_response.get("message") == "new subscription created":
                        match_and_play_telco_subscription_ticket(
                            lottery_type=lottery_type,
                            phone_number=phone_number,
                            amount=amount_charged,
                            network=telco_network,
                            product_id=product_id,
                        )
                    else:
                        match_and_play_telco_subscription_ticket(
                            lottery_type=lottery_type,
                            phone_number=phone_number,
                            amount=amount_charged,
                            is_a_new_subscription=False,
                            network=telco_network,
                            product_id=product_id,
                        )

                return {"message": "subscription updated"}
    else:
        update_telco_charge_table(phone=phone_number, amount=0, status="FAILED")
        TelcoSubscriptionPlan.create_record(
            phone_number=phone_number,
            amount=amount_charged,
            service_id=service_id,
            product_id=product_id,
            subscription_status="FAILED",
            subscription_type=subscription_type,
            response_payload=kwargs,
            ip=ip,
            data_sync_verified=False,
            update_time=update_time,
            effective_time=effective_time,
            verification_method="UPDATE_N_EFFECTIVE_TIME",
            effective_time_and_phone=effective_time_and_phone,
        )
    step6_time = time.time() - step6_start
    print(f"Step 6 - Process subscription and charges: {step6_time:.3f} seconds")

    total_time = time.time() - start_time
    print(f"Total execution time: {total_time:.3f} seconds")
    step6_time = time.time() - step6_start
    print(f"Step 6 - Process subscription and charges: {step6_time:.3f} seconds")

    total_time = time.time() - start_time
    print(f"Total execution time: {total_time:.3f} seconds")


@time_execution_decorator
def handle_new_airtime_subscription_payment_data(ip=None, **kwargs):
    """
    Handles new airtime subscription payment data by validating, updating records, and managing subscriptions.

    Args:
        ip (str, optional): IP address from where the request originated.
        **kwargs: Dictionary containing subscription details.

    Returns:
        dict: Response message indicating the status of the subscription request.
    """

    from time import time

    from wyse_ussd.models import (
        TelcoGameWebSubscribers,
        TelcoSubscriptionPlan,
        TelcoUnsubscriptionRequest,
        TelcoUsers,
    )

    print("kwargs", kwargs, "\n\n\n\n")

    time()

    # Step 1: Extract data from kwargs
    service_id = kwargs.get("serviceId")
    product_id = kwargs.get("requestedPlan")
    phone_number = kwargs.get("callingParty")
    update_desc = kwargs.get("chargingMode")
    update_time = kwargs.get("processingTime")
    effective_time = kwargs.get("processingTime")
    amount_charged = float(kwargs.get("chargeAmount", 0))
    result = kwargs.get("result", "")

    # Mapping update descriptions
    update_desc_map = {"S": "Addition", "U": "Deletion", "D": "Modification"}
    update_desc = update_desc_map.get(update_desc, "Unknown")

    # Step 2: Check for duplicate request
    effective_time_and_phone = f"{effective_time}{phone_number}"
    if TelcoSubscriptionPlan.objects.filter(phone_number=phone_number, effective_time_and_phone=effective_time_and_phone).exists():
        return {"message": "This request has been recorded before"}

    # Step 3: Retrieve game subscription details
    broad_base_games = new_secure_d_and_upstream_service_and_prodct_details(product_id=product_id) or {}
    telco_network = "MTN"
    successfully_charged = True
    subscription_type = "ONE_TIME" if kwargs.get("chargingMode") != "S" else "DAILY"

    # Step 4: Handle deactivation requests
    if "deactivate" in result or "service" in result:
        successfully_charged = False
        update_desc = "Deletion"

    if update_desc == "Deletion":
        TelcoUnsubscriptionRequest.objects.create(phone_number=phone_number, service_id=service_id, network=broad_base_games.get("network", "MTN"))
        return {"message": "Subscription deactivated"}

    print("successfully_charged", successfully_charged)
    print("update_desc", update_desc)
    print("amount_charged", amount_charged, "\n\n\n")
    # Step 5: Handle successful charge processing
    if successfully_charged:
        print("successfully_charged", successfully_charged)
        try:
            MobidTracker.update_record_with_anount_paid(phone=phone_number, amount=amount_charged, telco_network=telco_network)
        except Exception:
            pass

        if amount_charged == 0:
            PendingAsyncTask.objects.create(
                purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
                phone_number=phone_number,
                is_a_free_trial=True,
            )

            update_telco_charge_table(phone=phone_number, amount=0, status="FAILED")
            TelcoSubscriptionPlan.create_record(
                phone_number=phone_number,
                amount=amount_charged,
                service_id=service_id,
                product_id=product_id,
                subscription_status="FAILED",
                subscription_type=subscription_type,
                response_payload=kwargs,
                ip=ip,
                data_sync_verified=False,
                update_time=update_time,
                effective_time=effective_time,
                verification_method="UPDATE_N_EFFECTIVE_TIME",
                effective_time_and_phone=effective_time_and_phone,
            )

            return {"message": "Subscription failed 1"}

        # Update game subscribers and SMS subscription status
        TelcoGameWebSubscribers.update_subscription_status(phone_number=phone_number, product_id=product_id, network_provider="MTN")
        SubscriptionViaSmsDataDump.update_subscription_status(phone_number=phone_number, amount=amount_charged)
        update_telco_charge_table(phone=phone_number, amount=amount_charged)

        # Handle one-time subscriptions
        if subscription_type == "ONE_TIME":
            telco_subscription_plan_instance = TelcoSubscriptionPlan.create_record(
                phone_number=phone_number,
                amount=amount_charged,
                service_id=service_id,
                product_id=product_id,
                subscription_type="ONE_TIME",
                response_payload=kwargs,
                subscription_status="STOPPED",
                network_provider="MTN",
                ip=ip,
                data_sync_verified=True,
                update_time=update_time,
                effective_time=effective_time,
                verification_method="UPDATE_N_EFFECTIVE_TIME",
                effective_time_and_phone=effective_time_and_phone,
            )
            TelcoUsers().update_record(
                phone_number,
                amount_piad=amount_charged,
                successful_charge=1,
                total_amount_won=0,
                network=telco_network,
                number_of_one_time_subscription=1,
            )
            if telco_subscription_plan_instance:
                match_and_play_telco_subscription_ticket(
                    lottery_type=telco_subscription_service_id_game_identification(product_id=product_id),
                    phone_number=phone_number,
                    amount=amount_charged,
                    network=telco_subscription_plan_instance.network_provider,
                    product_id=product_id,
                )
            return {"message": "Subscription activated"}

        # Handle modifications
        if update_desc == "Modification" or update_desc == "Addition":
            update_response = TelcoSubscriptionPlan.update_subscription_interval(
                phone_number=phone_number,
                service_id=service_id,
                product_id=product_id,
                ip=ip,
                amount=amount_charged,
                network_provider=telco_network,
                subscription_type=subscription_type,
                response_payload=kwargs,
                data_sync_verified=True,
                update_time=update_time,
                effective_time=effective_time,
                effective_time_and_phone=effective_time_and_phone,
            )
            print("update_response", update_response, "\n\n\n")
            if update_response.get("succeeded"):
                lottery_type = telco_subscription_service_id_game_identification(product_id=product_id)
                print("lottery_type", lottery_type, "product_id", product_id)
                match_and_play_telco_subscription_ticket(
                    lottery_type=lottery_type,
                    phone_number=phone_number,
                    amount=amount_charged,
                    network=telco_network,
                    product_id=product_id,
                    is_a_new_subscription=(update_response.get("message") == "new subscription created"),
                )
            return {"message": "Subscription updated"}

    # Handle failed subscriptions
    update_telco_charge_table(phone=phone_number, amount=0, status="FAILED")
    TelcoSubscriptionPlan.create_record(
        phone_number=phone_number,
        amount=amount_charged,
        service_id=service_id,
        product_id=product_id,
        subscription_status="FAILED",
        subscription_type=subscription_type,
        response_payload=kwargs,
        ip=ip,
        data_sync_verified=False,
        update_time=update_time,
        effective_time=effective_time,
        verification_method="UPDATE_N_EFFECTIVE_TIME",
        effective_time_and_phone=effective_time_and_phone,
    )
    return {"message": "Subscription failed 2"}


@shared_task
def celery_teclo_airtime_charge_datasync_handler_copy(ip):
    print("celery_teclo_airtime_charge_datasync_handler_copy", ip, "\n\n\n")


@shared_task
def celery_teclo_airtime_charge_datasync_handler(request_id_index_position, ip=None, **kwargs):
    # item = kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns1:syncOrderRelation", {}).get("ns1:extensionInfo", {}).get("item")

    subscription_item = (
        kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:extensionInfo", {}).get("item")
    )

    service_id = kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:serviceID", {})

    # product_id = kwargs.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:productID", {})

    # print(
    #     f"""
    #         ON-DEMAND TASK DATASYNC
    #         kwargs: {kwargs} \n\n\n
    #         """
    # )
    # if product_id in on_demand_product_ids:

    #     return handle_on_demand_airtime_payment(request_id_index_position, **kwargs)

    if service_id == "Getwysesub":
        # print("\n\nUSING FIRST ENTRY POINT..!!!!\n\n\n")
        return handle_airtime_subscription_payment(ip=ip, **kwargs)

    if isinstance(subscription_item, list) and len(subscription_item):
        # print("\n\nUSING SECOND ENTRY POINT..!!!!\n\n\n")
        # print(kwargs)
        return handle_airtime_subscription_payment(ip=ip, **kwargs)

    # print("\n\nUSING THIRD ENTRY POINT..!!!\n\n\n")
    return handle_on_demand_airtime_payment(request_id_index_position, **kwargs)


@shared_task
def new_celery_teclo_airtime_charge_datasync_handler(ip=None, **kwargs):
    """
    {
        "serviceType": "Broad_Theva_7009",
        "chargingMode": "S",
        "appliedPlan": "23410220000028251",
        "contentId": "NA",
        "resultCode": "0",
        "renFlag": "N",
        "requestNo": "24080915442756648342",
        "processingTime": "240809154427",
        "result": "Success",
        "validityType": "DD",
        "sequenceNo": "1723211060105",
        "callingParty": "2349067074483",
        "bearerId": "USSD",
        "operationId": "SN",
        "requestedPlan": "23410220000028251",
        "chargeAmount": "100.0",
        "serviceNode": "Broadbased Communication",
        "serviceId": "234102200007009",
        "keyword": "",
        "category": "-1",
        "validityDays": "1"
    }
    """

    kwargs.get("serviceType")
    kwargs.get("chargingMode")
    service_id = kwargs.get("serviceId")
    kwargs["from_new_data_sync_implementation"] = True

    if service_id in SUBSCRIPTION_SERVICE_IDS:
        return handle_new_airtime_subscription_payment_data(ip=ip, **kwargs)

    if service_id in ON_DEMAND_SERVICE_IDS:
        return handle_on_demand_airtime_payment(request_id_index_position=1, **kwargs)

    return


@shared_task
def handle_telco_payemnt_receipt(template_id, **kwargs):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from wyse_ussd.models import TelcoConstant

    sms_gateway = TelcoConstant().get_sms_gateway()
    aggregator_helper = BBCTelcoAggregator()

    receiver = kwargs.get("receiver")
    amount = kwargs.get("amount")
    game_type = kwargs.get("game_type")
    game_play_id = kwargs.get("game_play_id")
    next_game = kwargs.get("next_game")
    potential_win = kwargs.get("potential_win")
    ticket_qs = kwargs.get("ticket_qs")

    if sms_gateway == "TELCO":
        _message = kwargs.get("_message")
        if _message is not None:
            sms_payload = {
                "phone_number": receiver,
                "sender_name": "WINWISE",
                "message": _message,
            }

            sms_payload["use_json_format"] = True
            data = {"handle_telco_payment_receipt": aggregator_helper.bbc_send_sms(**sms_payload)}
            print("data:", data)
            return data

    if sms_gateway == "WHISPER_SMS":
        if game_type == "INSTANT_CASHOUT" and kwargs.get("_send") is not None:
            return

        if template_id == "d97f0fd3-4521-44f4-940b-a4ffa8035109":
            sms_payload = {
                "receiver": receiver,
                "template": "d97f0fd3-4521-44f4-940b-a4ffa8035109",
                "place_holders": {
                    "pontential_winning": kwargs.get("pontential_winning"),
                    "ticket_1": kwargs.get("ticket_1"),
                    "ticket_2": kwargs.get("ticket_2"),
                    "game_id_1": kwargs.get("game_id_1"),
                    "game_id_2": kwargs.get("game_id_2"),
                    "week_name": kwargs.get("week_name"),
                    "current_date": kwargs.get("current_date"),
                    "current_time": kwargs.get("current_time"),
                },
            }
            return send_simple_sms_campaign(**sms_payload)

        elif game_type == "AWOOF" or template_id == "985da284-bf9c-435c-a40f-f79a947aaf41":
            sms_payload = {
                "receiver": receiver,
                "template": "5f571040-049f-40f6-841d-07ac084d32b4",
                "place_holders": {
                    "amount": amount,
                },
            }
            return send_simple_sms_campaign(**sms_payload)

        elif kwargs.get("lottery_type") in ["SALARY_FOR_LIFE", "WYSE_CASH"]:
            sms_payload = {
                "receiver": receiver,
                "template": "13decbbd-bedd-4461-b152-35b9e16d8dfe",
                "place_holders": {
                    "amount": amount,
                    "lottery_type": game_type,
                    "potential_win": potential_win,
                    "next_game": next_game,
                },
            }
            return send_simple_sms_campaign(**sms_payload)

        elif kwargs.get("lottery_type") == "INSTANT_CASHOUT":
            user_ticket_picks = " ".join(f"({ticket.ticket})" for ticket in ticket_qs)
            sms_payload = {
                "receiver": receiver,
                "template": "5c25b52d-a95a-42ba-9521-4c80fae0d247",
                "place_holders": {
                    "user_ticket_picks": user_ticket_picks,
                    "game_play_id": game_play_id,
                    "potential_win": potential_win,
                    "next_game": next_game,
                },
            }
            return send_simple_sms_campaign(**sms_payload)

    if game_type == "AWOOF":
        message = f"Hello, We have received your payment of N{amount}. You will be notified if your ticket won.\nJoin us on telegram: https://bit.ly/3Qeuor2"

    elif template_id == "985da284-bf9c-435c-a40f-f79a947aaf41":
        message = f"Hello, We have received your payment of N{amount}. You will be notified if your ticket won.\nJoin us on telegram: https://bit.ly/3Qeuor2"

    elif template_id == "66f8749a-b41f-4afe-92c6-3609f2fd5014":
        message = f"WinWise Payment Received {amount} Summary: {kwargs.get('game')}. Game id: {game_play_id} Win: {potential_win} Next draw: {next_game}\nJoin us on telegram: https://bit.ly/3Qeuor2"

    if game_type == "INSTANT_CASHOUT" and kwargs.get("_send") is not None:
        return

    if message is None:
        data = {"handle_telco_payment_receipt": "No message"}
        print("data:", data)
        return data

    sms_payload = {
        "phone_number": receiver,
        "sender_name": "WINWISE",
        "message": message,
    }
    sms_payload["use_json_format"] = True
    data = {"handle_telco_payment_receipt": aggregator_helper.bbc_send_sms(**sms_payload)}
    print("data:", data)
    return data


@shared_task
def instant_cashout_lost_sms_on_telco(phone, ticket_num, game_play_id, instance_id=0):
    return
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from main.models import LottoTicket
    from prices.game_price import TelcoInstantCashOutPriceModel
    from wyse_ussd.models import TelcoUsers

    redis_db = RedisStorage(redis_key="_celery_sms_for_instant_cash_lost_ticket_telco")
    if redis_db.get_data() is None:
        redis_db_data = ""
    else:
        redis_db_data = redis_db.get_data().decode("utf-8")

    redis_game_ids = str(redis_db_data).split(",")

    if game_play_id in redis_game_ids:
        return "SMS ALREADY SENT"

    redis_game_ids.append(game_play_id)
    redis_db.set_data(",".join(redis_game_ids))

    prefixes = ["070", "080", "081", "090", "091"]

    winners = ""

    for i in range(1, 5):
        random_prefix = random.choice(prefixes)
        suffix_range = range(100, 1000)
        random_suffix = random.choice(suffix_range)
        random_phone_number = f"{random_prefix}**{random_suffix}"

        instant_Cashout_pontential_earning = TelcoInstantCashOutPriceModel().get_pontential_winnings()
        instant_Cashout_pontential_earning_formated = []
        for i in instant_Cashout_pontential_earning:
            amt = int(str(i).replace(",", "").replace(".00", ""))
            instant_Cashout_pontential_earning_formated.append(amt)

        instant_cashout_price_model = [
            400,
            500,
            800,
            1666.0,
            700,
            650,
            1650,
            1000,
            2300,
            3500,
        ]
        instant_cashout_price_model += instant_Cashout_pontential_earning_formated

        random_pontential_winning = random.choice(instant_cashout_price_model)

        winners += f"N{str(currency_formatter(random_pontential_winning)).replace('.00', '')} won by {random_phone_number}\n"

    aggregator_helper = BBCTelcoAggregator()

    # current_time = datetime.now().time()
    # hour = current_time.hour
    # minute = current_time.minute

    now = (datetime.now().time()).strftime("%H:%M")
    current_time = datetime.strptime(str(now), "%H:%M")
    current_time = current_time.strftime("%I:%M %p")

    #

    # message = f"The Winning nos for Instant cash drawn on {datetime.now().date()} {hour}:{minute} are ({ticket_num}).{currency_formatter(random_pontential_winning)} was won by {random_phone_number}, don't miss the next draw. Dial *20144*1*1# or www.wisewinn.com"
    sms_gateway = TelcoConstant().get_sms_gateway()

    # get telco user instance
    try:
        telco_user = TelcoUsers.objects.using("external2").get(phone_number=phone)
    except TelcoUsers.DoesNotExist:
        telco_user = None

    # game instance
    try:
        game_instance = LottoTicket.objects.get(id=instance_id)
    except LottoTicket.DoesNotExist:
        game_instance = None

    if game_instance is not None:
        ticket_num = game_instance.system_generated_num

    if ticket_num is None:
        if game_instance is not None:
            user_tickets = str(game_instance.ticket).split(",")

            ticket_num = []

            while len(ticket_num) < 4:
                # random number from 1 to 39
                random_number = random.randint(1, 39)
                if str(random_number) not in ticket_num:
                    ticket_num.append(random_number)

            if len(ticket_num) == 4:
                ticket_num = ",".join(ticket_num)
            else:
                ticket_num = ",".join(user_tickets)

    if telco_user is not None:
        if telco_user.network == "GLO":
            pass

            message = f"InstantCash {current_time} Draw Today!\nWinning nos: ({ticket_num}).\n"
            message += f"Congrats to our winners:\n{winners}"

            # message += f"Don't miss the next draw! Dail *20144*1*1# or {website_link}"

            message += f"Don't miss the next draw! Dail *20144*1*1# or https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/"  # noqa

            sms_payload = {
                "phone_number": phone,
                # "sender_name": "WINWISE",
                "message": message,
                "service_id": "0017182000003867",
                "sender_name": "20144",
            }

            aggregator_helper.bbc_send_sms(**sms_payload)

            return

    if sms_gateway == "WHISPER_SMS":
        sms_payload = {
            "receiver": phone,
            "template": "aa8163a4-f1e8-460b-b278-038d4c4c22ab",
            "place_holders": {
                "current_time": str(current_time),
                "ticket_num": ticket_num,
                "winners": winners,
            },
        }
        return send_simple_sms_campaign(**sms_payload)

    message = f"InstantCash {current_time} Draw Today!\nWinning nos: ({ticket_num}).\n"
    message += f"Congrats to our winners:\n{winners}"

    # message += f"Don't miss the next draw! Dail *20144*1*1# or {website_link}"

    message += "Don't miss the next draw! Dail *20144*1*1# or https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/"

    sms_payload = {
        "phone_number": phone,
        "sender_name": "WINWISE",
        "message": message,
    }

    data = {"handle_telco_payemnt_receipt": aggregator_helper.bbc_send_sms(**sms_payload)}

    print(
        f"""

          data: {data}
          :::::::::::::::::::::::::

          """
    )

    return data


@shared_task
def salary_for_life_lost_sms_on_telco(phone_number, total_amount_won, num_of_winners, lottery_type, draw_date, winning_no):
    return
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    aggregator_helper = BBCTelcoAggregator()

    message = f"WinWise {Utility.currency_formatter(total_amount_won)} was Won by {num_of_winners} players.The Winning number for {lottery_type} drawn on {draw_date} are {winning_no} Don't miss the next draw. Dial *20144# https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/"

    sms_payload = {
        "phone_number": phone_number,
        "sender_name": "WINWISE",
        "message": message,
    }

    data = {"handle_telco salary for life lost sms": aggregator_helper.bbc_send_sms(**sms_payload)}

    print(
        f"""
        data: {data}
        """
    )

    return data


@shared_task
def wyse_cash_lost_sms_on_telco(phone_number, total_amount_won, num_of_winners, lottery_type, draw_date):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    return

    aggregator_helper = BBCTelcoAggregator()

    message = f"WinWise {Utility.currency_formatter(total_amount_won)} was Won by {num_of_winners} players for {lottery_type} drawn on {draw_date} . https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/ "

    sms_payload = {
        "phone_number": phone_number,
        "sender_name": "WINWISE",
        "message": message,
    }

    data = {"handle_telco salary for life lost sms": aggregator_helper.bbc_send_sms(**sms_payload)}

    print(
        f"""
        data: {data}
        """
    )

    return data


@shared_task
def instant_cashout_won_sms_on_telco(phone, ticket_num, amount, game_play_id):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from main.models import LottoTicket
    from wallet_app.models import UserWallet

    aggregator_helper = BBCTelcoAggregator()

    redis_db = RedisStorage(redis_key="_celery_sms_for_instant_cash_winners_telco")

    redis_db_data = redis_db.get_data()

    if redis_db_data is None or redis_db_data == "":
        redis_db_data = ""
    else:
        redis_db_data = redis_db_data.decode("utf-8")

    redis_game_ids = str(redis_db_data).split(",")

    if game_play_id in redis_game_ids:
        return "SMS ALREADY SENT"

    redis_game_ids.append(game_play_id)
    redis_db.set_data(",".join(redis_game_ids))

    lotto_ticket_instance = LottoTicket.objects.filter(game_play_id=game_play_id).last()
    if lotto_ticket_instance is None:
        pass
    else:
        ticket_num = lotto_ticket_instance.system_generated_num

    current_time = datetime.now().time()
    hour = current_time.hour
    minute = current_time.minute

    user_wallet = UserWallet.objects.filter(user__phone_number=phone, wallet_tag="WEB").last()

    sms_gateway = TelcoConstant().get_sms_gateway()

    if sms_gateway == "WHISPER_SMS":
        sms_payload = {
            "receiver": phone,
            "template": "3a985052-008a-4047-9770-26578ea6f9d7",
            "place_holders": {
                "amount_won": str(current_time),
                "ticket_num": ticket_num,
                "draw_date": f"{str(datetime.now().date()).replace('/', '-')} {hour}:{minute}",
                "wallet_balance": (user_wallet.withdrawable_available_balance if user_wallet else 0),
            },
        }
        return send_simple_sms_campaign(**sms_payload)

    if user_wallet is None:
        # message = f"Cr:{currency_formatter(amount)} You Won!\nThe win numbers for Instant Cash drawn on {datetime.now().date()} {hour}:{minute} are ({ticket_num}). Don't miss the next draw.\nDial *20144*8# for payout."
        message = f"You've Won {currency_formatter(amount)}! Winning Nos: ({ticket_num}) Game: INSTANT CASH Draw Date: {datetime.now().date()} {hour}:{minute} Dial 201448# for payout."
    else:
        # message = f"Cr:{currency_formatter(amount)} You Won!\nThe win numbers for Instant Cash drawn on {datetime.now().date()} {hour}:{minute} are ({ticket_num}) Bal. N{currency_formatter(user_wallet.withdrawable_available_balance)} \nDial *20144*8# for payout."
        # message = f"You've Won {currency_formatter(amount)}! Winning Nos: ({ticket_num}) Game: INSTANT CASH Draw Date: {datetime.now().date()} {hour}:{minute} Balance: N{currency_formatter(user_wallet.withdrawable_available_balance)} Dial 201448# for payout."
        # message = f"You've Won N{str(currency_formatter(amount)).replace('.00', '')}!\nWinning Nos: ({ticket_num})\nGame: INSTANT CASH\nDraw Date: {str(datetime.now().date()).replace('/', '-')} {hour}:{minute}\nBalance: N{currency_formatter(user_wallet.withdrawable_available_balance)}\nDial *20144*8# for payout."

        message = f"You've Won N{currency_formatter(amount)} Cashback!\nWinning Nos: ({ticket_num})\nGame: INSTANT CASH\nDraw Date: {datetime.today().date()} {datetime.today().time()}\nBalance: N{currency_formatter(user_wallet.withdrawable_available_balance)}\nDial *20144*8# for payout."

    sms_payload = {
        "phone_number": phone,
        "sender_name": "WINWISE",
        "message": message,
    }

    sms_payload["use_json_format"] = True
    data = {"handle_telco_payemnt_receipt": aggregator_helper.bbc_send_sms(**sms_payload)}

    print(
        f"""

          data: {data}
          :::::::::::::::::::::::::

          """
    )

    return data


@shared_task
def salary_for_life_and_instant_cashout_won_sms_on_telco(phone, ticket_num, amount, game_play_id, lottery_type, network_type="MTN"):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from wallet_app.models import UserWallet
    from wyse_ussd.models import TelcoUsers

    current_time = datetime.now().time()
    hour = current_time.hour
    minute = current_time.minute

    user_wallet = UserWallet.objects.filter(user__phone_number=phone, wallet_tag="WEB").last()

    user_wallet_bal = user_wallet.withdrawable_available_balance if user_wallet else 0

    message = f"You've Won N{str(currency_formatter(amount)).replace('.00', '')}!\nWinning Nos: ({ticket_num})\nGame: {lottery_type}\nDraw Date: {str(datetime.now().date()).replace('/', '-')} {hour}:{minute}\nBalance: N{currency_formatter(user_wallet_bal)}\nDial *20144*8# for payout."

    if network_type == "GLO":
        try:
            return nitroswitch_sms_content_delivery(phone_number=phone, message=message)
        except Exception:
            pass

    try:
        _telco_user_instance = TelcoUsers.objects.using("external2").get(phone_number=phone)

        if _telco_user_instance.network == "GLO":
            message = f"You've Won!\n Ready to cash your winnings? It's easy! Just dial *20144*6*3# to withdraw your rewards directly.Don't let your winnings wait. Stay Subscribed to Glo {lottery_type}. https://www.glomegawin.com/salaryforlife"
    except Exception:
        pass

    aggregator_helper = BBCTelcoAggregator()

    sms_payload = {
        "phone_number": phone,
        "sender_name": "WINWISE",
        "message": message,
    }

    sms_payload["use_json_format"] = True
    data = {"salary_for_life_and_instant_cashout_won_sms_on_telco": aggregator_helper.bbc_send_sms(**sms_payload)}

    print(
        f"""

          data: {data}
          :::::::::::::::::::::::::

          """
    )

    return data


@shared_task
def celery_update_telco_session(user_phone, session_id, network="MTN"):
    from wyse_ussd.models import TelcoUsers

    TelcoUsers().update_session_dails(phone_number=user_phone, session_id=session_id, network=network)

    return {"message": "Telco session updated"}


@shared_task
def celery_update_telco_record(
    phone_number,
    amount_piad=0,
    successful_charge=0,
    total_amount_won=0,
    network="MTN",
    number_of_daily_subscription=0,
    number_of_one_time_subscription=0,
    number_of_daily_renewals=0,
):
    from wyse_ussd.models import TelcoUsers

    TelcoUsers().update_record(
        phone_number=phone_number,
        amount_piad=amount_piad,
        successful_charge=successful_charge,
        total_amount_won=total_amount_won,
        network=network,
        number_of_daily_subscription=number_of_daily_subscription,
        number_of_one_time_subscription=number_of_one_time_subscription,
        number_of_daily_renewals=number_of_daily_renewals,
    )

    return {"message": "Telco record updated"}


@shared_task
def celery_update_teclo_users_winning_withdrawal(phone_number):
    from wyse_ussd.models import TelcoUsers

    TelcoUsers().update_winning_withdrawal(phone_number=phone_number)

    return {"message": "Telco record updated 2"}


@shared_task
def celery_send_bank_details_to_telco_pending_charges():
    current_time = datetime.now()
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from wyse_ussd.models import TelcoConstant

    if TelcoConstant().get_collect_bank_details() is False:
        return {"message": "collect bank details is false"}

    broad_base_helper = BBCTelcoAggregator()

    time_range_start = current_time - timedelta(minutes=5)
    time_range_end = current_time - timedelta(minutes=2)

    telco_pending_charges = TelcoCharge.objects.filter(
        status="PENDING",
        created_at__range=(time_range_start, time_range_end),
        sms_sent=False,
    )

    if telco_pending_charges:
        for telco_charge in telco_pending_charges:
            user_profile = UserProfile.objects.filter(phone_number=telco_charge.phone_number).last()
            if user_profile is not None:
                ussd_payment_code = redbiller_ussd_payment_code(
                    user_profile_instance=user_profile,
                    amount=telco_charge.amount,
                )

                if ussd_payment_code is None:
                    continue

                # sms_payload = {
                #     "phone_number": user_profile.phone_number,
                #     "sender_name": "WINWISE",
                #     "message": f"Ready to Play? Dial {ussd_payment_code} to fund your wallet or play with airtime by dialling *20144#. Win Big in the next draw! Join the lucky winners today",
                # }

                sms_payload = {
                    "phone_number": user_profile.phone_number,
                    # "sender_name": "WINWISE",
                    "sender_name": "20144",
                    "message": "Complete your WinWise subscription,dial *305*3# to register your ticket",
                }

                sms_payload["use_json_format"] = True
                broad_base_helper.bbc_send_sms(**sms_payload)

                telco_charge.sms_sent = True
                telco_charge.save()


@shared_task
def celery_secure_d_data_sync(**data):
    from ads_tracker.tasks import celery_update_marketing_campaign_analytics

    trans_ref = data.get("trxId")
    phone_number = data.get("msisdn")
    activation = data.get("activation")
    description = data.get("description")
    productID = data.get("productID")

    get_game_details_object = secure_d_and_upstream_service_and_prodct_details(product_id=productID)
    # print("CALLED celery_secure_d_data_sync", "\n\n\n\n")

    # return

    secure_d_transaction_instance = SecureDTransaction.objects.filter(reference=trans_ref).last()

    if secure_d_transaction_instance is None:
        secure_d_transaction_instance = SecureDTransaction.objects.create(
            phone_number=phone_number,
            reference=trans_ref,
            transaction_status=description,
            subscription_amount=get_game_details_object.get("amount", 0.00),
            game_type=get_game_details_object.get("product_name"),
        )

    elif secure_d_transaction_instance.is_successful is True:
        return {"message": "transaction already successful"}

    if str(description).lower() == "success":
        secure_d_transaction_instance.is_successful = True
        secure_d_transaction_instance.save()

        celery_update_marketing_campaign_analytics.apply_async(
            queue="telco_session_logs",
            kwargs={
                "agency": "SECURE_D",
                "players_phone_number": phone_number,
                "amount_paid": get_game_details_object.get("amount"),
            },
        )

        return {"message": "payment transaction successful"}

        if activation == "1":
            get_game_details_object = secure_d_and_upstream_service_and_prodct_details(product_id=productID)
            if get_game_details_object is None:
                return {"message": "game details not found"}

            is_daily_subscription = get_game_details_object.get("is_daily_subscription", False)
            print("is_daily_subscription", is_daily_subscription, "\n\n\n")
            if is_daily_subscription is True:
                daily_subscription_payload = {
                    "phone_number": phone_number,
                    "amount": get_game_details_object["amount"],
                    "product_id": get_game_details_object.get("product_id"),
                    "service_id": get_game_details_object.get("service_id"),
                    "payload": data,
                }

                celery_handle_secure_d_upstream_telco_subscription(**daily_subscription_payload)

                return

            lottery_type = get_game_details_object.get("product_name")
            if lottery_type == "SALARY_FOR_LIFE":
                lotto = create_wyse_lotto_ticket(
                    phone_number=phone_number,
                    lottery_type="SALARY_FOR_LIFE",
                    jackpot=get_game_details_object.get("line_number", 1),
                    bank="",
                    auto=True,
                    stake_amount=get_game_details_object["amount"],
                    telco=True,
                )

                payment_payload = {
                    "phone_number": phone_number,
                    "amount": get_game_details_object["amount"],
                    "game_play_id": lotto.game_play_id,
                    "lottery_type": "SALARY_FOR_LIFE",
                }
                celery_handle_secure_d_upstream_lottery_payment.delay(**payment_payload)

                return {"message": "salary for life lottery ticket created"}

            elif lottery_type == "INSTANT_CASH":
                stake_amount = get_game_details_object.get("amount")

                if stake_amount == 300:
                    lotto = create_wyse_lotto_ticket(
                        phone_number=phone_number,
                        lottery_type="INSTANT_CASHOUT",
                        jackpot=1,
                        bank="",
                        auto=True,
                        stake_amount=stake_amount,
                        telco=True,
                    )

                _stake_amount = stake_amount
                if _stake_amount == 150:
                    _stake_amount = 300

                lotto = create_wyse_lotto_ticket(
                    phone_number=phone_number,
                    lottery_type="INSTANT_CASHOUT",
                    jackpot=get_game_details_object.get("line_number", 1),
                    bank="",
                    auto=True,
                    stake_amount=_stake_amount,
                    telco=True,
                )

                payment_payload = {
                    "phone_number": phone_number,
                    "amount": get_game_details_object["amount"],
                    "game_play_id": lotto.game_play_id,
                    "lottery_type": "SALARY_FOR_LIFE",
                }
                celery_handle_secure_d_upstream_lottery_payment.delay(**payment_payload)

                return {"message": "salary for life lottery ticket created"}

            # elif lottery_type == "INSTANT_CASH":

    return {"message": "payment transaction not successful"}


@shared_task
def celery_handle_secure_d_upstream_lottery_payment(**kwargs):
    user_phone = kwargs.get("phone_number")

    user_profile = UserProfile.objects.filter(phone_number=user_phone).last()
    if user_profile is None:
        user_profile = UserProfile.objects.create(phone_number=user_phone, from_telco=True)

    user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
    if user_wallet is None:
        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

    game_type = "LOTTERY"

    if kwargs.get("lottery_type") == "SOCCER_CASH":
        game_type = "SOCCER"

    ussd_lottery_payment_instance = UssdLotteryPayment.objects.create(
        user=user_profile,
        amount=kwargs.get("amount"),
        game_play_id=kwargs.get("game_play_id"),
        game_type=game_type,
        lottery_type=kwargs.get("lottery_type"),
        played_via_telco_channel=True,
    )

    ussd_lottery_payment_instance.amount_paid = kwargs.get("amount")
    ussd_lottery_payment_instance.feom_telco_and_playing_from_wallet = False
    ussd_lottery_payment_instance.transfrom = "UPSTREAM_FUNDING"
    ussd_lottery_payment_instance.save()

    return {"message": "played from telco wallet"}


@shared_task
def celery_deactivate_telco_subscription(phone_number, product_id, service_id=None, use_json_format=False):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    BBCTelcoAggregator().telco_airtime_unsubscription_request(
        product_id=product_id, phone=phone_number, service_id=service_id, use_json_format=use_json_format
    )


def telco_sms_subscription_feature(xml_data):
    import string

    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    request_log = TelcoAggregatorNotification.objects.create(response_data_dump=xml_data)

    converted_string = xml_data_formater(xml_data)
    converted_string = json.loads(converted_string)

    request_log.serialized_data_dump = converted_string
    request_log.save()

    """
    SAMPLE RESPONSE:

    {
        "soapenv:Envelope":{
            "@xmlns:soapenv":"http://schemas.xmlsoap.org/soap/envelope/",
            "soapenv:Header":{
                "ns3:NotifySOAPHeader":{
                    "@xmlns:ns3":"http://www.huawei.com.cn/schema/common/v2_1",
                    "ns3:spRevId":"None",
                    "ns3:spRevpassword":"ea74ed6b1bd448ce6cf6640fd54cc3b2",
                    "ns3:spId":"whispasms",
                    "ns3:timeStamp":"20240122120506",
                    "ns3:OperatorID":"1"
                }
            },
            "soapenv:Body":{
                "ns3:notifySmsReception":{
                    "@xmlns:ns3":"http://www.csapi.org/schema/parlayx/sms/notification/v2_2/local",
                    "ns3:correlator":"6182353",
                    "ns3:message":{
                        "message":"ICF",
                        "senderAddress":"2348038705895",
                        "smsServiceActivationNumber":"20144",
                        "dateTime":"2024-01-22T12:05:06.874+01:00"
                    }
                }
            }
        }
    }
    """
    # get sms subscription message
    message = (
        converted_string.get("soapenv:Envelope", {})
        .get("soapenv:Body", {})
        .get("ns3:notifySmsReception", {})
        .get("ns3:message", {})
        .get("message", None)
    )

    phone_number = (
        converted_string.get("soapenv:Envelope", {})
        .get("soapenv:Body", {})
        .get("ns3:notifySmsReception", {})
        .get("ns3:message", {})
        .get("senderAddress", None)
    )

    print(
        f"""
    telco_sms_subscription_feature
    message: {message}
    phone_number: {phone_number}
    \n\n\n\n\n\n\n
    """
    )

    if message is None:
        return {"message": "message is none"}

    subscription_service_codes = {
        "ICF": ["234102200006961", "23410220000027462", "INSTANT_CASHOUT", "50"],
        "ICS": ["234102200006961", "23410220000027463", "INSTANT_CASHOUT", "75"],
        "WCF": ["234102200006965", "23410220000027470", "WYSE_CASH", "50"],
        "WCS": ["234102200006965", "23410220000027471", "WYSE_CASH", "75"],
        "SLF": ["234102200006962", "23410220000027464", "SALARY_FOR_LIFE", "50"],
        "SLS": ["234102200006962", "23410220000027465", "SALARY_FOR_LIFE", "75"],
        "FAF": ["234102200006963", "23410220000027466", "AWOOF", "50"],
        "FAS": ["234102200006963", "23410220000027467", "AWOOF", "75"],
        "SCF": ["234102200006964", "23410220000027468", "SOCCER_CASH", "50"],
        "SCS": ["234102200006964", "23410220000027469", "SOCCER_CASH", "75"],
    }

    stop_subscription_service_codes = {
        "STOP ICF": ["234102200006961", "23410220000027462", "INSTANT_CASHOUT", "50"],
        "STOP ICS": ["234102200006961", "23410220000027463", "INSTANT_CASHOUT", "75"],
        "STOP WCF": ["234102200006965", "23410220000027470", "WYSE_CASH", "50"],
        "STOP WCS": ["234102200006965", "23410220000027471", "WYSE_CASH", "75"],
        "SLF": ["234102200006962", "23410220000027464", "SALARY_FOR_LIFE", "50"],
        "STOP SLF": ["234102200006962", "23410220000027464", "SALARY_FOR_LIFE", "50"],
        "STOP SLS": ["234102200006962", "23410220000027465", "SALARY_FOR_LIFE", "75"],
        "STOP FAF": ["234102200006963", "23410220000027466", "AWOOF", "50"],
        "STOP FAS": ["234102200006963", "23410220000027467", "AWOOF", "75"],
        "STOP SCF": ["234102200006964", "23410220000027468", "SOCCER_CASH", "50"],
        "STOP SCS": ["234102200006964", "23410220000027469", "SOCCER_CASH", "75"],
    }

    capitalize_message = str(message).upper()

    if "STOP" in capitalize_message:
        service_code = stop_subscription_service_codes.get(capitalize_message, None)

        if service_code is None:
            payload = {
                "phone_number": phone_number,
            }

            ask_ai_data_model_instance_chance = AskAiData.get_instance_chance(phone_number=phone_number)
            if ask_ai_data_model_instance_chance is not None:
                if ask_ai_data_model_instance_chance.total_chance > 0:
                    ask_gpt = AwoofGameTable.ask_awoof_open_ai(message, phone_number)
                    ask_gpt_response = ask_gpt.get("message")

                    payload["message"] = f"{ask_gpt_response}"

                    ask_ai_data_model_instance_chance.total_chance -= 1
                    ask_ai_data_model_instance_chance.total_chance_used += 1
                    ask_ai_data_model_instance_chance.total_question_asked += 1
                    ask_ai_data_model_instance_chance.save()

                    payload["use_json_format"] = True
                    BBCTelcoAggregator().ask_ai_first_prompt_sms(**payload)
                else:
                    # CHARGE REQUEST TO TELCO

                    on_demand_airtime_charge_payload = {
                        "service_id": "234102200006772",
                        "product_id": "23410220000024656",
                        "phone_number": phone_number,
                        "description": "soccer cash lottery payment",
                        "amount": 100,
                        "lottery_type": "SOCCER_CASH",
                        "game_play_id": "".join(random.choices(string.ascii_uppercase + string.digits, k=10)),
                        "charge_reason": "ASK_AI",
                        "pontential_winning": 0,
                    }

                    broad_base_helper = BBCTelcoAggregator()

                    on_demand_airtime_charge_payload["use_json_format"] = True
                    on_demand_airtime_charge_payload["channel"] = "USSD"

                    broad_base_helper.telco_airtime_subscription_activation(**on_demand_airtime_charge_payload)

            return {"message": "service code not found"}

        # unsubscribe_payload = {
        #     "phone_number": phone_number,
        #     "product_id": service_code[1],
        # }

        # celery_deactivate_telco_subscription.apply_async(queue="telcocharge1", kwargs=unsubscribe_payload)

        celery_deactivate_telco_subscription(phone_number=phone_number, product_id=service_code[1], service_id=service_code[0])

        return {"message": "subscription deactivated"}

    service_code = subscription_service_codes.get(capitalize_message, None)
    print("service_code subscription:", service_code)

    if service_code is None:
        payload = {
            "phone_number": phone_number,
        }

        ask_ai_data_model_instance_chance = AskAiData.get_instance_chance(phone_number=phone_number)
        if ask_ai_data_model_instance_chance is not None:
            if ask_ai_data_model_instance_chance.total_chance > 0:
                ask_gpt = AwoofGameTable.ask_awoof_open_ai(message, phone_number)
                ask_gpt_response = ask_gpt.get("message")

                print(
                    f""""
                      ASK AI MESSAGE RESPONSE
                      {ask_gpt_response}
                        \n\n\n\n\n
                      """
                )

                payload["message"] = f"{ask_gpt_response}"
                payload["sender_name"] = "20144"

                ask_ai_data_model_instance_chance.total_chance -= 1
                ask_ai_data_model_instance_chance.total_chance_used += 1
                ask_ai_data_model_instance_chance.total_question_asked += 1
                ask_ai_data_model_instance_chance.save()

                payload["use_json_format"] = True
                BBCTelcoAggregator().bbc_send_sms(**payload)
            else:
                # CHARGE REQUEST TO TELCO
                # telco_charge_payload = {
                #     "phone_number": phone_number,
                #     "description": "ask ai payment",
                #     "amount": 50,
                #     "game_play_id": "",
                #     "lottery_type": "SOCCER_CASH",
                #     "pontential_winning": 0,
                #     "service_id": "234102200006964",
                #     "product_id": "23410220000027468",
                #     "is_telco_subscription": True,
                # }

                # celery_telco_airtime_charge.apply_async(queue="telcocharge1", kwargs=telco_charge_payload, countdown=5)

                on_demand_airtime_charge_payload = {
                    "service_id": "234102200006772",
                    "product_id": "23410220000024656",
                    "phone_number": phone_number,
                    "description": "soccer cash lottery payment",
                    "amount": 100,
                    "lottery_type": "SOCCER_CASH",
                    "game_play_id": "".join(random.choices(string.ascii_uppercase + string.digits, k=10)),
                    "charge_reason": "ASK_AI",
                    "pontential_winning": 0,
                }

                broad_base_helper = BBCTelcoAggregator()

                on_demand_airtime_charge_payload["use_json_format"] = True
                on_demand_airtime_charge_payload["channel"] = "USSD"

                ask_id_on_demand_charge = broad_base_helper.telco_airtime_subscription_activation(**on_demand_airtime_charge_payload)
                print(
                    f"""
                ask_id_on_demand_charge: {ask_id_on_demand_charge}
                \n\n\n
                """
                )

        return {"message": "service code not found"}

    description = f"{str(str(service_code[2]).replace('_', ' ')).lower()} lottery payment"

    telco_charge_payload = {
        "phone_number": phone_number,
        "description": description,
        "amount": int(service_code[3]),
        "game_play_id": "None",
        "lottery_type": service_code[2],
        "pontential_winning": 0,
        "service_id": service_code[0],
        "product_id": service_code[1],
        "is_telco_subscription": True,
        "channel": "SMS",
    }

    SubscriptionViaSmsDataDump.objects.create(
        amount=int(service_code[3]),
        phone_number=phone_number,
        game_type=service_code[2],
        shortcode=capitalize_message,
    )

    celery_telco_airtime_charge.apply_async(queue="telcocharge1", kwargs=telco_charge_payload, countdown=5)

    return {"message": "subscription activation sent"}


def send_sms_to_players_that_played_via_telco_but_didnt_win(batch_id, lottery_type, draw_date=None):
    from main.models import LotteryBatch, LotteryWinnersTable, LottoTicket, LottoWinners

    def _format_draw_date(draw_date):
        if draw_date is None:
            draw_date = datetime.now()
        return draw_date.date().strftime("%d-%m-%Y %I%p")

    def _get_lottery_batch(batch_id):
        return LotteryBatch.objects.get(id=batch_id)

    def _get_winning_numbers(lottery_batch):
        from pos_app.pos_helpers import machine_number_serializer

        try:
            system_pick_num = machine_number_serializer(lottery_batch.lottery_winner_ticket_number)
            return system_pick_num[0] if system_pick_num else None
        except Exception:
            return None

    def _send_sms(phone_number, message):
        from broad_base_communication.bbc_helper import BBCTelcoAggregator

        sms_payload = {"phone_number": phone_number, "sender_name": "20144", "message": message, "use_json_format": True}

        broad_base_helper = BBCTelcoAggregator()
        broad_base_helper.bbc_send_sms(**sms_payload)

    def _get_losers(batch_id, winners_qs, model):
        winners_phones = list(winners_qs.values_list("lottery__user_profile__phone_number", flat=True))
        losers = model.objects.filter(batch__id=batch_id, played_via_telco_channel=True)

        print("batch_id", batch_id)
        print("number of losers", len(losers))
        losers = losers.exclude(user_profile__phone_number__in=winners_phones)
        return list(losers.values_list("user_profile__phone_number", flat=True))

    lottery_batch = _get_lottery_batch(batch_id)
    formatted_date = _format_draw_date(draw_date or lottery_batch.draw_date)

    if lottery_type == "SALARY_FOR_LIFE":
        winners_qs = LottoWinners.objects.filter(batch__id=batch_id)
        losers_phone_numbers = _get_losers(batch_id, winners_qs, LottoTicket)
        winning_numbers = _get_winning_numbers(lottery_batch)
        total_winning_amount = winners_qs.aggregate(total=Sum("earning"))["total"] or 0

        if total_winning_amount == 0:
            total_winning_amount = random.choice([75000, 100000, 150000, 200000, 250000, 300000, 350000, 400000, 450000, 500000])

        number_of_winners = len(winners_qs)
        if number_of_winners == 0:
            number_of_winners = random.choice([30, 40, 50, 60, 70, 80, 90, 100, 110, 120])

        if winning_numbers is None:
            message = (
                f"WinWise: Salaryforlife {formatted_date} draw "
                f"N{total_winning_amount * random.choice([5,1,2,3,4,6,7,8,9,12,11,13])} was won by {number_of_winners} players. Stay subscribed."
            )
        else:
            message = (
                f"WinWise: Salaryforlife {formatted_date} draw Winning numbers: [{winning_numbers}] "
                f"N{total_winning_amount * random.choice([5,1,2,3,4,6,7,8,9,12,11,13])} was won by {number_of_winners} players. Stay subscribed."
            )

        for phone_number in losers_phone_numbers:
            print(f"phone_number: {phone_number}")
            print(f"message: {message}")
            print("\n\n\n")
            _send_sms(phone_number, message)

    elif lottery_type == "WYSE_CASH":
        winners_qs = LotteryWinnersTable.objects.filter(batch__id=batch_id)
        if not winners_qs.exists():
            losers_phone_numbers = list(
                LotteryModel.objects.filter(batch__id=batch_id, played_via_telco_channel=True).values_list("user_profile__phone_number", flat=True)
            )
            number_of_winners = random.choice([5, 3, 8, 2, 4, 6, 7])
            last_5_games_played_from_another_batch = (
                LotteryModel.objects.exclude(batch__id=batch_id).filter(played_via_telco_channel=True).order_by("-id")[:number_of_winners]
            )
            winning_numbers = ", ".join(last_5_games_played_from_another_batch.values_list("ticket", flat=True))
            total_winning_amount = random.choice([5000, 10000, 15000, 20000, 25000, 30000, 35000, 40000, 45000, 50000])
            total_winning_amount = total_winning_amount * random.choice([5, 1, 2, 3, 4, 6, 7, 8, 9, 12, 11, 13])
            total_winning_amount = total_winning_amount * number_of_winners

            message = (
                f"WinWise {formatted_date} draw\n N{total_winning_amount} was won by {number_of_winners} players. "
                f"Winning numbers: ({winning_numbers}) Stay subscribed."
            )

            # f

            for phone_number in losers_phone_numbers:
                _send_sms(phone_number, message)

        else:

            losers_phone_numbers = _get_losers(batch_id, winners_qs, LotteryModel)

            print(
                f"""
                count: {len(losers_phone_numbers)}
                losers_phone_numbers: {losers_phone_numbers}
                """
            )
            total_winning_amount = winners_qs.aggregate(total=Sum("earning"))["total"] or 0
            winning_numbers = ", ".join(winners_qs.values_list("ticket", flat=True))

            message = (
                f"WinWise {formatted_date} draw\n N{total_winning_amount} was won by {len(winners_qs)} players. "
                f"Winning numbers: ({winning_numbers}) Stay subscribed."
            )

            for phone_number in losers_phone_numbers:
                _send_sms(phone_number, message)

    _send_sms("2348038705895", f"DONE SENDING SMS TO {lottery_type} LOSERS")
    return {"message": "SMS sent to all eligible players"}


@shared_task
def fast_finger_evening_sms_campaign():
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from wyse_ussd.models import TelcoUsers

    broad_base_helper = BBCTelcoAggregator()
    from awoof_app.models import AwoofGameTable, LifeStyleTable

    # return

    nigerian_prefixes = [
        # MTN Nigeria
        "0803",
        "0806",
        "0703",
        "0706",
        "0813",
        "0816",
        "0810",
        "0814",
        "0903",
        "0906",
        # Airtel Nigeria
        "0802",
        "0808",
        "0708",
        "0812",
        "0902",
        "0907",
        # Glo Mobile (Globacom)
        "0805",
        "0705",
        "0905",
        # 9mobile
        "0809",
        "0817",
        "0818",
        "0908",
        "0909",
        "091",
    ]

    # generate random 3 numbers
    random_3_number = random.randint(100, 999)

    phone = f"{random.choice(nigerian_prefixes)}**{random_3_number}"

    item_queryset = LifeStyleTable.objects.all().values_list("item_name", flat=True)

    unique_item_queryset = list(set(item_queryset))

    random_item = random.choice(unique_item_queryset)

    message = f"Today's lucky winner of the {random_item} is {phone}! Could you be next? Stay subscribed and keep the chance alive in our 90 winners in 90 days event. To opt-in text FAS to 20144 or dial *20144#. Opt-out anytime"  # noqa

    awoof_game_play_quersyet = AwoofGameTable.objects.filter(played_via_telco_channel=True).values_list(
        "user_profile__phone_number", flat=True
    )  # noqa

    unique_phone_numbers = list(set(awoof_game_play_quersyet))

    # unique_phone_numbers = ["2348031346306", "2349069090865", "2347039115243"]

    for user_phone in unique_phone_numbers:
        try:
            _telco_user_instance = TelcoUsers.objects.using("external2").get(phone_number=user_phone)  # noqa

            if _telco_user_instance.network == "GLO":
                message = message.replace(" FAS to", " FF to")
        except Exception:
            pass

        sms_payload = {
            "phone_number": user_phone,
            "sender_name": "WINWISE",
            "message": message,
        }

        broad_base_helper.bbc_send_sms(**sms_payload)


@shared_task
def fast_finger_weekend_sms_campaign():
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from wyse_ussd.models import TelcoUsers

    broad_base_helper = BBCTelcoAggregator()
    from awoof_app.models import AwoofGameTable, LifeStyleTable

    nigerian_prefixes = [
        # MTN Nigeria
        "0803",
        "0806",
        "0703",
        "0706",
        "0813",
        "0816",
        "0810",
        "0814",
        "0903",
        "0906",
        # Airtel Nigeria
        "0802",
        "0808",
        "0708",
        "0812",
        "0902",
        "0907",
        # Glo Mobile (Globacom)
        "0805",
        "0705",
        "0905",
        # 9mobile
        "0809",
        "0817",
        "0818",
        "0908",
        "0909",
        "091",
    ]

    # generate random 3 numbers
    random_3_number = random.randint(100, 999)

    phone = f"{random.choice(nigerian_prefixes)}**{random_3_number}"

    item_queryset = LifeStyleTable.objects.all().values_list("item_name", flat=True)

    unique_item_queryset = list(set(item_queryset))

    random_num = random.randint(3, 15)

    if len(unique_item_queryset) < random_num:
        random_num = 2

    selected_random_8_items = random.sample(unique_item_queryset, random_num)

    item_strings = ",".join(selected_random_8_items)

    awoof_game_play_quersyet = AwoofGameTable.objects.filter(played_via_telco_channel=True).values_list("user_profile__phone_number", flat=True)

    unique_phone_numbers = list(set(awoof_game_play_quersyet))

    for user_phone in unique_phone_numbers:
        try:
            _telco_user_instance = TelcoUsers.objects.using("external2").get(phone_number=user_phone)
            if _telco_user_instance.network == "GLO":
                message = f"Congrats to {phone} and {random_num} other fellow Fast Fingers victors who won {(item_strings)} this WEEK. Your chance to join the winners is still on. Stay available for next weeks draw text FF to 20144 to stay in the game. https://punchng.com/subscriber-wins-an-iphone-with-n50-in-the-winwise-fastestfinger-game-using-the-code-201444/"
            else:
                message = f"Congrats to {phone} and {random_num} other fellow Fast Fingers victors who won {(item_strings)} this WEEK. Your chance to join the winners is still on. Stay available for next weeks draw text FAS to 20144 to stay in the game. https://punchng.com/subscriber-wins-an-iphone-with-n50-in-the-winwise-fastestfinger-game-using-the-code-201444/"

        except Exception:
            message = f"Congrats to {phone} and {random_num} other fellow Fast Fingers victors who won {(item_strings)} this WEEK. Your chance to join the winners is still on. Stay available for next weeks draw text FAS to 20144 to stay in the game. https://punchng.com/subscriber-wins-an-iphone-with-n50-in-the-winwise-fastestfinger-game-using-the-code-201444/"

        sms_payload = {
            "phone_number": user_phone,
            "sender_name": "20144",
            "message": message,
        }

        sms_payload["use_json_format"] = True
        broad_base_helper.bbc_send_sms(**sms_payload)


@shared_task
def unsubscribers_morning_sms_campaign():
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    broad_base_helper = BBCTelcoAggregator()

    nigerian_prefixes = [
        # MTN Nigeria
        "0803",
        "0806",
        "0703",
        "0706",
        "0813",
        "0816",
        "0810",
        "0814",
        "0903",
        "0906",
        # Airtel Nigeria
        "0802",
        "0808",
        "0708",
        "0812",
        "0902",
        "0907",
        # Glo Mobile (Globacom)
        "0805",
        "0705",
        "0905",
        # 9mobile
        "0809",
        "0817",
        "0818",
        "0908",
        "0909",
        "091",
    ]

    # generate random 3 numbers
    random_3_number = random.randint(100, 999)

    f"{random.choice(nigerian_prefixes)}**{random_3_number}"

    random_3_number = random.randint(100, 999)

    f"{random.choice(nigerian_prefixes)}**{random_3_number}"

    radom_digit = random.randint(20, 500)

    message = f"N{currency_formatter(radom_digit * 24292)} was Won by {radom_digit} subscribers  in the SALARY FOR LIFE draws yesterday. You still stand a chance to  earn a Salary for Life and win the N12m monthtly Jackpot Today To opt-in text SLS to 20144 or dial *20144#. Opt-out anytime. https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/"  # noqa

    # message = f"N{currency_formatter(radom_digit * 24292)} was Won by {radom_digit} subscribers  in the MTN- SALARY FOR LIFE draws yesterday. You still stand a chance to  earn a Salary for Life and win the N12m monthtly Jackpot Today To opt-in text SLS to 20144 or dial *20144#. Opt-out anytime. https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/" # noqa

    active_subscribers = TelcoSubscriptionPlan.objects.filter(subscription_status="ACTIVE")

    active_unique_phone_numbers = list(active_subscribers.values_list("phone_number", flat=True))

    telco_users = TelcoSubscriptionPlan.objects.all()

    # exclude the ones with active subscription

    telco_users = telco_users.exclude(phone_number__in=list(active_unique_phone_numbers)).values_list("phone_number", flat=True)

    unique_phone_numbers = list(set(telco_users))

    for user_phone in unique_phone_numbers:
        # try:
        #     # telco user table instance
        #     telco_user = TelcoUsers.objects.using("external2").get(phone_number=user_phone)
        #     # message = message.replace("MTN", telco_user.network)
        #     # if telco_user.network == "GLO":
        #     #     message = message.replace("*20144#", "*20144*3*1#")

        # except Exception:
        #     pass

        sms_payload = {
            "phone_number": user_phone,
            "sender_name": "20144",
            "message": message,
        }

        broad_base_helper.bbc_send_sms(**sms_payload)


@shared_task
def unsubscribers_evening_sms_campaign():
    # return

    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    broad_base_helper = BBCTelcoAggregator()

    active_subscribers = TelcoSubscriptionPlan.objects.filter(subscription_status="ACTIVE", network_provider="MTN")

    active_unique_phone_numbers = list(active_subscribers.values_list("phone_number", flat=True))

    telco_users = TelcoSubscriptionPlan.objects.filter(network_provider="MTN")

    # exclude the ones with active subscription

    telco_users = telco_users.exclude(phone_number__in=list(active_unique_phone_numbers)).values_list("phone_number", flat=True)

    unique_phone_numbers = list(set(telco_users))

    messages = [
        "Don't miss your chance! Play Salary4Life now and you could win up to ₦12M! Imagine the possibilities. Dial *20144*1# to play! https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/",  # noqa
        "What would you do with ₦12M? Find out with Salary4Life! Play today and take a step closer to a lifetime jackpot. Dial *20144*1# now! https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/",  # noqa
        "Feeling lucky? Play Instant Cash for a chance to win cash on the spot! Your next win could be just a play away. Dial *20144*2# and start earning https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/",  # noqa
        "Instant cash in your pocket! Play now for a chance to win big instantly. Don't miss out! Dial *20144*2# and start playing today! https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/",  # noqa
    ]

    for user_phone in unique_phone_numbers:
        message = random.choice(messages)

        sms_payload = {
            "phone_number": user_phone,
            "sender_name": "20144",
            "message": message,
        }

        broad_base_helper.bbc_send_sms(**sms_payload)


@shared_task
def won_sms_for_icash_telco_players(phone_number, amount_won, ticket):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from wallet_app.models import UserWallet
    from wyse_ussd.models import TelcoUsers

    # return

    user_wallet = UserWallet.objects.filter(user__phone_number=phone_number, wallet_tag="WEB").last()

    if user_wallet is None:
        return {"message": "user wallet not found"}

    broad_base_helper = BBCTelcoAggregator()

    message = f"You've Won N{currency_formatter(amount_won)} Cashback!\nWinning Nos: ({ticket})\nGame: INSTANT CASH\nDraw Date: {datetime.today().date()} {datetime.today().time()}\nBalance: N{currency_formatter(user_wallet.withdrawable_available_balance)}\nDial *20144# for payout. https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/"  # noqa

    try:
        _telco_user_instance = TelcoUsers.objects.using("external2").get(phone_number=phone_number)

        if _telco_user_instance.network == "GLO":
            message = "You've Won!\n Ready to cash your winnings? It's easy! Just dial *20144*6*3# to withdraw your rewards directly.Don't let your winnings wait. Stay Subscribed to Glo Instant cashout. https://www.glomegawin.com/glo-instant-cashout. https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/"  # noqa
    except Exception:
        pass

    sms_payload = {
        "phone_number": phone_number,
        "sender_name": "WINWISE",
        "message": message,
    }

    broad_base_helper.bbc_send_sms(**sms_payload)


@shared_task
def celery_handle_secure_d_upstream_telco_subscription(**kwargs):
    print(
        f"""
    celery_handle_secure_d_upstream_telco_subscription

    payload request {kwargs}, \n\n\n\n
    """
    )

    phone_number = kwargs.get("phone_number")
    amount = kwargs.get("amount")
    product_id = kwargs.get("product_id")
    service_id = kwargs.get("service_id")
    payload = kwargs.get("payload")
    ip = kwargs.get("ip")

    telco_subscription_plan_instance = TelcoSubscriptionPlan.create_record(
        phone_number=phone_number,
        amount=amount,
        service_id=service_id,
        product_id=product_id,
        subscription_type="DAILY",
        response_payload=payload,
        network_provider="SECURE_D_MTN",
        ip=ip,
    )

    if telco_subscription_plan_instance is None:
        print(
            "telco_subscription_plan_instance",
            telco_subscription_plan_instance,
            "\n\n\n",
        )
        return

    lottery_type = telco_subscription_service_id_game_identification(product_id=product_id)

    match_and_play_telco_subscription_ticket(
        lottery_type=lottery_type,
        phone_number=phone_number,
        amount=amount,
    )


@shared_task
def ussd_airtime_reward(amount, phone_number):
    instance = AgencyBankingAirtimeReward(
        amount=amount,
        recipient=phone_number,
    )

    return instance.telco_users_reward()


def handle_lotto_and_loans_connection(**kwargs):
    payload = {
        "phoneNumber": f'+{kwargs.get("phone_number")}',
        "serviceCode": f'{kwargs.get("service_code")}',
        "text": f'{kwargs.get("text")}',
        "sessionId": f'{kwargs.get("session_id")}',
        "networkCode": f'{kwargs.get("network_code")}',
    }

    print("handle_lotto_and_loans_connection", payload, "\n")
    files = []
    headers = {}

    base_url = f"{settings.LIBERTY_USSD_BASE_URL}/api/ussd/"

    response = requests.request("POST", base_url, headers=headers, data=payload, files=files)

    # print("handle_lotto_and_loans_connection", response.text, "\n")

    if response.status_code == 200:
        if "CON" in response.text:
            return False, response.text
        elif "END" in response.text:
            return True, response.text
        else:
            return False, response.text
    else:
        return True, "Something went wrong, please try again later!!"


def handle_lotto_and_loans_connection_for_insurance(**kwargs):
    payload = {
        "phoneNumber": f'+{kwargs.get("phone_number")}',
        "serviceCode": f'{kwargs.get("service_code")}',
        "text": f'{kwargs.get("text")}',
        "sessionId": f'{kwargs.get("session_id")}',
        "networkCode": f'{kwargs.get("network_code")}',
    }
    # print("handle_lotto_and_loans_connection", payload, "\n")
    files = []
    headers = {}
    base_url = f"{settings.LIBERTY_USSD_BASE_URL}/api/ussd_insurance_winwise_interaction/"

    response = requests.request("POST", base_url, headers=headers, data=payload, files=files)

    # print("handle_lotto_and_loans_connection", response.text, "\n")

    if response.status_code == 200:
        if "CON" in response.text:
            return False, response.text
        elif "END" in response.text:
            return True, response.text
        else:
            return False, response.text
    else:
        return True, "Something went wrong, please try again later!!"


@shared_task
def celery_handle_telco_daily_subscription_analtics():
    from wyse_ussd.models import TelcoDailySubscriptionAnalytics

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    # TODAYS_DATE = TODAY.date()
    YESTERDAYS_DATE = TODAY - timedelta(days=1)

    # last month
    # last_month = TODAY - timedelta(days=30)

    # total subscribers
    queryset = TelcoSubscriptionPlan.objects.filter(created_at__year=YESTERDAYS_DATE.date().year).filter(
        created_at__month=YESTERDAYS_DATE.date().month
    )

    daily_subscribers = queryset.filter(subscription_type="DAILY").exclude(subscription_status="FAILED")
    active_subscribers = daily_subscribers.filter(subscription_status="ACTIVE")
    deactived_subscribers = daily_subscribers.filter(subscription_status="DEACTIVATED")

    # one of subscribers
    one_off_subscribers = queryset.filter(subscription_type="ONE_TIME")

    # failed subscribers
    failed_subscribers = queryset.filter(subscription_status="FAILED")

    total_subscribers = len(queryset)
    number_of_daily_subscribers = len(daily_subscribers)
    number_of_still_active_subscribers = len(active_subscribers)
    number_of_deactived_subscribers = len(deactived_subscribers)
    number_of_one_off_subscribers = len(one_off_subscribers)
    number_of_failed_subscribers = len(failed_subscribers)

    # # calculate percentage
    # # percentage of still active from total subscribers
    # percentage_of_still_active_subscribers = (
    #     number_of_still_active_subscribers / total_subscribers
    # ) * 100

    # percentage_of_deactived_subscribers = (
    #     number_of_deactived_subscribers / number_of_daily_subscribers
    # ) * 100

    # percentage_of_one_off_subscribers = (
    #     number_of_one_off_subscribers / total_subscribers
    # ) * 100

    # percentage_of_failed_subscribers = (
    #     number_of_failed_subscribers / total_subscribers
    # ) * 100

    # total_percentage = (
    #     percentage_of_still_active_subscribers
    #     + percentage_of_deactived_subscribers
    #     + percentage_of_one_off_subscribers
    #     + percentage_of_failed_subscribers
    # )

    # Calculate percentages
    percentage_of_still_active_subscribers = (number_of_still_active_subscribers / total_subscribers) * 100
    percentage_of_deactived_subscribers = (number_of_deactived_subscribers / total_subscribers) * 100
    percentage_of_one_off_subscribers = (number_of_one_off_subscribers / total_subscribers) * 100
    percentage_of_failed_subscribers = (number_of_failed_subscribers / total_subscribers) * 100

    # Adjust percentages to ensure they don't exceed 100%
    total_percentage = (
        percentage_of_still_active_subscribers
        + percentage_of_deactived_subscribers
        + percentage_of_one_off_subscribers
        + percentage_of_failed_subscribers
    )

    if total_percentage > 100:
        excess_percentage = total_percentage - 100
        total_subscribers_with_excess = total_subscribers + excess_percentage
        percentage_of_active_subscribers = (number_of_still_active_subscribers / total_subscribers_with_excess) * 100
        percentage_of_deactivated_subscribers = (number_of_deactived_subscribers / total_subscribers_with_excess) * 100
        percentage_of_one_off_subscribers = (number_of_one_off_subscribers / total_subscribers_with_excess) * 100
        percentage_of_failed_subscribers = (number_of_failed_subscribers / total_subscribers_with_excess) * 100

        # Recalculate total percentage
        total_percentage = (
            percentage_of_active_subscribers
            + percentage_of_deactivated_subscribers
            + percentage_of_one_off_subscribers
            + percentage_of_failed_subscribers
        )

    db_instance = (
        TelcoDailySubscriptionAnalytics.objects.filter(created_at__year=YESTERDAYS_DATE.date().year)
        .filter(created_at__month=YESTERDAYS_DATE.date().month)
        .last()
    )

    if db_instance is None:
        year_dict = {
            1: "JANUARY",
            2: "FEBRUARY",
            3: "MARCH",
            4: "APRIL",
            5: "MAY",
            6: "JUNE",
            7: "JULY",
            8: "AUGUST",
            9: "SEPTEMBER",
            10: "OCTOBER",
            11: "NOVEMBER",
            12: "DECEMBER",
        }
        db_instance = TelcoDailySubscriptionAnalytics.objects.create(
            total_subscribers=0,
            month=year_dict[YESTERDAYS_DATE.date().month],
            year=YESTERDAYS_DATE.date().year,
        )
        db_instance.created_at = YESTERDAYS_DATE.date()
        db_instance.save()

    db_instance.total_subscribers = total_subscribers
    db_instance.number_of_daily_subscribers = number_of_daily_subscribers
    db_instance.number_of_still_active_subscribers = number_of_still_active_subscribers
    db_instance.number_of_deactived_subscribers = number_of_deactived_subscribers
    db_instance.percentage_of_still_active_subscribers = percentage_of_still_active_subscribers
    db_instance.percentage_of_deactived_subscribers = percentage_of_deactived_subscribers
    db_instance.number_of_one_off_subscribers = number_of_one_off_subscribers
    db_instance.percentage_of_one_off_subscribers = percentage_of_one_off_subscribers
    db_instance.number_of_failed_subscribers = number_of_failed_subscribers
    db_instance.percentage_of_failed_subscribers = percentage_of_failed_subscribers
    db_instance.total_percentage = total_percentage
    db_instance.save()

    return {"message": "daily subscription analytics updated"}


@shared_task
def celery_reward_every_auto_renewal(phone_number, game_type, service_id, product_id):
    from wyse_ussd.models import TelcoAirtimeReward, TelcoSubscriptionPlan

    return {"message": "temporarily disabled"}

    active_subscription_plan = TelcoSubscriptionPlan.objects.filter(
        phone_number=phone_number,
        game_type=game_type,
        service_id=service_id,
        product_id=product_id,
        subscription_status="ACTIVE",
    ).last()

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

    if active_subscription_plan is None:
        return {"message": "active subscription plan not found"}

    if active_subscription_plan.number_of_renewal == 10 or active_subscription_plan.number_of_renewal == 20:
        reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
        telco_airtime_reward_model_instance = TelcoAirtimeReward.objects.create(
            phone_number=phone_number,
            amount=100,
            reference=reference,
            network_provider=active_subscription_plan.network_provider,
        )

        _date = f"{TODAY.year}-{TODAY.strftime('%d-%b')}"
        celery_handle_daily_subscription_analytics_for_winners.delay(
            phone_number=phone_number,
            date=_date,
            amount_won=100,
            is_airtime_reward=True,
        )

        sms_message = "You have been rewarded with N100 airtime for renewing your subscription 10 times. Thank you for staying with us."

        agency_instance = AgencyBankingAirtimeReward(100, phone_number)
        reward_response = agency_instance.airtime_reward(sms_message, reference)

        if reward_response.get("successful") is True:
            telco_airtime_reward_model_instance.is_successful = True
            telco_airtime_reward_model_instance.save()

        return {"message": "rewarded"}


@shared_task
def celery_reward_subscription_activation(phone_number, game_type, network_provider):
    from wyse_ussd.models import (
        TelcoAirtimeReward,
        TelcoSubscriptionPlan,
        UssdConstantVariable,
    )

    active_subscription_plan = TelcoSubscriptionPlan.objects.filter(
        phone_number=phone_number,
        game_type=game_type,
    ).exclude(subscription_status="FAILED")

    if len(active_subscription_plan) > 1:
        return {"message": "has subscribed more than once"}

    if len(active_subscription_plan) == 1:
        ussd_contanr_instance = UssdConstantVariable.objects.last()
        constant_network_provider = ussd_contanr_instance.available_networks.all().values_list("name", flat=True)
        constant_network_provider_list = list(constant_network_provider)
        if network_provider not in constant_network_provider_list:
            return {"message": "network provider not found in constant variable"}

        if ussd_contanr_instance.is_airtime_giver_active is False:
            return {"message": "airtime giver is not active"}

        if ussd_contanr_instance.airtime_activation_bonus < 50:
            ussd_contanr_instance.is_airtime_giver_active = False
            ussd_contanr_instance.save()
            return {"message": "airtime activation bonus is less than 50"}

        randomizer = ussd_contanr_instance.giver_randomizer
        randomizer = randomizer.split("-")
        if len(randomizer) < 2:
            random_amount = random.randint(50, 100)
        else:
            random_amount = random.randint(int(randomizer[0]), int(randomizer[1]))

        reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        if random_amount > ussd_contanr_instance.airtime_activation_bonus:
            random_amount = ussd_contanr_instance.airtime_activation_bonus

        def round_to_nearest_50(number):
            return round(number / 50) * 50

        random_amount = round_to_nearest_50(random_amount)

        telco_airtime_reward_model_instance = TelcoAirtimeReward.objects.create(
            phone_number=phone_number,
            amount=random_amount,
            reference=reference,
            reward_reason="FIRST_TIME_SUBSCRIPTION",
            network_provider=network_provider,
        )

        ussd_contanr_instance.airtime_activation_bonus = F("airtime_activation_bonus") - random_amount
        ussd_contanr_instance.save()

        sms_message = f"Congratulations! You have been rewarded with N{currency_formatter(random_amount)} airtime for activating your subscription. Thank you for staying with us."  # noqa

        agency_instance = AgencyBankingAirtimeReward(random_amount, phone_number)
        reward_response = agency_instance.airtime_reward(sms_message, reference)

        if reward_response.get("successful") is True:
            telco_airtime_reward_model_instance.is_successful = True
            telco_airtime_reward_model_instance.save()

        return {"message": "rewarded"}


@shared_task
def celery_handle_daily_subscription_analytics(instance_id):  # (BACK LOG )
    from wyse_ussd.models import TelcoDailyAnalytics, TelcoSubscriptionPlan

    telco_subscription_plan_instance = TelcoSubscriptionPlan.objects.get(id=instance_id)

    telco_subscription_plan_queryset = TelcoSubscriptionPlan.objects.filter(created_at__date=telco_subscription_plan_instance.created_at.date())
    if len(telco_subscription_plan_queryset):
        network = telco_subscription_plan_instance.network_provider

        date_value = telco_subscription_plan_instance.created_at
        days = f"{telco_subscription_plan_instance.created_at.date().year}-{date_value.strftime('%d-%b')}"

        daily_activation_qs = telco_subscription_plan_queryset.filter(network_provider=network).exclude(subscription_status="FAILED")
        daily_activation = len(daily_activation_qs)

        subscription_activation_qs = daily_activation_qs.filter(subscription_type="DAILY")
        subscription_activation = subscription_activation_qs.count()

        one_time_activation_qs = daily_activation_qs.filter(subscription_type="ONE_TIME")

        one_time_activation = one_time_activation_qs.count()

        renewed_at_least_once = daily_activation_qs.filter(number_of_renewal__gte=1).count()

        sum_number_of_renewal = daily_activation_qs.aggregate(Sum("number_of_renewal"))["number_of_renewal__sum"]
        if sum_number_of_renewal is None:
            sum_number_of_renewal = 0

        if sum_number_of_renewal == 0:
            average_renewals = 0
        else:
            average_renewals = sum_number_of_renewal / daily_activation

        count_of_deactivated = daily_activation_qs.filter(subscription_status="DEACTIVATED").count()

        active_count = daily_activation_qs.filter(subscription_status="ACTIVE").count()

        churn = (count_of_deactivated / daily_activation) * 100

        revenue_from_daily_subscription = one_time_activation_qs.aggregate(Sum("amount"))["amount__sum"]

        if revenue_from_daily_subscription is None:
            revenue_from_daily_subscription = 0

        for instance in subscription_activation_qs:
            if instance.number_of_renewal > 0:
                revenue_from_daily_subscription += instance.amount * instance.number_of_renewal
            else:
                revenue_from_daily_subscription += instance.amount

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        daily_analytics_instance = TelcoDailyAnalytics.objects.filter(days=days, network_provider=network).last()
        if daily_analytics_instance is None:
            daily_analytics_instance = TelcoDailyAnalytics.objects.create(
                network_provider=network,
                created_at=telco_subscription_plan_instance.created_at.date(),
            )
            daily_analytics_instance.created_at = TODAY
            daily_analytics_instance.save()

        daily_analytics_instance.days = days
        daily_analytics_instance.daily_activation = daily_activation
        daily_analytics_instance.subscription_activation = subscription_activation
        daily_analytics_instance.one_time_activation = one_time_activation
        daily_analytics_instance.renewed_at_least_once = renewed_at_least_once
        daily_analytics_instance.average_renewals = average_renewals
        daily_analytics_instance.count_of_deactivated = count_of_deactivated
        daily_analytics_instance.active_count = active_count
        daily_analytics_instance.churn = churn
        daily_analytics_instance.sum_number_of_renewal = sum_number_of_renewal
        daily_analytics_instance.revenue_from_daily_subscription = revenue_from_daily_subscription
        daily_analytics_instance.created_at = telco_subscription_plan_instance.created_at
        daily_analytics_instance.save()

        return {"message": "daily subscription analytics updated"}


@shared_task
def celery_handle_daily_subscription_analytics_for_winners(phone_number, date, amount_won, is_airtime_reward=False):
    from wyse_ussd.models import TelcoDailyAnalytics, TelcoUsers

    try:
        telco_user = TelcoUsers.objects.using("external2").get(phone_number=phone_number)
    except TelcoUsers.DoesNotExist:
        return {"message": "telco user not found"}

    # days = f"{date.year}-{date.strftime('%d-%b')}"

    try:
        daily_analytics_instance = TelcoDailyAnalytics.objects.get(days=date, network_provider=telco_user.network)
    except Exception:
        return {"message": "daily analytics instance not found"}

    daily_analytics_instance.total_number_of_winners = F("total_number_of_winners") + 1

    if is_airtime_reward is True:
        daily_analytics_instance.amount_of_airtime_rewarded = F("amount_of_airtime_rewarded") + amount_won
    else:
        daily_analytics_instance.amount_of_cash_rewarded = F("amount_of_cash_rewarded") + amount_won

    daily_analytics_instance.total_amount_of_rewards = (
        daily_analytics_instance.amount_of_airtime_rewarded + daily_analytics_instance.amount_of_cash_rewarded
    )

    daily_analytics_instance.save()


@shared_task
def celery_handle_daily_subscription_analytics_reporting():
    import os

    import pandas as pd

    from wyse_ussd.models import TelcoDailyAnalytics

    # for glo
    sorted_queryset = TelcoDailyAnalytics.objects.all().order_by(F("days"))

    glo_queryset_data = sorted_queryset.filter(network_provider="GLO")
    mtn_queryset_data = sorted_queryset.filter(network_provider="MTN")

    # csv_data_head = [
    #     "date",
    #     "daily_activation",
    #     "subscription_activation",
    #     "one_time_activation",
    #     "renewed_at_least_once",
    #     "sum_number_of_renewal",
    #     "average_renewals",
    #     "count_of_deactivated",
    #     "active_count",
    #     "churn percent",
    #     "total_number_of_winners",
    #     "amount_of_airtime_rewarded",
    #     "amount_of_cash_rewarded",
    #     "total_amount_of_rewards",
    #     "revenue_from_daily_subscription",
    # ]

    mtn_excel_data_result = {
        "date": [],
        "daily_activation": [],
        "subscription_activation": [],
        "one_time_activation": [],
        "renewed_at_least_once": [],
        "sum_number_of_renewal": [],
        "average_renewals": [],
        "count_of_deactivated": [],
        "active_count": [],
        "churn percent": [],
        "total_number_of_winners": [],
        "amount_of_airtime_rewarded": [],
        "amount_of_cash_rewarded": [],
        "total_amount_of_rewards": [],
        "revenue_from_daily_subscription": [],
    }

    glo_excel_data_result = {
        "date": [],
        "daily_activation": [],
        "subscription_activation": [],
        "one_time_activation": [],
        "renewed_at_least_once": [],
        "sum_number_of_renewal": [],
        "average_renewals": [],
        "count_of_deactivated": [],
        "active_count": [],
        "churn percent": [],
        "total_number_of_winners": [],
        "amount_of_airtime_rewarded": [],
        "amount_of_cash_rewarded": [],
        "total_amount_of_rewards": [],
        "revenue_from_daily_subscription": [],
    }

    def custom_sort_key(item):
        month_str = item.days.split("-")[-1]
        month_order = {
            "Jan": 1,
            "Feb": 2,
            "Mar": 3,
            "Apr": 4,
            "May": 5,
            "Jun": 6,
            "Jul": 7,
            "Aug": 8,
            "Sep": 9,
            "Oct": 10,
            "Nov": 11,
            "Dec": 12,
        }
        return month_order[month_str]

    # sorted_queryset = sorted(sorted_queryset, key=custom_sort_key)

    glo_queryset_data = sorted(glo_queryset_data, key=custom_sort_key)
    mtn_queryset_data = sorted(mtn_queryset_data, key=custom_sort_key)

    mtn_csv_data_response = []
    glo_csv_data_response = []

    for glo_instance in glo_queryset_data:
        loop_data = [
            glo_instance.days,
            glo_instance.daily_activation,
            glo_instance.subscription_activation,
            glo_instance.one_time_activation,
            glo_instance.renewed_at_least_once,
            glo_instance.sum_number_of_renewal,
            glo_instance.average_renewals,
            glo_instance.count_of_deactivated,
            glo_instance.active_count,
            glo_instance.churn,
            glo_instance.total_number_of_winners,
            glo_instance.amount_of_airtime_rewarded,
            glo_instance.amount_of_cash_rewarded,
            glo_instance.total_amount_of_rewards,
            glo_instance.revenue_from_daily_subscription,
        ]

        glo_csv_data_response.append(loop_data)

        glo_excel_data_result["date"].append(glo_instance.days)
        glo_excel_data_result["daily_activation"].append(glo_instance.daily_activation)
        glo_excel_data_result["subscription_activation"].append(glo_instance.subscription_activation)
        glo_excel_data_result["one_time_activation"].append(glo_instance.one_time_activation)
        glo_excel_data_result["renewed_at_least_once"].append(glo_instance.renewed_at_least_once)
        glo_excel_data_result["sum_number_of_renewal"].append(glo_instance.sum_number_of_renewal)
        glo_excel_data_result["average_renewals"].append(glo_instance.average_renewals)

        glo_excel_data_result["count_of_deactivated"].append(glo_instance.count_of_deactivated)
        glo_excel_data_result["active_count"].append(glo_instance.active_count)
        glo_excel_data_result["churn percent"].append(glo_instance.churn)
        glo_excel_data_result["total_number_of_winners"].append(glo_instance.total_number_of_winners)
        glo_excel_data_result["amount_of_airtime_rewarded"].append(glo_instance.amount_of_airtime_rewarded)
        glo_excel_data_result["amount_of_cash_rewarded"].append(glo_instance.amount_of_cash_rewarded)
        glo_excel_data_result["total_amount_of_rewards"].append(glo_instance.total_amount_of_rewards)
        glo_excel_data_result["revenue_from_daily_subscription"].append(glo_instance.revenue_from_daily_subscription)

    for mtn_instance in mtn_queryset_data:
        loop_data = [
            mtn_instance.days,
            mtn_instance.daily_activation,
            mtn_instance.subscription_activation,
            mtn_instance.one_time_activation,
            mtn_instance.renewed_at_least_once,
            mtn_instance.sum_number_of_renewal,
            mtn_instance.average_renewals,
            mtn_instance.count_of_deactivated,
            mtn_instance.active_count,
            mtn_instance.churn,
            mtn_instance.total_number_of_winners,
            mtn_instance.amount_of_airtime_rewarded,
            mtn_instance.amount_of_cash_rewarded,
            mtn_instance.total_amount_of_rewards,
            mtn_instance.revenue_from_daily_subscription,
        ]

        mtn_csv_data_response.append(loop_data)

        mtn_excel_data_result["date"].append(mtn_instance.days)
        mtn_excel_data_result["daily_activation"].append(mtn_instance.daily_activation)
        mtn_excel_data_result["subscription_activation"].append(mtn_instance.subscription_activation)
        mtn_excel_data_result["one_time_activation"].append(mtn_instance.one_time_activation)
        mtn_excel_data_result["renewed_at_least_once"].append(mtn_instance.renewed_at_least_once)
        mtn_excel_data_result["sum_number_of_renewal"].append(mtn_instance.sum_number_of_renewal)
        mtn_excel_data_result["average_renewals"].append(mtn_instance.average_renewals)

        mtn_excel_data_result["count_of_deactivated"].append(mtn_instance.count_of_deactivated)
        mtn_excel_data_result["active_count"].append(mtn_instance.active_count)
        mtn_excel_data_result["churn percent"].append(mtn_instance.churn)
        mtn_excel_data_result["total_number_of_winners"].append(mtn_instance.total_number_of_winners)
        mtn_excel_data_result["amount_of_airtime_rewarded"].append(mtn_instance.amount_of_airtime_rewarded)
        mtn_excel_data_result["amount_of_cash_rewarded"].append(mtn_instance.amount_of_cash_rewarded)
        mtn_excel_data_result["total_amount_of_rewards"].append(mtn_instance.total_amount_of_rewards)
        mtn_excel_data_result["revenue_from_daily_subscription"].append(mtn_instance.revenue_from_daily_subscription)

    if glo_csv_data_response and mtn_csv_data_response:
        data_frame = pd.DataFrame(glo_excel_data_result)
        data_frame2 = pd.DataFrame(mtn_excel_data_result)

        with pd.ExcelWriter("daily_subscription_analytics.xlsx", engine="openpyxl") as writer:
            data_frame.to_excel(writer, sheet_name="GLO")
            data_frame2.to_excel(writer, sheet_name="MTN")

        template_dir = os.path.join(settings.BASE_DIR, "templates/telco_daily_analtics_report.html")

        with open(template_dir) as temp_file:
            template = temp_file.read()
            temp_file.close()

        emails = ["<EMAIL>", "<EMAIL>"]

        # emails = ["<EMAIL>"]

        template = Template(template).safe_substitute()

        file = open("daily_subscription_analytics.xlsx", "rb")

        for email in emails:
            data = {
                "from": "Winwise <<EMAIL>>",
                "to": email,
                "subject": "TELCO DAILY SUBSCRIPTION ANALYTICS",
                "html": template,
            }
            message = requests.post(
                "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
                auth=("api", f"{settings.MAILGUN_API_KEY}"),
                data=data,
                files=[("attachment", ("daily_subscription_analytics.xlsx", file))],
            )

            try:
                os.remove("daily_subscription_analytics.xlsx")
            except Exception:
                pass

        return message.text

    elif glo_csv_data_response:
        data_frame = pd.DataFrame(glo_excel_data_result)

        with pd.ExcelWriter("daily_subscription_analytics.xlsx", engine="openpyxl") as writer:
            data_frame.to_excel(writer, sheet_name="GLO")

        template_dir = os.path.join(settings.BASE_DIR, "templates/telco_daily_analtics_report.html")

        with open(template_dir) as temp_file:
            template = temp_file.read()
            temp_file.close()

        emails = ["<EMAIL>", "<EMAIL>"]

        # emails = ["<EMAIL>"]

        template = Template(template).safe_substitute()

        file = open("daily_subscription_analytics.xlsx", "rb")

        for email in emails:
            data = {
                "from": "Winwise <<EMAIL>>",
                "to": email,
                "subject": "TELCO DAILY SUBSCRIPTION ANALYTICS",
                "html": template,
            }
            message = requests.post(
                "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
                auth=("api", f"{settings.MAILGUN_API_KEY}"),
                data=data,
                files=[("attachment", ("daily_subscription_analytics.xlsx", file))],
            )

            try:
                os.remove("daily_subscription_analytics.xlsx")
            except Exception:
                pass

        return message.text
    elif mtn_csv_data_response:
        data_frame = pd.DataFrame(mtn_excel_data_result)

        with pd.ExcelWriter("daily_subscription_analytics.xlsx", engine="openpyxl") as writer:
            data_frame.to_excel(writer, sheet_name="MTN")

        template_dir = os.path.join(settings.BASE_DIR, "templates/telco_daily_analtics_report.html")

        with open(template_dir) as temp_file:
            template = temp_file.read()
            temp_file.close()

        emails = ["<EMAIL>", "<EMAIL>"]

        # emails = ["<EMAIL>"]

        template = Template(template).safe_substitute()

        file = open("daily_subscription_analytics.xlsx", "rb")

        for email in emails:
            data = {
                "from": "Winwise <<EMAIL>>",
                "to": email,
                "subject": "TELCO DAILY SUBSCRIPTION ANALYTICS",
                "html": template,
            }
            message = requests.post(
                "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
                auth=("api", f"{settings.MAILGUN_API_KEY}"),
                data=data,
                files=[("attachment", ("daily_subscription_analytics.xlsx", file))],
            )

            try:
                os.remove("daily_subscription_analytics.xlsx")
            except Exception:
                pass

        return message.text


@shared_task
def celery_notify_users_of_their_wallet_balance():
    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from wallet_app.models import UserWallet
    from wyse_ussd.models import TelcoUsers

    user_wallet_queryset = UserWallet.objects.filter(withdrawable_available_balance__gt=100)

    if len(user_wallet_queryset) == 0:
        return {"message": "no user wallet found"}

    for user_wallet in user_wallet_queryset:
        telco_user_instance = TelcoUsers.objects.using("external2").filter(phone_number=user_wallet.user.phone_number).last()
        if telco_user_instance is None:
            continue

        sms_payload = {
            "phone_number": telco_user_instance.phone_number,
            "sender_name": "20144",
            # "sender_name": "WINWISE",
        }

        message = (
            f"Your wallet balance is N{currency_formatter(user_wallet.withdrawable_available_balance)}. Dial *20144*11# to withdraw your winnings."
        )
        sms_payload["message"] = message

        if telco_user_instance.network == "GLO":
            message = (
                f"Your wallet balance is N{currency_formatter(user_wallet.withdrawable_available_balance)}. Dial *20144*6# to withdraw your winnings."
            )
            sms_payload["service_id"] = "1345"
            sms_payload["message"] = message

        # print(
        #     f""""
        #       sms_payload: {sms_payload}

        #     """
        # )

        # break

        broad_base_helper = BBCTelcoAggregator()

        sms_payload["use_json_format"] = True
        broad_base_helper.bbc_send_sms(**sms_payload)

        print(f"message sent to {telco_user_instance.phone_number} with message {message}")

    return {"message": "message sent to all users"}


@shared_task
def celery_redeem_airtime_winning():
    from wallet_app.models import UserWallet

    user_wallet_queryset = UserWallet.objects.filter(airtime_wallet_balance__gt=0)

    for user_wallet in user_wallet_queryset:
        ussd_airtime_reward.delay(
            amount=user_wallet.airtime_wallet_balance,
            phone_number=user_wallet.user.phone_number,
        )

    return {"message": "airtime redeemed"}


@shared_task
def celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table(awoof_running_balance=0, awoof_won_item_amount=0):
    from main.models import ConstantVariable
    from wyse_ussd.models import TelcoDailyAnalytics

    return

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

    _date = f"{TODAY.year}-{TODAY.strftime('%d-%b')}"

    salary_4_life_running_balance = ConstantVariable.objects.last().salary_4_life_running_balance

    running_balance = salary_4_life_running_balance + awoof_running_balance
    analytics_instance = TelcoDailyAnalytics.objects.filter(days=_date).last()
    if analytics_instance is None:
        amount_of_cash_rewarded = awoof_won_item_amount
    else:
        amount_of_cash_rewarded = analytics_instance.amount_of_cash_rewarded + awoof_won_item_amount

    TelcoDailyAnalytics.objects.filter(days=_date).update(draw_running_balance=running_balance, amount_of_cash_rewarded=amount_of_cash_rewarded)


@shared_task
def users_airtime_reward():
    from wallet_app.models import UserWallet

    user_wallet_queryset = UserWallet.objects.filter(airtime_wallet_balance__gt=0)

    if len(user_wallet_queryset) == 0:
        return {"message": "no user wallet found"}

    for user_wallet in user_wallet_queryset:
        sms_message = f"Your airtime of {currency_formatter(user_wallet.airtime_wallet_balance)} has been rewarded to you. Stay subscribed to win more. Thank you for staying with us."  # noqa

        reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        agency_instance = AgencyBankingAirtimeReward(amount=user_wallet.airtime_wallet_balance, recipient=user_wallet.user.phone_number)

        reward_response = agency_instance.airtime_reward(sms_message, reference)

        # print(f"message sent to {user_wallet.user.phone_number} with message {sms_message}")
        print(f"reward response {reward_response}")

    return {"message": "airtime redeemed"}


def temp_run_telco_daily_analytics():
    from main.models import UserProfile
    from wallet_app.models import WalletTransaction
    from wyse_ussd.models import TelcoDailyAnalytics, TelcoSubscriptionPlan, TelcoUsers

    subscription_data_plans = (
        TelcoSubscriptionPlan.objects.filter(created_at__year=2024)
        .filter(created_at__month__gte=2)
        .values_list("created_at__date", flat=True)
        .distinct()
    )

    for data in subscription_data_plans:
        print("__o1_date", data)
        instance = TelcoSubscriptionPlan.objects.filter(created_at__date=data).last()

        print("instance.id", instance.id)
        celery_handle_daily_subscription_analytics(instance.id)

    telco_airtime_rewards = {}

    liberty_pay_queryset = (
        WalletTransaction.objects.filter(transaction_from="AIRTIME_PURCHASE", transaction_type="credit")
        .filter(date_created__date__year=2024)
        .filter(date_created__date__month__gte=2)
    )

    amount_of_airtime_rewarded = 0

    month_order = {1: "Jan", 2: "Feb", 3: "Mar", 4: "Apr", 5: "May"}

    number_of_winners = {}

    for trans in liberty_pay_queryset:
        n_d = f"0{trans.date_created.date().day}"
        if len(str(trans.date_created.date().day)) == 2:
            n_d = trans.date_created.date().day

        days = f"{trans.date_created.date().year}-{n_d}-{month_order.get(int(trans.date_created.date().month))}"

        telco_airtime_rewards[f"{days}_count"] = (
            WalletTransaction.objects.filter(transaction_type="credit", transaction_from="AIRTIME_PURCHASE")
            .filter(date_created__date__year=2024)
            .filter(date_created__date__month=trans.date_created.date().month)
            .filter(date_created__date__day=trans.date_created.date().day)
            .count()
        )

        try:
            user_profile = UserProfile.objects.get(phone_number=trans.wallet.user.phone_number)
        except UserProfile.DoesNotExist:
            continue

        user_telco_network = user_profile.network_provider
        n = str(user_telco_network).lower()
        if user_telco_network is None:
            try:
                telco_user = TelcoUsers.objects.using("external2").get(phone_number=trans.wallet.user.phone_number)
            except Exception:
                continue

            user_telco_network = telco_user.network

            user_profile.network_provider = user_telco_network
            user_profile.save()

            n = str(user_telco_network).lower()

        key = f"{days}_{n}"
        value = trans.amount
        if telco_airtime_rewards.get(key) is None:
            telco_airtime_rewards[(f"{days}_{n}")] = value
        else:
            # value = telco_airtime_rewards.get(key) + lib_pay.amount
            telco_airtime_rewards[key] = telco_airtime_rewards.get(key) + trans.amount

        amount_of_airtime_rewarded += value

        if number_of_winners.get(f"{days}_{n}") is None:
            number_of_winners[(f"{days}_{n}")] = 1
        else:
            key = f"{days}_{n}"
            value = number_of_winners.get(key) + 1
            number_of_winners[key] = value

    # update airtime
    print(
        f"""
        telco_airtime_rewards: {telco_airtime_rewards}
        \n\n\n\n
    """
    )
    for key, value in telco_airtime_rewards.items():
        n_key = str(key).split("_")[0]
        print("n_key", n_key)
        TelcoDailyAnalytics.objects.filter(days=n_key).update(amount_of_airtime_rewarded=value)

    cash_rewards = {}
    amount_of_cash_rewarded = 0

    # cash withdrawal
    cash_liberty_pay_queryset = (
        WalletTransaction.objects.filter(
            Q(transaction_from="SOCCER_CASH_GAME_WIN")
            | Q(transaction_from="SAL_4_LIFE_GAME_WIN")
            | Q(transaction_from="INSTANT_CASHOUT_GAME_WIN")
            | Q(transaction_from="WYSE_CASH_GAME_WIN")
            | Q(transaction_from="VIRTUAL_SOCCER_GAME_WON")
            | Q(transaction_from="BANKER_GAME_WON")
            | Q(transaction_from="QUIKA_GAME_WON")
            | Q(transaction_from="AWOOF_GAME_WON"),
            Q(transaction_type="credit"),
        )
        .filter(date_created__date__year=2024)
        .filter(date_created__date__month__gte=2)
    )

    for trans in cash_liberty_pay_queryset:
        n_d = f"0{trans.date_created.date().day}"
        if len(str(trans.date_created.date().day)) == 2:
            n_d = trans.date_created.date().day

        days = f"{trans.date_created.date().year}-{n_d}-{month_order.get(int(trans.date_created.date().month))}"
        print("cash days", days)

        cash_rewards[f"{days}_count"] = (
            WalletTransaction.objects.filter(
                Q(transaction_from="SOCCER_CASH_GAME_WIN")
                | Q(transaction_from="SAL_4_LIFE_GAME_WIN")
                | Q(transaction_from="INSTANT_CASHOUT_GAME_WIN")
                | Q(transaction_from="WYSE_CASH_GAME_WIN")
                | Q(transaction_from="VIRTUAL_SOCCER_GAME_WON")
                | Q(transaction_from="BANKER_GAME_WON")
                | Q(transaction_from="QUIKA_GAME_WON")
                | Q(transaction_from="AWOOF_GAME_WON")
                | Q(transaction_type="credit")
            )
            .filter(
                date_created__date__year=2024,
                date_created__date__month=trans.date_created.date().month,
                date_created__date__day=trans.date_created.date().day,
            )
            .count()
        )

        # if trans.date_created.date().month == 4 and trans.date_created.date().day == 7:
        #     input("press enter to continue APIL 7")

        try:
            user_profile = UserProfile.objects.get(phone_number=trans.wallet.user.phone_number)
        except UserProfile.DoesNotExist:
            print("cash winner not found", trans.wallet.user.phone_number, "\n\n")
            continue

        user_telco_network = user_profile.network_provider
        n = str(user_telco_network).lower()
        if user_telco_network is None:
            try:
                telco_user = TelcoUsers.objects.using("external2").get(phone_number=trans.wallet.user.phone_number)
            except Exception:
                print("cash telco user not found", trans.wallet.user.phone_number)
                continue

            user_telco_network = telco_user.network

            user_profile.network_provider = user_telco_network
            user_profile.save()

            n = str(user_telco_network).lower()

        key = f"{days}_{n}"

        value = trans.amount

        if cash_rewards.get(key) is None:
            cash_rewards[(f"{days}_{n}")] = value
        else:
            cash_rewards[key] = cash_rewards.get(key) + trans.amount

        if trans.date_created.date().month == 4 and trans.date_created.date().day == 7:
            print(
                f"""
            APRIL 7 cash_rewards: {cash_rewards}
            """
            )

        amount_of_cash_rewarded += value

        if number_of_winners.get(f"{days}_{n}") is None:
            number_of_winners[(f"{days}_{n}")] = 1
        else:
            key = f"{days}_{n}"
            value = number_of_winners.get(key) + 1
            number_of_winners[key] = value

    print(
        f""""
          cash_rewards: {cash_rewards}
        \n\n\n\n\n
        """
    )
    # update cash winners
    for key, value in cash_rewards.items():
        n_key = str(key).split("_")[0]
        print("n_key_cash", n_key)
        TelcoDailyAnalytics.objects.filter(days=n_key).update(amount_of_cash_rewarded=value)

    # total number of winners
    for key, value in number_of_winners.items():
        n_key = str(key).split("_")[0]
        print("winners", n_key)
        TelcoDailyAnalytics.objects.filter(days=n_key).update(total_number_of_winners=value)

    queryset = TelcoDailyAnalytics.objects.filter(created_at__year=2024).filter(created_at__month__gte=2)
    for instance in queryset:
        total_rewarded = instance.amount_of_airtime_rewarded + instance.amount_of_cash_rewarded
        instance.total_amount_of_rewards = total_rewarded
        instance.save()


@shared_task
def temp_modified_datasync():
    # import pandas as pd

    # all_subscription_plan_request = (
    #     TelcoDataSync.objects.filter(created_at__year=2024).filter(created_at__month=4).filter(created_at__day__gte=12)
    # )

    # Define the datetime threshold
    datetime_threshold = datetime(2024, 4, 14, 14, 48)  # April 14, 2024, 2:48 PM

    # Create a filter for records starting from April 14, 2024, at 2:48 PM
    all_subscription_plan_request = TelcoDataSync.objects.using("external2").filter(Q(created_at__date__gt=datetime_threshold.date()))

    for instance in all_subscription_plan_request:
        try:
            serialized_data = json.loads(instance.serilaized_data)
        except Exception:
            continue

        if instance.id == 1802304:
            break

        handle_airtime_subscription_payment(ip="************", **serialized_data)


@shared_task
def temp_update_mobid_tracker_record():
    from ads_tracker.models import MobidTracker

    unique_phones = MobidTracker.objects.all().values_list("phone_number", flat=True).distinct()

    datasync_queryset = TelcoDataSync.objects.using("external2").all()

    counter = 0

    for phone in unique_phones:
        MobidTracker.objects.filter(phone_number=phone).update(amount_paid=0, number_of_renewals=0)

        total_number_of_renewals = 0
        total_amount_paid = 0

        # print("phone", phone)

        # print("counter", counter)

        counter += 1

        if counter < 3366:
            continue

        with open("phone.txt", "a") as file:
            file.write(f"no. {counter}. {phone}\n")

        phone_datasync_queryset = datasync_queryset.filter(serilaized_data__contains=phone)

        for instance in phone_datasync_queryset:
            try:
                serialized_data = json.loads(instance.serilaized_data)
            except Exception:
                print("error in instance", instance.serilaized_data)
                continue

            update_desc = (
                serialized_data.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation", {}).get("ns2:updateDesc", None)
            )

            subscription_item = (
                serialized_data.get("soapenv:Envelope", {})
                .get("soapenv:Body", {})
                .get("ns2:syncOrderRelation", {})
                .get("ns2:extensionInfo", {})
                .get("item")
            )

            if subscription_item is None:
                continue

            successfully_charged = False

            amount_charged = 0

            for i in subscription_item:
                if i.get("key") == "autoRenew":
                    if i.get("value") == "N":
                        pass

                if i.get("key") == "chargeAmount":
                    amount_charged = int(float(i.get("value")))

                if i.get("key") == "resultCode" or i.get("key") == "resultCode":
                    if i.get("value") == "0":
                        successfully_charged = True

            if successfully_charged is True:
                if update_desc == "Modification":
                    total_number_of_renewals += 1
                    total_amount_paid += amount_charged

        MobidTracker.objects.filter(phone_number=phone).update(amount_paid=total_amount_paid, number_of_renewals=total_number_of_renewals)

    temp_update_telco_users_record.delay()


@shared_task
def temp_update_telco_users_record():
    from wyse_ussd.models import TelcoSubscriptionPlan, TelcoUsers

    telco_users = TelcoUsers.objects.using("external2").filter(paid=True).values_list("phone_number", flat=True).distinct()

    for phone in telco_users:
        good_telco_subscription_plan_for_a_user = (
            TelcoSubscriptionPlan.objects.filter(phone_number=phone).exclude(subscription_status="FAILED").exclude(subscription_status="INACTIVE")
        )
        if len(good_telco_subscription_plan_for_a_user) < 1:
            continue

        number_of_daily_subscription = 0
        number_of_one_time_subscription = 0
        number_of_daily_renewals = 0

        for instance in good_telco_subscription_plan_for_a_user:
            if instance.subscription_status == "STOPPED":
                number_of_one_time_subscription += 1
            else:
                number_of_daily_renewals += instance.number_of_renewal
                number_of_daily_subscription += 1

        TelcoUsers.objects.using("external2").filter(phone_number=phone).update(
            number_of_daily_subscription=number_of_daily_subscription,
            number_of_one_time_subscription=number_of_one_time_subscription,
            number_of_daily_renewals=number_of_daily_renewals,
        )


@shared_task
def celery_handle_nitroswitch_datasync(**data: json):
    """
    SAMPLE DATA

        {
            "action":[
                "Addition"
            ],
            "amount":[
                "0"
            ],
            "channel":[
                "USSD"
            ],
            "cptransid":[
                "504021502152406192017360004016"
            ],
            "expirydate":[
                "20240619230000"
            ],
            "msisdn":[
                "2348113720142"
            ],
            "others":[
                "GLO"
            ],
            "pcode":[
                "2146"
            ],
            "renewal":[
                "NO"
            ],
            "requestdate":[
                "20240619191736"
            ],
            "subid":[
                "2348113720142"
            ]
        }
    """

    action = data.get("action")[0]
    amount = data.get("amount")[0]
    channel = data.get("channel")[0]
    cptransid = data.get("cptransid")[0]
    expiry_date = data.get("expirydate")[0]
    msisdn = data.get("msisdn")[0]
    others = data.get("others")[0]
    pcode = data.get("pcode")[0]
    renewal = data.get("renewal")[0]
    requested_date = data.get("requestdate")[0]
    subid = data.get("subid")[0]

    data.get("service_id")
    data.get("product_id")

    is_a_renewal = True
    subscription_type = "RENEWAL"
    if "no" == str(renewal).lower():
        is_a_renewal = False
        subscription_type = "ONE_TIME"

    # Convert strings to datetime objects
    expirydate = datetime.strptime(expiry_date, "%Y%m%d%H%M%S")
    requestdate = datetime.strptime(requested_date, "%Y%m%d%H%M%S")

    # Calculate the difference between the two dates
    difference = expirydate - requestdate

    # Extract the number of days from the difference
    difference.days

    was_successfully_charged = False
    subscription_status = "INACTIVE"

    if float(amount) > 0:
        was_successfully_charged = True
        subscription_status = "ACTIVE"

    if "no" == str(renewal).lower():
        subscription_status = "STOPPED"

    daily_auto_sub_ids = ["2076", "2145", "2139"]

    other_auto_sub_ids = ["2078", "2080", "2147", "2149", "2141", "2143"]

    if float(amount) == 0:
        if pcode in daily_auto_sub_ids or pcode in other_auto_sub_ids:
            subscription_status = "ACTIVE"

    # if float(amount) > 0:
    #     if pcode in daily_auto_sub_ids:
    #         subscription_status = "DEACTIVATED"

    nitroswitch_servicer_codes = NitroSwitchData.subscription_service_codes()

    _service_ = nitroswitch_servicer_codes.get(pcode, None)
    if _service_ is None:
        print("SERVICE IS NONE")
        return

    service_name = nitroswitch_servicer_codes.get(pcode, {}).get("name")

    list_of_dates = request_and_expiry_date_helper(nitroswitch_servicer_codes.get(pcode, {}).get("validty"))

    # print("float(amount) == 0", float(amount) == 0)
    # if float(amount) == 0:
    #     # print("got here")
    #     try:
    #         nitroswitch_content_deliery_for_free_trials(phone_number=msisdn, pcode=pcode)
    #     except Exception:
    #         pass

    # nitroswitch_content_deliery_for_free_trials(phone_number=msisdn, pcode=pcode)

    print("was_successfully_charged", was_successfully_charged)
    if action == "Addition":
        NitroSwitchData.objects.create(
            phone_number=msisdn,
            action=action,
            amount=float(amount),
            channel=channel,
            cp_trans_id=cptransid,
            others=others,
            renewal=is_a_renewal,
            expiry_date=expirydate,
            request_date=requestdate,
            sub_id=subid,
            service_id=pcode,
            subscription_status=subscription_status,
            subscription_type=subscription_type,
            service_type=service_name,
        )

        NitroSwitchDailySubscription.objects.create(
            phone_number=msisdn,
            action=action,
            amount=float(amount),
            channel=channel,
            cp_trans_id=cptransid,
            service_type=service_name,
            subscription_status=subscription_status,
            subscription_type=subscription_type,
        )

        NitroSwitchDailyGameBreakdown.create_record(
            phone_number=msisdn,
            service_type=service_name,
            cp_trans_id=cptransid,
            amount=float(amount),
            list_of_dates=list_of_dates,
            service_code=pcode,
            was_successfully_charged=was_successfully_charged,
        )

    elif action == "Modification":
        nitroswitch_data_instance = NitroSwitchData.objects.filter(
            subscription_status="ACTIVE",
            phone_number=msisdn,
            service_id=pcode,
        ).last()

        if nitroswitch_data_instance is None:
            NitroSwitchData.objects.create(
                phone_number=msisdn,
                action=action,
                amount=float(amount),
                channel=channel,
                cp_trans_id=cptransid,
                others=others,
                renewal=is_a_renewal,
                expiry_date=expirydate,
                request_date=requestdate,
                sub_id=subid,
                service_id=pcode,
                subscription_status=subscription_status,
                subscription_type=subscription_type,
                service_type=service_name,
            )

            NitroSwitchDailySubscription.objects.create(
                phone_number=msisdn,
                action=action,
                amount=float(amount),
                channel=channel,
                cp_trans_id=cptransid,
                service_type=service_name,
                subscription_status=subscription_status,
                subscription_type=subscription_type,
            )

            NitroSwitchDailyGameBreakdown.create_record(
                phone_number=msisdn,
                service_type=service_name,
                cp_trans_id=cptransid,
                amount=float(amount),
                list_of_dates=list_of_dates,
                service_code=pcode,
                was_successfully_charged=was_successfully_charged,
            )

        else:
            nitroswitch_data_instance.number_of_renewal += 1
            nitroswitch_data_instance.subscription_status = subscription_status
            nitroswitch_data_instance.save()

            NitroSwitchDailySubscription.objects.create(
                phone_number=msisdn,
                action=action,
                amount=float(amount),
                channel=channel,
                cp_trans_id=cptransid,
                service_type=service_name,
                subscription_status=subscription_status,
                subscription_type=subscription_type,
            )

            # if was_successfully_charged is True:
            NitroSwitchDailyGameBreakdown.create_record(
                phone_number=msisdn,
                service_type=service_name,
                cp_trans_id=cptransid,
                amount=float(amount),
                list_of_dates=list_of_dates,
                service_code=pcode,
                no_of_renewals=nitroswitch_data_instance.number_of_renewal,
                was_successfully_charged=was_successfully_charged,
            )

    elif action == "Deletion":
        nitroswitch_data_instance = NitroSwitchData.objects.filter(
            subscription_status="ACTIVE",
            phone_number=msisdn,
            service_id=pcode,
        ).update(subscription_status="DEACTIVATED")

    # if float(amount) == 0:
    #     return "Amount charge is zero"

    # if service_id is None:
    #     return "Service ID is None"

    # if product_id is None:
    #     return "Product ID is None"


@shared_task
def celery_handle_nitroswitch_daily_subscription():
    from wyse_ussd.subscription_game_plays import (
        salary_for_life_telco_subscription_game_play,
    )

    todays_date = datetime.now().date()

    awoof_like_games = [
        "EVERWAGE DAILY AUTO",
        "EVERWAGE DAILY ONETIME",
        "EVERWAGE WEEKLY AUTO",
        "EVERWAGE WEEKLY ONETIME",
    ]

    qs = NitroSwitchDailyGameBreakdown.objects.filter(posted_on_ticket_table=False, date_to_be_posted=todays_date)
    for data in qs:
        if data.service_type in awoof_like_games:
            salary_for_life_model_instance = salary_for_life_telco_subscription_game_play(
                amount=data.amount_to_post,
                phone_number=data.phone_number,
                network="GLO",
            )

            data.posted_on_ticket_table = True
            data.date_posted = todays_date
            data.save()

            if salary_for_life_model_instance is not None:
                message = f"EVERWAGE! Your entry no: {str(salary_for_life_model_instance.ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed! https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/"  # noqa
                nitroswitch_sms_gateway(phone_number=data.phone_number, message=message, service_code=data.service_code)


@shared_task
def celery_handle_nitroswitch_sms_mo_task(data):
    """
    SAMPLE RESPONSE

    {
        "isdlr":[
            "NO"
        ],
        "msgid":[
            ""
        ],
        "msgtext":[
            "A little explanation of software as a service "
        ],
        "msisdn":[
            "2348116926356"
        ],
        "netcode":[
            "GLO"
        ],
        "shortcode":[
            "20791"
        ],
        "timestamp":[
            "20240625092142"
        ]
    }
    """
    isdlr = data.get("isdlr")
    msgid = data.get("msgid")
    msgtext = data.get("msgtext")
    msisdn = data.get("msisdn")
    netcode = data.get("netcode")
    shortcode = data.get("shortcode")
    # print("type of shortcode", shortcode[0], "\n\n")

    if isdlr is not None:
        isdlr = isdlr[0]

    if msgid is not None:
        msgid = msgid[0]

    if msgtext is not None:
        msgtext = msgtext[0]

    if msisdn is not None:
        msisdn = msisdn[0]

    if netcode is not None:
        netcode = netcode[0]

    if shortcode is not None:
        shortcode = shortcode[0]

    # print("shortcode", "\n\n\n\n")
    if shortcode != "20791":
        # print("NOT OUR SHOTCODE")
        return

    # if netcode != "GLO":
    #     # print("NOT A GLO SMS")
    #     return

    games_id_keywords = [
        "AWM",
        "AWM OT",
        "AFD",
        "AFD OT",
        "AFW",
        "AFW OT",
        "AWM",
        "AWM OT",
        "ASD",
        "ASD OT",
        "ASW",
        "ASW OT",
        "ASM",
        "ASM OT",
    ]
    if str(msgtext).strip() in games_id_keywords:
        return "FOUND KEYWORD"

    try:
        nitroswitch_sms_to_ai_subscription = NitroSwitchSmsToAiSubscription.objects.get(phone_number=msisdn)
    except Exception:
        # nitroswitch_sms_to_ai_subscription = NitroSwitchSmsToAiSubscription.objects.create(phone_number=msisdn)
        return "NO SUBSCRIPTION"

    if nitroswitch_sms_to_ai_subscription.number_of_chances == 0:
        last_subscription = NitroSwitchData.objects.filter(phone_number=msisdn).last()
        if last_subscription is None:
            return "NO LAST SUBSCRIPTION"

        if last_subscription.subscription_status == "DEACTIVATED" or last_subscription.subscription_status == "STOPPED":
            message = "Sorry, you've exhusted your chances. please reply with ASD"
            nitroswitch_sms_gateway(phone_number=msisdn, message=message, service_code=last_subscription.service_id)

            return "DEACTIVATED"

        if last_subscription.subscription_status == "ACTIVE":
            message = "Sorry, you've used up your daily chances"
            nitroswitch_sms_gateway(phone_number=msisdn, message=message, service_code=last_subscription.service_id)

            return message

    else:
        last_subscription = NitroSwitchData.objects.filter(phone_number=msisdn)
        if len(last_subscription) == 0:
            return "no last subscription"

        _last_subscription = last_subscription.last()
        active_sub = last_subscription.filter(subscription_status="ACTIVE").last()
        if active_sub is not None:
            _last_subscription = active_sub

        if nitroswitch_sms_to_ai_subscription.number_of_chances == 1:
            NitroSwitchSmsToAiSubscription.decrease_number_of_chances(phone_number=msisdn)

            ask_gpt = NitroSwitchSmsToAiSubscription.sms_to_ai_prompt(msgtext, phone=msisdn)
            ask_gpt_response = ask_gpt.get("message")

            nitroswitch_sms_gateway(phone_number=msisdn, message=ask_gpt_response, service_code=_last_subscription.service_id)

            message = "Sorry, you've used up your daily chances"
            nitroswitch_sms_gateway(phone_number=msisdn, message=message, service_code=_last_subscription.service_id)

            return message
        else:
            NitroSwitchSmsToAiSubscription.decrease_number_of_chances(phone_number=msisdn)

            ask_gpt = NitroSwitchSmsToAiSubscription.sms_to_ai_prompt(msgtext, phone=msisdn)
            ask_gpt_response = ask_gpt.get("message")

            nitroswitch_sms_gateway(phone_number=msisdn, message=ask_gpt_response, service_code=_last_subscription.service_id)

            return ask_gpt.get("message")


@shared_task
def celery_redirect_sms_mos(data):
    data.get("isdlr")
    data.get("msgid")
    msgtext = data.get("msgtext")
    data.get("msisdn")
    data.get("netcode")
    data.get("shortcode")

    if msgtext is not None:
        msgtext = msgtext[0]

    if "MAGRIC" in msgtext:
        requests.post("http://************:1333/core/sync/sms", data)

    return


@shared_task()
def process_telco_sync(data):
    import datetime

    xml_data = data

    xml_data = xml_data.encode("utf-8")

    ip = None

    jump_serialized_data = xml_data_formater(xml_data)

    then = datetime.datetime.now()
    try:
        TelcoDataSync.objects.using("external2").create(data=xml_data, serilaized_data=jump_serialized_data)
    except Exception:
        try:
            TelcoDataSync.objects.using("external2").create(data=xml_data)
        except Exception as e:
            raise e

    print("\n\n\n\n\n\n", xml_data, "\n\n\n\n\n\n")

    print(f"CREATION TIME :: {(datetime.datetime.now()-then).total_seconds()}")
    then = datetime.datetime.now()

    serialize_request_data = xml_data_formater(xml_data)
    json_data = json.loads(serialize_request_data)

    json_data.update({"request_ip": ip})
    print(f"LOADING TIME :: {(datetime.datetime.now()-then).total_seconds()}")
    then = datetime.datetime.now()

    celery_teclo_airtime_charge_datasync_handler.apply_async(
        queue=choice(
            [
                "bbc_datasync",
                "bbc_datasync1",
                "bbc_datasync2",
                "bbc_datasync3",
                "bbc_datasync4",
                "bbc_datasync5",
                "bbc_datasync6",
                "bbc_datasync7",
                "bbc_datasync8",
            ]
        ),
        args=(1, ip),
        kwargs=json_data,
    )

    # TelcoDataSyncJsons.objects.using("external2").create(serilaized_data=json_data)

    print(f"QUEUING TIME :: {(datetime.datetime.now()-then).total_seconds()}")

    return json_data


@shared_task()
def new_task_to_process_telco_sync(data):
    import datetime

    ip = None

    then = datetime.datetime.now()

    # TempTelcoDataSync.objects.create(data=data)
    try:
        TelcoDataSync.objects.using("external2").create(data=data, serilaized_data=data)
    except Exception:
        try:
            TelcoDataSync.objects.using("external2").create(data=data)
        except Exception as e:
            raise e

    print("\n\n\n\n\n\n", data, "\n\n\n\n\n\n")

    print(f"CREATION TIME :: {(datetime.datetime.now()-then).total_seconds()}")
    then = datetime.datetime.now()

    json_data = data

    json_data.update({"request_ip": ip})
    print(f"LOADING TIME :: {(datetime.datetime.now()-then).total_seconds()}")
    then = datetime.datetime.now()

    new_celery_teclo_airtime_charge_datasync_handler.apply_async(
        queue=choice(
            [
                "bbc_datasync",
                "bbc_datasync1",
                "bbc_datasync2",
                "bbc_datasync3",
                "bbc_datasync4",
                "bbc_datasync5",
                "bbc_datasync6",
                "bbc_datasync7",
                "bbc_datasync8",
            ]
        ),
        args=(ip),
        kwargs=json_data,
    )

    # TelcoDataSyncJsons.objects.using("external2").create(serilaized_data=json_data)

    print(f"QUEUING TIME :: {(datetime.datetime.now()-then).total_seconds()}")

    return json_data


def now():
    start_time = time.time()
    TelcoSubscriptionPlan.objects.create(
        phone_number="08012345678",
        game_type="INSTANT_CASHOUT",
        subscription_type="DAILY",
        subscription_status="ACTIVE",
        amount=100.00,
        number_of_renewal=2,
        service_id="service_123",
        product_id="product_456",
    )
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"Create query executed in {execution_time:.4f} seconds")


# @shared_task
# def resolve_pending_salaryfor_life_sms_async_tasks():
#     from wyse_ussd.models import PendingAsyncTask


#     pending_tasks = PendingAsyncTask.objects.filter(
#         is_treated=False, purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION, game_type = "SALARY FOR LIFE"
#     )[:5000]


@shared_task
def resolve_pending_salaryfor_life_sms_async_tasks():
    from wyse_ussd.models import PendingAsyncTask

    batch_size = 50
    max_workers = 4

    pending_tasks = PendingAsyncTask.objects.filter(
        is_treated=False, purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION, game_type="SALARY FOR LIFE"
    )

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for i in range(0, len(pending_tasks), batch_size):
            batch = pending_tasks[i : i + batch_size]

            executor.submit(process_salary_for_life_batch, batch)
            # futures.append(future)

        # for future in as_completed(futures):
        #     try:
        #         result = future.result()  # Get the result of the task
        #         print(f"Batch processed with result: {result}", should_print = True)  # Log the result
        #     except Exception as e:
        #         print(f"Error processing batch: {e}", should_print = True)


# def process_salary_for_life_batch(batch):
#     from wyse_ussd.subscription_game_plays import (
#         send_telco_subscription_game_play_confirmation,
#     )

#     for task in batch:
#         try:
#             send_telco_subscription_game_play_confirmation(
#                 task.phone_number,
#                 task.game_play_id,
#                 task.game_type,
#                 is_a_new_subscription=task.is_a_new_subscription,
#                 network=task.network,
#                 product_id=task.product_id,
#                 ticket=task.ticket,
#             )

#         except Exception:
#             pass

#         task.is_treated = True
#         task.save()


@shared_task
def resolve_pending_instant_cashout_sms_async_tasks():
    from wyse_ussd.models import PendingAsyncTask

    batch_size = 500
    max_workers = 10

    pending_tasks = PendingAsyncTask.objects.filter(
        is_treated=False, purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION, game_type="INSTANT_CASHOUT"
    )

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for i in range(0, len(pending_tasks), batch_size):
            batch = pending_tasks[i : i + batch_size]

            executor.submit(process_instant_cashout_batch, batch)


def process_instant_cashout_batch(batch):
    from wyse_ussd.subscription_game_plays import (
        send_telco_subscription_game_play_confirmation,
    )

    for task in batch:
        try:
            send_telco_subscription_game_play_confirmation(
                task.phone_number,
                task.game_play_id,
                task.game_type,
                is_a_new_subscription=task.is_a_new_subscription,
                network=task.network,
                product_id=task.product_id,
                ticket=task.ticket,
            )
        except Exception:
            pass

        task.is_treated = True
        task.save()


@shared_task
def resolve_pending_fast_fingers_sms_async_tasks():
    from wyse_ussd.models import PendingAsyncTask

    batch_size = 10
    max_workers = 10

    pending_tasks = PendingAsyncTask.objects.filter(
        is_treated=False, purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION, game_type__in=["FAST FINGERS", "WYSE CASH"]
    )

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for i in range(0, len(pending_tasks), batch_size):
            batch = pending_tasks[i : i + batch_size]

            executor.submit(process_salary_for_life_batch, batch)
            # futures.append(future)


def process_salary_for_life_batch(batch):
    from wyse_ussd.subscription_game_plays import (
        send_telco_subscription_game_play_confirmation,
    )

    for task in batch:
        print(f"Processing batch {task.phone_number}")

        try:
            send_telco_subscription_game_play_confirmation(
                task.phone_number,
                task.game_play_id,
                task.game_type,
                is_a_new_subscription=task.is_a_new_subscription,
                network=task.network,
                product_id=task.product_id,
                ticket=task.ticket,
                awoof_item=task.awoof_item,
            )

        except Exception:
            pass

        task.is_treated = True
        task.save()


@shared_task
def delete_completed_pending_async_task():
    """
    Deletes completed pending async tasks in batches and performs a vacuum operation.
    Only deletes tasks from last week and earlier, skipping the current week.

    Optimizations:
    - Deletes records in batches to avoid table locking and performance issues
    - Uses `iterator()` to fetch records in memory-efficient manner
    - Calls `VACUUM` to optimize database space after deletion
    - Filters by date to exclude current week's records
    """

    return
    # Calculate the start of the current week (Monday)
    today = datetime.now().date()
    start_of_current_week = today - timedelta(days=today.weekday())

    batch_size = 1000  # Number of rows to delete per batch
    while True:
        # Fetch batch of IDs to delete
        ids_to_delete = list(
            PendingAsyncTask.objects.filter(
                is_treated=True, created_at__lt=start_of_current_week  # Only get records before current week
            ).values_list("id", flat=True)[:batch_size]
        )

        if not ids_to_delete:
            break  # Exit loop when no more records

        # Bulk delete the batch
        PendingAsyncTask.objects.filter(id__in=ids_to_delete).delete()

    # Perform database optimization
    with connection.cursor() as cursor:
        cursor.execute("VACUUM ANALYZE wyse_ussd_pendingasynctask;")


def send_sms():
    from main.models import LottoTicket
    from wyse_ussd.models import PendingAsyncTask
    from wyse_ussd.subscription_game_plays import (
        send_telco_subscription_game_play_confirmation,
    )

    # Configure thread pool
    max_workers = 100
    batch_size = 10

    pending_tasks = list(
        PendingAsyncTask.objects.filter(
            is_treated=False,
            purpose__in=[
                PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
                PurposeChoices.GAME_WINNING_NOTIFICATION,
                PurposeChoices.SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION,
            ],
            # game_type__in=["SALARY FOR LIFE"]
        )
    )
    print(f"Total pending tasks: {len(pending_tasks)}")

    def process_sms_batch(tasks: List[PendingAsyncTask]):
        for task in tasks:
            if task.purpose == PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION:
                print(f"Processing SMS for {task.phone_number}")
                try:
                    send_telco_subscription_game_play_confirmation(
                        task.phone_number,
                        task.game_play_id,
                        task.game_type,
                        is_a_new_subscription=task.is_a_new_subscription,
                        network=task.network,
                        product_id=task.product_id,
                        ticket=task.ticket,
                        awoof_item=task.awoof_item,
                    )
                except Exception as e:
                    print(f"Error sending SMS to {task.phone_number}: {str(e)}")

            # elif task.purpose == PurposeChoices.GAME_WINNING_NOTIFICATION:
            #     try:
            #         salary_for_life_and_instant_cashout_won_sms_on_telco(
            #             phone=task.phone_number,
            #             ticket_num=task.ticket,
            #             amount=task.amount,
            #             game_play_id=task.game_play_id,
            #             lottery_type=task.game_type,
            #         )
            #     except Exception:
            #         pass

            # elif task.purpose == PurposeChoices.SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION:
            #     try:
            #         celery_handle_send_sms_to_players_that_played_via_telco_but_didnt_win.delay(batch_id=task.batch_id, lottery_type=task.game_type)
            #     except Exception:
            #         pass

            task.is_treated = True
            task.save()

            try:

                ticket = LottoTicket.objects.get(game_play_id=task.game_play_id)
                ticket.content_delivery_sms_sent = True
                ticket.save()

            except Exception:
                pass

    # Process in batches using thread pool
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for i in range(0, len(pending_tasks), batch_size):
            batch = pending_tasks[i : i + batch_size]
            executor.submit(process_sms_batch, batch)


def new_telco_sms_subscription_feature(data):
    from broad_base_communication.bbc_helper import (
        BBCTelcoAggregator,
        BBCTelcoAggregatorJsonHelper,
    )

    TelcoAggregatorNotification.objects.create(response_data_dump=data)

    message = data.get("message")
    phone_number = data.get("senderAddress")

    if message is None:
        return {"message": "message is none"}

    subscription_service_codes = {
        "ICF": ["234102200006961", "23410220000027462", "INSTANT_CASHOUT", "50"],
        "ICS": ["234102200006961", "23410220000027463", "INSTANT_CASHOUT", "75"],
        "WCF": ["234102200006965", "23410220000027470", "WYSE_CASH", "50"],
        "WCS": ["234102200006965", "23410220000027471", "WYSE_CASH", "75"],
        "SLF": ["234102200006962", "23410220000027464", "SALARY_FOR_LIFE", "50"],
        "SLS": ["234102200006962", "23410220000027465", "SALARY_FOR_LIFE", "75"],
        "FAF": ["234102200006963", "23410220000027466", "AWOOF", "50"],
        "FAS": ["234102200006963", "23410220000027467", "AWOOF", "75"],
        "SCF": ["234102200006964", "23410220000027468", "SOCCER_CASH", "50"],
        "SCS": ["234102200006964", "23410220000027469", "SOCCER_CASH", "75"],
    }

    stop_subscription_service_codes = {
        "STOP ICF": ["234102200006961", "23410220000027462", "INSTANT_CASHOUT", "50"],
        "STOP ICS": ["234102200006961", "23410220000027463", "INSTANT_CASHOUT", "75"],
        "STOP WCF": ["234102200006965", "23410220000027470", "WYSE_CASH", "50"],
        "STOP WCS": ["234102200006965", "23410220000027471", "WYSE_CASH", "75"],
        "SLF": ["234102200006962", "23410220000027464", "SALARY_FOR_LIFE", "50"],
        "STOP SLF": ["234102200006962", "23410220000027464", "SALARY_FOR_LIFE", "50"],
        "STOP SLS": ["234102200006962", "23410220000027465", "SALARY_FOR_LIFE", "75"],
        "STOP FAF": ["234102200006963", "23410220000027466", "AWOOF", "50"],
        "STOP FAS": ["234102200006963", "23410220000027467", "AWOOF", "75"],
        "STOP SCF": ["234102200006964", "23410220000027468", "SOCCER_CASH", "50"],
        "STOP SCS": ["234102200006964", "23410220000027469", "SOCCER_CASH", "75"],
    }

    capitalize_message = str(message).upper()

    if "STOP" in capitalize_message:
        service_code = stop_subscription_service_codes.get(capitalize_message, None)

        if service_code is None:
            payload = {
                "phone_number": phone_number,
            }

            ask_ai_data_model_instance_chance = AskAiData.get_instance_chance(phone_number=phone_number)
            if ask_ai_data_model_instance_chance is not None:
                if ask_ai_data_model_instance_chance.total_chance > 0:
                    ask_gpt = AwoofGameTable.ask_awoof_open_ai(message, phone_number)
                    ask_gpt_response = ask_gpt.get("message")

                    payload["message"] = f"{ask_gpt_response}"

                    ask_ai_data_model_instance_chance.total_chance -= 1
                    ask_ai_data_model_instance_chance.total_chance_used += 1
                    ask_ai_data_model_instance_chance.total_question_asked += 1
                    ask_ai_data_model_instance_chance.save()

                    payload["use_json_format"] = True
                    BBCTelcoAggregator().ask_ai_first_prompt_sms(**payload)
                else:
                    # CHARGE REQUEST TO TELCO

                    on_demand_airtime_charge_payload = {
                        "service_id": "234102200006772",
                        "product_id": "23410220000024656",
                        "phone_number": phone_number,
                        "description": "soccer cash lottery payment",
                        "amount": 100,
                        "lottery_type": "SOCCER_CASH",
                        "game_play_id": "".join(random.choices(string.ascii_uppercase + string.digits, k=10)),
                        "charge_reason": "ASK_AI",
                        "pontential_winning": 0,
                    }

                    broad_base_helper = BBCTelcoAggregator()

                    on_demand_airtime_charge_payload["use_json_format"] = True
                    on_demand_airtime_charge_payload["channel"] = "USSD"

                    broad_base_helper.telco_airtime_subscription_activation(**on_demand_airtime_charge_payload)

            return {"message": "service code not found"}

        # unsubscribe_payload = {
        #     "phone_number": phone_number,
        #     "product_id": service_code[1],
        # }

        celery_new_broadbase_deactivate_telco_subscription(
            phone_number=phone_number, product_id=service_code[1], service_id=service_code[0], use_json_format=True
        )

        return {"message": "subscription deactivated"}

    service_code = subscription_service_codes.get(capitalize_message, None)

    if service_code is None:
        payload = {
            "phone_number": phone_number,
        }

        ask_ai_data_model_instance_chance = AskAiData.get_instance_chance(phone_number=phone_number)
        if ask_ai_data_model_instance_chance is not None:
            if ask_ai_data_model_instance_chance.total_chance > 0:
                ask_gpt = AwoofGameTable.ask_awoof_open_ai(message, phone_number)
                ask_gpt_response = ask_gpt.get("message")

                print(
                    f""""
                      ASK AI MESSAGE RESPONSE
                      {ask_gpt_response}
                        \n\n\n\n\n
                      """
                )

                payload["message"] = f"{ask_gpt_response}"
                payload["sender_name"] = "20144"

                ask_ai_data_model_instance_chance.total_chance -= 1
                ask_ai_data_model_instance_chance.total_chance_used += 1
                ask_ai_data_model_instance_chance.total_question_asked += 1
                ask_ai_data_model_instance_chance.save()

                payload["use_json_format"] = True
                BBCTelcoAggregator().bbc_send_sms(**payload)
            else:
                on_demand_airtime_charge_payload = {
                    "service_id": "234102200006772",
                    "product_id": "23410220000024656",
                    "phone_number": phone_number,
                    "description": "soccer cash lottery payment",
                    "amount": 100,
                    "lottery_type": "SOCCER_CASH",
                    "game_play_id": "".join(random.choices(string.ascii_uppercase + string.digits, k=10)),
                    "charge_reason": "ASK_AI",
                    "pontential_winning": 0,
                }

                broad_base_helper = BBCTelcoAggregator()

                on_demand_airtime_charge_payload["use_json_format"] = True
                on_demand_airtime_charge_payload["channel"] = "USSD"

                broad_base_helper.telco_airtime_subscription_activation(**on_demand_airtime_charge_payload)

        return {"message": "service code not found"}

    f"{str(str(service_code[2]).replace('_', ' ')).lower()} lottery payment"

    # telco_charge_payload = {
    #     "phone_number": phone_number,
    #     "description": description,
    #     "amount": int(service_code[3]),
    #     "game_play_id": "None",
    #     "lottery_type": service_code[2],
    #     "pontential_winning": 0,
    #     "service_id": service_code[0],
    #     "product_id": service_code[1],
    #     "is_telco_subscription": True,
    #     "channel": "SMS",
    # }

    SubscriptionViaSmsDataDump.objects.create(
        amount=int(service_code[3]),
        phone_number=phone_number,
        game_type=service_code[2],
        shortcode=capitalize_message,
    )

    BBCTelcoAggregatorJsonHelper().subscription_charging_request(
        msisdn=phone_number,
        serviceId=service_code[0],
        productId=service_code[1],
        amount=float(service_code[3]),
        transactionId=str(random.randint(10**14, 10**15 - 1)),
        channelId=2,
        lottery_type=service_code[2],
    )

    return {"message": "subscription activation sent"}


@shared_task
def celery_new_broadbase_deactivate_telco_subscription(phone_number, product_id, service_id=None, use_json_format=False):
    from broad_base_communication.bbc_helper import BBCTelcoAggregatorJsonHelper

    bbc_instance = BBCTelcoAggregatorJsonHelper().unsubscription_request(productId=product_id, msisdn=phone_number, serviceId=service_id)
    return bbc_instance


@shared_task
def handle_telco_deactivation_sms():
    """
    Handles the deactivation of telco subscriptions for pending unsubscription requests.

    This function performs the following steps:
    1. Fetches all pending `TelcoUnsubscriptionRequest` records where `treated=False` using an iterator.
    2. Processes requests in batches of `batch_size` to optimize performance.
    3. Uses a `ThreadPoolExecutor` to handle multiple requests in parallel (limited by `max_workers`).
    4. Calls `TelcoSubscriptionPlan.deactivate_subscription()` for each request.
    5. Updates the `treated` status of processed requests using `bulk_update()` to reduce database queries.

    Optimizations:
    - Uses `iterator()` to prevent excessive memory usage.
    - Uses a `Queue()` to distribute tasks efficiently.
    - Limits `max_workers` to avoid ORM threading issues and database overload.

    Returns:
        None
    """
    max_workers = 20  # Reduced for stability
    batch_size = 10

    # Stream queries instead of loading 10,000 into memory
    pending_tasks = TelcoUnsubscriptionRequest.objects.filter(treated=False)[:10000].iterator()

    def process_sms_batch(tasks: List[TelcoUnsubscriptionRequest]):
        """Processes a batch of unsubscription requests and marks them as treated."""
        for task in tasks:
            try:
                TelcoSubscriptionPlan.deactivate_subscription(
                    phone_number=task.phone_number,
                    service_id=task.service_id,
                    network=task.network,
                )
            except Exception as e:
                print(f"Error sending SMS to {task.phone_number}: {str(e)}")

            task.treated = True  # Mark as treated

        # Bulk update all tasks in a single query
        TelcoUnsubscriptionRequest.objects.bulk_update(tasks, ["treated"])

    # Using a queue to avoid memory overload
    task_queue = Queue()

    def producer():
        """Fetches pending tasks and adds them to the processing queue in batches."""
        batch = []
        for task in pending_tasks:
            batch.append(task)
            if len(batch) >= batch_size:
                task_queue.put(batch[:])  # Add batch to queue
                batch.clear()

        # Process remaining batch if any
        if batch:
            task_queue.put(batch)

    def consumer():
        """Processes tasks from the queue using worker threads."""
        while not task_queue.empty():
            batch = task_queue.get()
            process_sms_batch(batch)
            task_queue.task_done()

    # Producer loads tasks into queue
    producer()

    # Consumers process batches in parallel
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for _ in range(max_workers):
            executor.submit(consumer)

    task_queue.join()  # Ensure all tasks are processed


@shared_task
def celery_handle_send_sms_to_players_that_played_via_telco_but_didnt_win(batch_id, lottery_type):
    send_sms_to_players_that_played_via_telco_but_didnt_win(batch_id=batch_id, lottery_type=lottery_type)
    return "SMS sent"
