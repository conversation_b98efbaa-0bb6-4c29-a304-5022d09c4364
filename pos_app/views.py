import ast
import json
import random
import uuid
from datetime import date, datetime, timedelta
from time import sleep
import pytz

from django.conf import settings
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password
from django.db import IntegrityError
from django.db.models import Q, Sum
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.authtoken.models import Token
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView


from account.authentication import (
    AgencyBankingFundingAuthentication,
    IsAgentRemittanceDuePermission,
    IsAgentSuspendedPermission,
    IsBlackListedPermission,
    SuperUser2Permission,
    WithdrawalPermission,
)
from account.helpers import log_save_operation
from account.models import BlackListed, User
from africa_lotto.models import AfricaLotto, AfricaLottoConstants
from africa_lotto.serializers import AfricaLottoGameHistorySerializer
from awoof_app.models import AwoofGameTable
from banker_lottery.models import BankerConstant
from main.api.api_lottery_helpers import (
    create_lottery,
    delete_lottery,
    generate_game_play_id,
    validate_pos_wyse_cash_price,
)
from main.api.serializer import DataSetSerializer
from main.helpers.helper_functions import currency_formatter
from main.helpers.vfd_disbursement_helper import VfdDisbursementHelperFunc
from main.helpers.woven_manager import WovenHelper
from main.models import (
    ConstantVariable,
    Jackpot,
    LotteryBatch,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    MaxWithdrawalThresold,
    PayoutTransactionTable,
    UserProfile,
)

# from main.signals import quika_icash, winnings
# from main.ussd.bankdb import filter_bankx
from main.ussd.helpers import Utility

# from overide_print import print
from pos_app.manage_request import ManageMobileRequest
from pos_app.multiple_play.i_cash_quika.payment_game_check import (
    pay_quika_lotto_ticket_and_check_game_result,
)
from pos_app.multiple_play.i_cash_quika.play_lottery import PlayMultipleLottoTicket
from pos_app.prices.structure import LottoObjectConverter, WyseCashObjConverter
from pos_app.retail_helpers import (
    create_retail_bonus_lottery,
    create_retail_lottery,
    validate_retail_amount,
)
from pos_app.sport_helper.create_predictions import create_prediction_ticket_object
from pos_app.sport_helper.func import verify_stake_amount
from pos_app.tasks import (
    celery_trigger_agent_reward,
    celery_winwise_agent_record_form,
    guarantor_verification_messenger,
    send_guarantor_verification_details,
)
from pos_app.utils import serialize_ticket
from prices.game_price import (
    InstantCashOutPriceModel,
    PosNewQuikaPriceModel,
    QuikaPriceModel,
    SalaryForLifePriceModel,
    WyseCashPriceModel,
)
from retail_metrics.models import WalletFundingAnalytics
from sport_app.models import FootballTable
from sport_app.serializers import SoccerCashGameSelectSerializer
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_app.models import (
    DebitCreditRecord,
    FloatWallet,
    UserWallet,
    WithdrawalTable,
)
from wallet_app.serializers import (
    PosFundDebitPlayWinningWalletSerializer,
    UpdateAgentRemittanceAmountPaidSerializer,
)
from wallet_system.models import Wallet
from wyse_ussd.helper.general_helper import (
    retail_has_enough_money_to_give_out,
)
from wyse_ussd.models import UssdLotteryPayment

from .custom_serializer import CustomLottoSerializer
from .models import (
    Agent,
    AgentConstantVariables,
    AgentFundingTable,
    AgentOnBoardingPayload,
    AgentWallet,
    AgentWalletTransaction,
    BoughtLotteryTickets,
    CreateAgentLogs,
    FailedRemittanceAgencyWalletCharge,
    LottoAgentFailedTransactionLog,
    LottoAgentGuarantorDetail,
    LottoAgentRemittanceTable,
    LottoSuperAgents,
    PosLotteryWinners,
    PosWithdrawalRequest,
    RawFundingData,
    RemunerationChargeRemittance,
    RetailTicketRequestLogs,
    TerminalIdUnAssignmentRequestLogs,
    WinwiseEmployeeSalary,
)
from .pos_helpers import (
    CustomPaginator,
    PosAgentHelper,
    PosLottoHelper,
    generate_pin,
    get_agent_detail_type,
    get_agent_sales_rep_super_agent_and_supervisor,
    lotto_data_sort_by_date,
    machine_number_serializer,
    merge_and_sort_game_history,
    new_merge_and_sort_game_history,
    remove_duplicate_game_history,
    salary_for_life_lotto_data_sort_by_date,
    salary_for_life_system_pick_serializer,
    serialize_WYSE_CASH_game_history,
    verify_agency_banking_transaction_for_vfd_funding,
    verify_game_play_id_payment,
)
from .serializers import (  # LottoTicketGameResultSerializer,; WyseCashGameResultSerializer,
    AgentDownLineCommissionGivingSerializer,
    AgentDownLinesAccountRestrictionSerializer,
    AgentLoginSerializer,
    AgentModelSerializer,
    AgentPayoutSerializer,
    AgentTransactionHistorySerializer,
    ChargeAgentOnAgencyBankingForRemittanceSerializer,
    CreateAgentSerializer,
    GameHistorySerializer,
    GamePlayIdSerializer,
    GetRetailLotteryHistorySerializer,
    IndividualMobileUserWinningWithdrawalSerializer,
    InstantCashGameResultSortSerializer,
    KYC3Serializer,
    LibertyPayVFDPayoutVerificationSerializer,
    LibertyPayWinningsPayoutSerializer,
    LotteryBatchSerializer,
    LotteryRetailCreationSerializer,
    LotteryRetailCreationViaBonusSerializer,
    LotteryRetailSoccerCashSerializer,
    LottoTicketGameResultSerializer,
    ManuallyCheckAndCreateLottoWinningsSerializer,
    ManuallyCreateWinningForATicketSerializer,
    ManuallyPreFundAgentApiViewSerializer,
    MobileUserPayoutTransactionHistory,
    MultipleLottoPlaySerializer,
    PhoneNumberSerializer,
    PosLotteryFilterLostSerializer,
    PosLotteryFilterPendingDrawSerializer,
    PosLotteryFilterWinnersSerializer,
    PosLotteryPaymentSerializer,
    PosLottoFilterLostSerializer,
    PosLottoFilterPendingDrawSerializer,
    PosLottoFilterWinnersSerializer,
    QuikaLottoPlayApiViewSerializer,
    SecondGameHistorySerializer,
    TicketDetailsSerializer,
    WinningsPayoutSerializer,
    WyseCashGameResultSerializer,
    WyseCashGameResultSortSerializer,
)


class CreateAgentRecord(APIView):
    """
    This view is used to create an agent record
    """

    serializer_class = CreateAgentSerializer

    def post(self, request):
        serializer = CreateAgentSerializer(data=request.data)

        serializer.is_valid(raise_exception=True)

        phone = serializer.validated_data.get("phone")
        email = serializer.validated_data.get("email")
        user_id = serializer.validated_data.get("user_id")
        terminal_id = serializer.validated_data.get("terminal_id")

        try:
            int(user_id)
        except ValueError:
            data = {"message": "user_id must be a number"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        agent_already_exists = Agent.objects.filter(Q(phone=phone) | Q(email=email) | Q(user_id=user_id)).exists()

        if agent_already_exists:
            data = {"message": "Agent already exists"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # create agent record in User profile model
        try:
            UserProfile.objects.create(
                phone_number=LotteryModel.format_number_from_back_add_234(phone),
                email=email,
                first_name=serializer.validated_data.get("first_name"),
                last_name=serializer.validated_data.get("last_name"),
                channel="POS",
            )

        except IntegrityError:
            pass

        # create agent record in user account model
        try:
            User.objects.create(
                email=email,
                phone=phone,
                password=make_password(serializer.validated_data.get("user_uuid")),
                first_name=serializer.validated_data.get("first_name"),
                last_name=serializer.validated_data.get("last_name"),
                channel="APP/POS",
                phone_is_verified=True,
            )

        except IntegrityError:
            pass

        # save logs

        CreateAgentLogs.objects.create(phone=phone, payload=serializer.validated_data)

        first_name = serializer.validated_data.get("first_name")
        last_name = serializer.validated_data.get("last_name")
        user_uuid = serializer.validated_data.get("user_uuid")
        address = serializer.validated_data.get("address")

        try:
            get_agent_type = get_agent_detail_type(user_id)
        except Exception:
            get_agent_type = {"status": "error", "type_of_user": "error"}

        if get_agent_type.get("status") == "error":
            data = {"message": "Failed to get agent type"}
            return Response(get_agent_type, status=status.HTTP_400_BAD_REQUEST)

        elif get_agent_type.get("type_of_user") is not None and get_agent_type.get("status") == "success":
            agent_type = get_agent_type.get("type_of_user")
            terminal_id = get_agent_type.get("terminal_id")

            AgentOnBoardingPayload.objects.create(phone=phone, payload=serializer.validated_data)

            Agent.objects.create(
                first_name=first_name,
                last_name=last_name,
                phone=phone,
                email=email,
                user_id=user_id,
                user_uuid=user_uuid,
                address=address,
                agent_type=agent_type,
                terminal_id=terminal_id,
            )

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        else:
            data = {"message": "Failed to get agent type"}
            return Response(get_agent_type, status=status.HTTP_400_BAD_REQUEST)

        # serializer.save()
        # return Response(serializer.data, status=status.HTTP_201_CREATED)


class AgentLoginView(APIView):
    """
    Login View
    """

    def post(self, request, *args, **kwargs):
        serializer = AgentLoginSerializer(data=request.data)

        serializer.is_valid(raise_exception=True)

        user_uuid = serializer.validated_data["user_uuid"]
        password = serializer.validated_data["password"]

        print("user_uuid", user_uuid)

        agent = Agent.objects.filter(user_uuid=user_uuid).last()

        if not agent:
            return Response({"message": "Agent does not exist"}, status=status.HTTP_404_NOT_FOUND)

        try:
            user = authenticate(email=agent.email, password=password)

        except Exception as e:
            if agent is None:
                return Response({"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)
            else:
                user = User.objects.filter(email=agent.email).last()
                if user is None:
                    user = User.objects.filter(phone=agent.phone).last()
                    if user is None:
                        return Response(
                            {"message": "Agent does not exist"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                # login(request, user)
                token, _ = Token.objects.get_or_create(user=user)
                return Response({"token": token.key}, status=status.HTTP_200_OK)

        if user is None:
            user = User.objects.filter(email=agent.email).last()
            if user is None:
                user = User.objects.filter(phone=agent.phone).last()
                if user is None:
                    return Response(
                        {"message": "Agent does not exist"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # login(request, user)
            token, _ = Token.objects.get_or_create(user=user)
            return Response({"token": token.key}, status=status.HTTP_200_OK)

        # login(request, user)
        token, _ = Token.objects.get_or_create(user=user)
        return Response({"token": token.key}, status=status.HTTP_200_OK)


class AgentView(APIView, ManageMobileRequest):
    """
    This view is used to get agent details
    """

    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        agent = Agent.objects.filter(email=request.user.email).last()
        if not agent:
            agent = Agent.objects.filter(phone=request.user.phone).last()
            if not agent:
                return Response(
                    {"message": "Agent does not exist"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        agent_wallet = AgentWallet.objects.filter(agent=agent).last()
        if not agent_wallet:
            agent_wallet = AgentWallet.objects.create(
                agent=agent,
                agent_name=agent.first_name + " " + agent.last_name,
                agent_phone_number=agent.phone,
                agent_email=agent.email,
            )

        agent_commission_percentage = AgentConstantVariables().get_win_agent_commission() * 100

        # store winnings

        store_winning_data = self.agent_store_winnings(agent)

        is_super_agent = False
        is_super_agent = LottoSuperAgents.objects.filter(phone=agent.phone).exists()

        data = {
            "first_name": agent.first_name,
            "last_name": agent.last_name,
            "email": agent.email,
            "phone": agent.phone,
            "user_id": agent.user_uuid,
            "winnings": agent_wallet.winnings_bal,
            "agent_commission_percentage": f"{int(agent_commission_percentage)}%",
            "agent_type": agent.agent_type,
            "is_super_agent": is_super_agent,
            "global_store_winnings": store_winning_data.get("global_store_winnings"),
            "agent_store_winnings": store_winning_data.get("agent_store_winnings"),
            "agent_total_winnings": store_winning_data.get("agent_total_winnings"),
            "today_sales": store_winning_data.get("today_sales"),
        }
        return Response(data=data, status=status.HTTP_200_OK)


class LottoView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request, channel):
        if str(channel).casefold() == "pos" or str(channel).casefold() == "mobile":
            _lotto_types = LottoTicket.LOTTO_TYPE

            list_of_lotto_types = []

            # --------------------- AGENT INSTANCE --------------------- #
            _agent_instance = Agent.objects.filter(phone=request.user.phone).last()

            if not _agent_instance:
                _agent_instance = Agent.objects.filter(email=request.user.email).last()

            if not _agent_instance:
                data = {
                    "message": "Agent does not exist",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            for _, value in _lotto_types:
                list_of_lotto_types.append(value)

            # check if the lotto type is valid
            req_lotto_type = str(request.GET.get("lotto_type")).upper()

            # check if this user is not restricted from playing instant cashout
            if str(req_lotto_type).upper() == "INSTANT_CASHOUT":
                if AgentConstantVariables().get_is_instant_cashout_enabled() is False:
                    dev_test_users = str(settings.DEV_TEST_USERS).split(",")

                    # print(
                    #     f"""
                    # dev_test_users: {dev_test_users}
                    # \n\n\n\n
                    # """
                    # )

                    if _agent_instance.email not in dev_test_users:
                        data = {
                            "message": "Instant cashout is undergoing maintenance",
                        }

                        return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                if BlackListed().can_play_instant_cashout(email=_agent_instance.email, phone=_agent_instance.phone) is False:
                    data = {
                        "message": "Instant cashout is not accessible for you at the moment",
                    }

                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if str(req_lotto_type).upper() == "SALARY_FOR_LIFE":
                if BlackListed().can_play_salary_for_life(email=_agent_instance.email, phone=_agent_instance.phone) is False:
                    data = {
                        "message": "salary for life is not accessible for you at the moment",
                    }

                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if req_lotto_type not in list_of_lotto_types:
                data = {
                    "message": "Invalid lotto type",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            data = dict(request.data)

            serializer = CustomLottoSerializer(data=data)
            serializer.is_valid(lottery_type=req_lotto_type, raise_exception=True)

            # return Response(data = {"message": "ok"}, status=status.HTTP_200_OK)

            # get or create user profile
            phone = serializer.data.get("phone_number")

            if phone is None or phone == "":
                phone = _agent_instance.phone

            serializer.data["phone_number"] = phone

            _formated_phone = LotteryModel.format_number_from_back_add_234(phone)

            # CHECK IF PLAYER NUMBER IS REQUIRED
            if AgentConstantVariables().get_phone_number_is_required() is True:
                if _formated_phone == _agent_instance.phone:
                    data = {
                        "message": "You cannot use your own phone number",
                    }
                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            try:
                player, created = UserProfile.objects.get_or_create(phone_number=_formated_phone, channel="POS")
            except Exception:
                player = UserProfile.objects.filter(phone_number=_formated_phone)

                if player.exists():
                    player = player.last()
                    player.channel = "POS"
                    player.save()
                else:
                    player = UserProfile.objects.create(phone_number=_formated_phone, channel="POS")

            if str(channel).casefold() == "pos":
                # get agent_instance
                _agent_instance = Agent.objects.filter(phone=request.user.phone).last()

                if not _agent_instance:
                    _agent_instance = Agent.objects.filter(email=request.user.email).last()

                if not _agent_instance:
                    data = {
                        "message": "Agent does not exist",
                    }
                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                data = PosLottoHelper.play_lotto(player, _agent_instance, serializer.data, req_lotto_type, channel)

                if data.get("status") == "error":
                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                return Response(data=data, status=status.HTTP_200_OK)

            return Response(status=status.HTTP_200_OK)

        data = {"message": "Invalid channel"}
        return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, channel):
        lottery_type = request.GET.get("lotto_type")
        id = request.GET.get("id")

        agent_profile = Agent.objects.filter(email=request.user.email).last()
        if not agent_profile:
            agent_profile = Agent.objects.filter(phone=request.user.phone).last()

            if not agent_profile:
                return Response(
                    {"message": "Agent does not exist"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        if lottery_type is None or lottery_type == "":
            return Response(
                data={"message": "lottery_type is required in the query parameters"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if lottery_type.upper() not in [
            "WYSE_CASH",
            "INSTANT_CASHOUT",
            "SALARY_FOR_LIFE",
        ]:
            return Response(
                data={"message": "Invalid lottery type"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if id is None or id == "":
            return Response(
                data={"message": "id is required in the query parameters"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if lottery_type.upper() == "SALARY_FOR_LIFE" or lottery_type.upper() == "INSTANT_CASHOUT":
            get_lottery = LottoTicket.objects.filter(id=int(id), agent_profile=agent_profile).last()

            if get_lottery is not None:
                lotto_model_qs = LottoTicket.objects.filter(game_play_id=get_lottery.game_play_id)

                if lottery_type.upper() == "SALARY_FOR_LIFE":
                    number_of_ticket = lotto_model_qs.count() - 1
                    expected_amount = (
                        LottoTicket.salary_for_life_stake_amount_pontential_winning(number_of_ticket).get("stake_amount") / number_of_ticket
                    )
                    potential_winning = LottoTicket.salary_for_life_stake_amount_pontential_winning(number_of_ticket).get("total_winning_amount")

                    stake_amount = (
                        LottoTicket.salary_for_life_stake_amount_pontential_winning(number_of_ticket).get("stake_amount") / number_of_ticket
                    )

                elif lottery_type.upper() == "INSTANT_CASHOUT":
                    number_of_ticket = lotto_model_qs.count() - 1
                    expected_amount = LottoTicket.instant_stake_amount_pontential_winning(number_of_ticket).get("stake_amount") / number_of_ticket
                    potential_winning = LottoTicket.instant_stake_amount_pontential_winning(number_of_ticket).get("total_winning_amount")

                    stake_amount = LottoTicket.instant_stake_amount_pontential_winning(number_of_ticket).get("stake_amount") / number_of_ticket

                lotto_model_qs.update(
                    expected_amount=expected_amount,
                    potential_winning=potential_winning,
                    stake_amount=stake_amount,
                    number_of_ticket=number_of_ticket,
                )

                get_lottery.delete()

                return Response(
                    data={"message": "Lottery ticket deleted successfully"},
                    status=status.HTTP_200_OK,
                )

            else:
                return Response(
                    data={"message": "Lottery ticket not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        elif lottery_type.upper() == "WYSE_CASH":
            if not LotteryModel.objects.filter(id=int(id), agent_profile=agent_profile).exists():
                return Response(
                    data={"message": "Lottery ticket not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            delete_lottery_response = delete_lottery(id)

            if delete_lottery_response is None:
                data = {
                    "message": "id is not found",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            return Response(data={"message": "success"}, status=status.HTTP_200_OK)






class NewLottoView(APIView):
    """
    API view for lottery ticket operations (creation and deletion).
    
    Handles lottery ticket creation through POST requests and
    deletion through DELETE requests.
    """
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request, channel):
        """Create lottery tickets based on channel and lottery type."""
        # Validate channel
        if not self._is_valid_channel(channel):
            return Response(
                {"message": "Invalid channel"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Get agent instance
        agent_instance = self._get_agent_instance(request)
        if not agent_instance:
            return Response(
                {"message": "Agent does not exist"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Validate lottery type
        lotto_type = str(request.GET.get("lotto_type", "")).upper()
        validation_response = self._validate_lottery_type(lotto_type, agent_instance)
        if validation_response:
            return validation_response
            
        # Process request data
        data = dict(request.data)
        serializer = CustomLottoSerializer(data=data)
        serializer.is_valid(lottery_type=lotto_type, raise_exception=True)
        
        # Get or create user profile
        player = self._get_or_create_player(serializer.data, agent_instance)
        if isinstance(player, Response):
            return player
        
        result = PosLottoHelper.new_play_lotto(player, agent_instance, serializer.data, lotto_type, channel)
            
        # Process lottery play request
        # try:
            
        #     result = PosLottoHelper.new_play_lotto(player, agent_instance, serializer.data, lotto_type, channel)
        # except IntegrityError:
        #     return Response(
        #         {"message": "Unable to process your request at the moment"},
        #         status=status.HTTP_400_BAD_REQUEST
        #     )
        
        # except Exception as e:
        #     return Response(
        #         {"message": str(e)},
        #         status=status.HTTP_400_BAD_REQUEST
        #     )
        
        if result.get("status") == "error":
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)
            
        return Response(data=result, status=status.HTTP_200_OK)

    def delete(self, request, channel):
        """Delete lottery tickets."""
        # Get request parameters
        lottery_type = request.GET.get("lotto_type")
        ticket_id = request.GET.get("id")
        
        # Validate parameters
        validation_response = self._validate_delete_params(lottery_type, ticket_id)
        if validation_response:
            return validation_response
            
        # Get agent profile
        agent_profile = self._get_agent_instance(request)
        if not agent_profile:
            return Response(
                {"message": "Agent does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )
            
        # Process deletion based on lottery type
        if lottery_type.upper() in ["SALARY_FOR_LIFE", "INSTANT_CASHOUT"]:
            return self._delete_standard_lottery(lottery_type.upper(), ticket_id, agent_profile)
        elif lottery_type.upper() == "WYSE_CASH":
            return self._delete_wyse_cash_lottery(ticket_id, agent_profile)
            
        return Response(
            {"message": "Invalid lottery type"},
            status=status.HTTP_400_BAD_REQUEST
        )
        
    def _is_valid_channel(self, channel):
        """Check if the channel is valid."""
        return str(channel).casefold() in ["pos", "mobile"]
        
    def _get_agent_instance(self, request):
        """Get agent instance from user credentials."""
        agent_instance = Agent.objects.filter(phone=request.user.phone).last()
        if not agent_instance:
            agent_instance = Agent.objects.filter(email=request.user.email).last()
        return agent_instance
        
    def _validate_lottery_type(self, lotto_type, agent_instance):
        """
        Validate if the lottery type is valid and accessible to the agent.
        Returns Response object if validation fails, None if validation passes.
        """

        available_lotto_types = AgentConstantVariables.objects.last().avialable_games.all()
        available_lotto_types = [lotto_type.game_type for lotto_type in available_lotto_types]

        print("available_lotto_types", available_lotto_types, "\n\n\n")

        if lotto_type not in available_lotto_types:
            return Response(
                {"message": "This game is temporarily unavailable"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if lottery type is valid
        lotto_types = [value for _, value in LottoTicket.LOTTO_TYPE]
        if lotto_type not in lotto_types:
            return Response(
                {"message": "Invalid lotto type"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Check restrictions for INSTANT_CASHOUT
        if lotto_type == "INSTANT_CASHOUT":
            # Check if instant cashout is enabled
            if not AgentConstantVariables().get_is_instant_cashout_enabled():
                # Allow access only for test users if disabled
                dev_test_users = str(settings.DEV_TEST_USERS).split(",")
                if agent_instance.email not in dev_test_users:
                    return Response(
                        {"message": "Instant cashout is undergoing maintenance"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                    
            # Check if agent is blacklisted for instant cashout
            if not BlackListed().can_play_instant_cashout(
                email=agent_instance.email, 
                phone=agent_instance.phone
            ):
                return Response(
                    {"message": "Instant cashout is not accessible for you at the moment"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        # Check restrictions for SALARY_FOR_LIFE
        if lotto_type == "SALARY_FOR_LIFE":
        
            if not BlackListed().can_play_salary_for_life(
                email=agent_instance.email, 
                phone=agent_instance.phone
            ):
                return Response(
                    {"message": "Salary for life is not accessible for you at the moment"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        return None
        
    def _get_or_create_player(self, data, agent_instance):
        """
        Get or create player profile.
        Returns Response object if validation fails, player object if successful.
        """
        phone = data.get("phone_number", "")
        
        # Use agent's phone if not provided
        if not phone:
            phone = agent_instance.phone
            data["phone_number"] = phone
            
        formatted_phone = LotteryModel.format_number_from_back_add_234(phone)
        
        # Check if agent is using their own number when not allowed
        if (AgentConstantVariables().get_phone_number_is_required() and 
            formatted_phone == agent_instance.phone):
            return Response(
                {"message": "You cannot use your own phone number"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Get or create player profile
        try:
            player, created = UserProfile.objects.get_or_create(
                phone_number=formatted_phone, 
                channel="POS"
            )
        except Exception:
            # Handle exception case
            player = UserProfile.objects.filter(phone_number=formatted_phone)
            
            if player.exists():
                player = player.last()
                player.channel = "POS"
                player.save()
            else:
                player = UserProfile.objects.create(
                    phone_number=formatted_phone, 
                    channel="POS"
                )
                
        return player
        
    def _validate_delete_params(self, lottery_type, ticket_id):
        """Validate delete request parameters."""
        if not lottery_type:
            return Response(
                {"message": "lottery_type is required in the query parameters"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        if lottery_type.upper() not in ["WYSE_CASH", "INSTANT_CASHOUT", "SALARY_FOR_LIFE"]:
            return Response(
                {"message": "Invalid lottery type"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        if not ticket_id:
            return Response(
                {"message": "id is required in the query parameters"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        return None
        
    def _delete_standard_lottery(self, lottery_type, ticket_id, agent_profile):
        """Delete SALARY_FOR_LIFE or INSTANT_CASHOUT lottery tickets."""
        get_lottery = LottoTicket.objects.filter(
            id=int(ticket_id), 
            agent_profile=agent_profile
        ).last()
        
        if not get_lottery:
            return Response(
                {"message": "Lottery ticket not found"},
                status=status.HTTP_404_NOT_FOUND
            )
            
        # Get all tickets with the same game play ID
        lotto_model_qs = LottoTicket.objects.filter(game_play_id=get_lottery.game_play_id)
        number_of_ticket = lotto_model_qs.count() - 1
        
        # Calculate new values based on lottery type
        if lottery_type == "SALARY_FOR_LIFE":
            values = self._calculate_salary_for_life_values(number_of_ticket)
        else:  # INSTANT_CASHOUT
            values = self._calculate_instant_cashout_values(number_of_ticket)
            
        # Update remaining tickets
        lotto_model_qs.update(
            expected_amount=values["expected_amount"],
            potential_winning=values["potential_winning"],
            stake_amount=values["stake_amount"],
            number_of_ticket=number_of_ticket
        )
        
        # Delete the ticket
        get_lottery.delete()
        
        return Response(
            {"message": "Lottery ticket deleted successfully"},
            status=status.HTTP_200_OK
        )
        
    def _calculate_salary_for_life_values(self, number_of_ticket):
        """Calculate values for SALARY_FOR_LIFE after deleting a ticket."""
        stake_calc = LottoTicket.salary_for_life_stake_amount_pontential_winning(number_of_ticket)
        
        return {
            "expected_amount": stake_calc.get("stake_amount") / number_of_ticket if number_of_ticket > 0 else 0,
            "potential_winning": stake_calc.get("total_winning_amount"),
            "stake_amount": stake_calc.get("stake_amount") / number_of_ticket if number_of_ticket > 0 else 0
        }
        
    def _calculate_instant_cashout_values(self, number_of_ticket):
        """Calculate values for INSTANT_CASHOUT after deleting a ticket."""
        stake_calc = LottoTicket.instant_stake_amount_pontential_winning(number_of_ticket)
        
        return {
            "expected_amount": stake_calc.get("stake_amount") / number_of_ticket if number_of_ticket > 0 else 0,
            "potential_winning": stake_calc.get("total_winning_amount"),
            "stake_amount": stake_calc.get("stake_amount") / number_of_ticket if number_of_ticket > 0 else 0
        }
        
    def _delete_wyse_cash_lottery(self, ticket_id, agent_profile):
        """Delete WYSE_CASH lottery tickets."""
        # Check if ticket exists
        if not LotteryModel.objects.filter(
            id=int(ticket_id), 
            agent_profile=agent_profile
        ).exists():
            return Response(
                {"message": "Lottery ticket not found"},
                status=status.HTTP_404_NOT_FOUND
            )
            
        # Try to delete the lottery
        delete_result = delete_lottery(ticket_id)
        
        if delete_result is None:
            return Response(
                {"message": "id is not found"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        return Response(
            {"message": "success"},
            status=status.HTTP_200_OK
        )




class WhyseCashLottery(APIView, ManageMobileRequest):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request, channel):
        """
        This endpoint is used to generate whyse cash lottery
        """

        generate_lottery_nums = self.new_agent_wyse_cash_game_generation()

        return Response(generate_lottery_nums, status=status.HTTP_200_OK)

    def post(self, request, channel):
        if not str(channel).casefold() in ["pos", "mobile"]:
            data = {"message": "Invalid channel"}

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        _agent_instance = Agent.objects.filter(email=request.user.email).last()

        if not _agent_instance:
            _agent_instance = Agent.objects.filter(phone=request.user.phone).last()

        if not _agent_instance:
            data = {
                "message": "Agent does not exist",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        phone_number = request.GET.get("phone_number")

        using_agent_phone_number = False

        if AgentConstantVariables().get_phone_number_is_required() is True and (phone_number is None or phone_number == ""):
            data = {"message": "Phone number is required in the query params"}

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if AgentConstantVariables().get_phone_number_is_required() is False and phone_number is None or phone_number == "":
            phone_number = _agent_instance.phone
            using_agent_phone_number = True

        if using_agent_phone_number is False:
            # validate phone number
            if len(phone_number) < 11:
                data = {"message": "Invalid phone number"}

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            # check if phone number has special characters
            if not phone_number.isnumeric():
                data = {"message": "Invalid phone number"}

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if not Agent.check_if_phone_number_is_valid(phone_number):
                data = {"message": "Phone number is invalid"}

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        serializer = DataSetSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # check if phone number passed to the query params is saved in our database
        _formated_phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        # CHECK IF PLAYER NUMBER IS REQUIRED
        if AgentConstantVariables().get_phone_number_is_required() is True:
            if _formated_phone_number == _agent_instance.phone:
                data = {
                    "message": "You cannot use your own phone number",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        player_qs = UserProfile.objects.filter(phone_number=_formated_phone_number)

        if not player_qs.exists():
            data = {"message": "Phone number not found"}

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # chekc if there's active batch running
        get_current_batch = LotteryBatch.objects.filter(is_active=True, lottery_type="WYSE_CASH").last()

        if get_current_batch is None:
            # data = {"status": "error", "message": "No active batch found"}

            # return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            get_current_batch = LotteryBatch.objects.create(is_active=True, lottery_type="WYSE_CASH")

        ten = serializer.validated_data.get("ten")
        fifty = serializer.validated_data.get("fifty")
        hundred = serializer.validated_data.get("hundred")
        two_hundred = serializer.validated_data.get("two_hundred")

        all_data = [ten, fifty, hundred, two_hundred]

        # Validate price
        if validate_pos_wyse_cash_price(all_data) is False:
            return Response(data={"message": "Invalid amount"}, status=status.HTTP_400_BAD_REQUEST)

        lottery = create_lottery(
            _agent_instance,
            all_data,
            from_pos=True,
            player_instance=player_qs.last(),
            channel="POS_AGENT",
        )

        _serialize_WYSE_CASH = PosLottoHelper.serilaize_WYSE_CASH_data(
            lottery,
            _formated_phone_number,
            _agent_instance,
            using_agent_phone_number=using_agent_phone_number,
        )

        # data = {
        #     "message": "success",
        #     "data": _serialize_WYSE_CASH
        # }

        return Response(data=_serialize_WYSE_CASH, status=status.HTTP_200_OK)


class CommissionPayout(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serilaizer = AgentPayoutSerializer(data=request.data)
        serilaizer.is_valid(raise_exception=True)

        amount = serilaizer.validated_data.get("amount")
        account_number = serilaizer.validated_data.get("account_number")
        bank_code = serilaizer.validated_data.get("bank_code")
        narration = serilaizer.validated_data.get("narration")

        # verify agent transaction pin

        # agent wallet
        agent_wallet = AgentWallet.objects.filter(agent=request.user).last()

        if agent_wallet is None:
            data = {"message": "You have no withdrawal wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        _wallet_to_charge = agent_wallet.commission_bal

        if amount > _wallet_to_charge:
            data = {"message": "Insufficient balance in your withdrawable wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if amount < 500:
            data = {"message": "Minimum withdrawal amount is 500"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # VERIFY BANK DETAILS
        paystack = PaymentGateway()
        verify_bank_response = paystack.fetch_account_name(account_number=account_number, bank_code=bank_code)

        if isinstance(verify_bank_response, dict):
            print(verify_bank_response)

            if verify_bank_response.get("status") is False:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            name = verify_bank_response.get("data").get("account_name")
            reference = "withdraw-{}".format(uuid.uuid4())

            _filter_bank_details = "filter_bank(cbn_code=bank_code)"

            if _filter_bank_details is None:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            _bank_code = _filter_bank_details.get("cbn_code")

            # disbursement payload
            _narration = "disbursement"
            if narration is not None and narration != "":
                _narration = narration
            woven_payload = {
                "beneficiary_account_name": name,
                "beneficiary_nuban": account_number,
                "beneficiary_bank_code": _bank_code,
                "bank_code_scheme": "NIP",
                "currency_code": "NGN",
                "narration": _narration,
                "callback_url": "",
                "reference": reference,
                "amount": amount,
            }

            _bank_name = _filter_bank_details.get("name")

            _withdraw_table_instance = WithdrawalTable.objects.create(
                source="WOVEN",
                amount=amount,
                phone=request.user.phone,
                payout_trans_ref=reference,
                name=name,
                source_response_payload=woven_payload,
                bank_name=_bank_name,
                bank_code=_bank_code,
                channel="POS",
                agent_wallet_type="COMMISSION",
            )

            woven_payload["source_account"] = settings.WOVEN_DISBURSEMENT_SOURCE_ACCOUNT
            woven_payload["PIN"] = settings.WOVEN_DISBURSEMENT_PAYMENT_PIN

            # update agent wallet
            agent_wallet.commission_bal -= amount
            agent_wallet.transaction_from = "COMMISSION"
            agent_wallet.save()

            # initiate payout
            _woven_helper = WovenHelper()

            payout_response = _woven_helper.initaite_payout_to_winners(**woven_payload)

            _withdraw_table_instance.source_response_payload = payout_response
            _withdraw_table_instance.save()

            data = {
                "status": "success",
                "message": "Withdrawal initiated",
                "transaction_ref": reference,
                "agent_id": request.user.user_uuid,
                "account_name": name,
                "bank_name": _bank_name,
                "amount": amount,
                "date": datetime.now(),
            }
            return Response(data, status=status.HTTP_200_OK)

        else:
            data = {"message": "Sorry we could not process your request"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class WinningsPayout(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        WithdrawalPermission,
    ]

    def post(self, request):
        serilaizer = WinningsPayoutSerializer(data=request.data)
        serilaizer.is_valid(raise_exception=True)

        amount = serilaizer.validated_data.get("amount")
        account_number = serilaizer.validated_data.get("account_number")
        bank_code = serilaizer.validated_data.get("bank_code")
        serilaizer.validated_data.get("narration")

        game_id = str(serilaizer.validated_data.get("game_id")).strip()
        pin = str(serilaizer.validated_data.get("pin")).strip()
        phone = str(serilaizer.validated_data.get("phone")).strip()
        agent_transaction_pin = serilaizer.validated_data.get("agent_transaction_pin")

        _formated_phone = LotteryModel.format_number_from_back_add_234(phone)

        # get agent_instance
        _agent_instance = Agent.objects.filter(phone=request.user.phone).last()

        if not _agent_instance:
            _agent_instance = Agent.objects.filter(email=request.user.email).last()

        if not _agent_instance:
            data = {
                "message": "Agent does not exist",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        pos_lottery_winner_qs = PosLotteryWinners.objects.filter(
            agent=_agent_instance,
            game_id__iexact=game_id,
            player__phone_number=_formated_phone,
        )

        # CHECK IF PLAYER NUMBER IS REQUIRED
        if AgentConstantVariables().get_phone_number_is_required() is True:
            if _formated_phone == _agent_instance.phone:
                data = {
                    "message": "You cannot use your own phone number",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # -------- CHECK FOR PAYOUT STATUS -------------
        if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
            data = {
                "message": "Payout is not available at the moment",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if pos_lottery_winner_qs.exists():
            pos_lottery_winner = pos_lottery_winner_qs.last()

            pos_withdrawal_request = PosWithdrawalRequest.objects.filter(agent=_agent_instance, game_id=game_id).last()
            if pos_withdrawal_request is not None:
                pos_withdrawal_request.stage_two_payload = serilaizer.validated_data
                pos_withdrawal_request.stage = "STAGE2"
                pos_withdrawal_request.save()

            print("pos_lottery_winner.amount_won", pos_lottery_winner.amount_won)
            print("amount", amount)

            if pos_lottery_winner.is_win_claimed is True:
                data = {"message": "You have already claimed your winnings"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if BlackListed().can_withdraw(phone=_formated_phone) is True:
                data = {"message": "You can't initiate withdrawal with this phone number"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if pin != pos_lottery_winner.pin:
                data = {
                    "message": "Invalid pin",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if pos_lottery_winner.withdrawl_initiated is True:
                data = {"message": "Please wait for your withdrawal to be processed"}

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if pos_lottery_winner.amount_won != amount:
                return Response(
                    data={"message": "Invalid amount. please enter correct amount"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not PosLotteryWinners.validate_pin(
                agent=_agent_instance,
                pin=pin,
                game_id=game_id,
                player_phone=_formated_phone,
            ):
                data = {"message": "Invalid pin"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # agent wallet
            agent_wallet = AgentWallet.objects.filter(agent=_agent_instance).last()

            if agent_wallet is None:
                data = {"message": "You have no withdrawal wallet"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # ------------------------------ VALIDATE AGENT LIBERTY PAY TRANSACTION PIN ------------------------------ #

            pos_agent_helper = PosAgentHelper(agent_instance=_agent_instance, amount=amount)

            if pos_agent_helper.verify_agent_transaction_pin(agent_transaction_pin) is False:
                data = {
                    "message": "Invalid transaction pin",
                }

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            # CHECK IF USER HAS EXCEEDED THRESOLD
            if (
                _agent_instance.phone not in ConstantVariable.get_constant_variable().get("users_exempted_from_limit")
            ) and MaxWithdrawalThresold.check_and_update_thresold(
                phone_number=_agent_instance.phone, amount=float(pos_lottery_winner.amount_won)
            ) is True:
                data = {
                    "message": "Please, try withdrawal in the next 24 hours",
                }

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            _wallet_to_charge = agent_wallet.winnings_bal

            # check the amount
            if amount != pos_lottery_winner.amount_won:
                data = {"message": "Invalid amount"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if amount > _wallet_to_charge:
                data = {"message": "Insufficient balance in your withdrawable wallet"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # check general wallet and make sure we've the amount tto give out
            if retail_has_enough_money_to_give_out(amount) is False:
                data = {"message": "We're sorry, an error occured while processing your request. Please try again later"}

                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            amount = pos_lottery_winner.amount_won

            return PayoutTransactionTable().agent_buddy_disbursement(
                amount=amount,
                phone=_formated_phone,
                account_number=account_number,
                bank_code=bank_code,
                agent_wallet_instance=agent_wallet,
                _agent_instance=_agent_instance,
                narration="LOTTO_WINNING_WITHDRAWAL",
                pos_lottery_winner_instance=pos_lottery_winner,
                game_id=game_id,
            )

        else:
            data = {"message": "Sorry we could not find your ticket"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class WinningsPayoutToAgencyBanking(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        WithdrawalPermission,
    ]

    def post(self, request):
        serilaizer = LibertyPayWinningsPayoutSerializer(data=request.data)
        serilaizer.is_valid(raise_exception=True)

        # amount = serilaizer.validated_data.get("amount")
        # narration = serilaizer.validated_data.get("narration")

        game_id = str(serilaizer.validated_data.get("game_id")).strip()
        pin = serilaizer.validated_data.get("pin")
        # phone = serilaizer.validated_data.get("phone")
        agent_transaction_pin = serilaizer.validated_data.get("agent_transaction_pin")

        # _formated_phone = LotteryModel.format_number_from_back_add_234(phone)

        # get agent_instance
        _agent_instance = Agent.objects.filter(phone=request.user.phone).last()

        if not _agent_instance:
            _agent_instance = Agent.objects.filter(email=request.user.email).last()

        if not _agent_instance:
            data = {
                "message": "Agent does not exist",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        pos_lottery_winner_qs = PosLotteryWinners.objects.filter(
            agent=_agent_instance,
            game_id__iexact=game_id,
            pin=pin,
        )

        if not pos_lottery_winner_qs.exists():
            data = {"message": "Sorry we could not find your ticket"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        pos_lottery_winner = pos_lottery_winner_qs.last()

        amount = pos_lottery_winner.amount_won

        # ---------------------- check if this cashout is awoof coupon ticket game claim
        awoof_ticket_data = AwoofGameTable().awoof_pos_won_ticket_confirmation(phone=_agent_instance.phone, game_id=game_id, pin=pin)

        if awoof_ticket_data.get("status") is True:
            if awoof_ticket_data.get("data") is None:
                data = {
                    "status": "error",
                    "message": awoof_ticket_data.get("message"),
                }

                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            data = {
                "status": "success",
                "message": awoof_ticket_data.get("message"),
                "data": awoof_ticket_data.get("data"),
            }

            print(
                f"""
            data: {data}
            \n\n\n\n\n
            """
            )

            return Response(data, status=status.HTTP_200_OK)

        # ---------------------------- END AWOOF COUPON TICKET CLAIM

        if pos_lottery_winner.is_win_claimed is True:
            data = {"message": "You have already claimed your winnings"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if pin != pos_lottery_winner.pin:
            data = {
                "message": "Invalid pin",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if pos_lottery_winner.amount_won != amount:
            return Response(
                data={"message": "Invalid amount. please enter correct amount"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check our VFD mirrow float wallet and make sure we've the amount to give out
        if FloatWallet.get_float_wallet(source="RETAIL_RTP_WALLET").amount < amount:
            data = {"message": "We're sorry, an error occured while processing your request. Please try again later"}

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # agent wallet
        agent_wallet = AgentWallet.objects.filter(agent=_agent_instance).last()

        if agent_wallet is None:
            data = {"message": "You have no withdrawal wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        _wallet_to_charge = agent_wallet.winnings_bal

        # ------------------------------ VALIDATE AGENT LIBERTY PAY TRANSACTION PIN ------------------------------ #

        pos_agent_helper = PosAgentHelper(agent_instance=_agent_instance, amount=amount)

        if pos_agent_helper.verify_agent_transaction_pin(agent_transaction_pin) is False:
            data = {
                "message": "Invalid transaction pin",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # ------------------------------ VALIDATE AGENT LIBERTY PAY TRANSACTION PIN ------------------------------ #

        if (
            _agent_instance.phone not in ConstantVariable.get_constant_variable().get("users_exempted_from_limit")
        ) and MaxWithdrawalThresold.check_and_update_thresold(
            phone_number=_agent_instance.phone, amount=float(pos_lottery_winner.amount_won)
        ) is True:
            data = {
                "message": "Please, try withdrawal in the next 24 hours",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        print("DONE CHECKING WITHDRAWAL THRESOLD")

        # check the amount
        if amount != pos_lottery_winner.amount_won:
            data = {"message": "Invalid amount"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if amount > _wallet_to_charge:
            data = {"message": "Insufficient balance in your withdrawable wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        print("KAKKKKKA 1")
        # check general wallet and make sure we've the amount tto give out
        if retail_has_enough_money_to_give_out(amount) is False:
            data = {"message": "We're sorry, an error occured while processing your request. Please try again later"}

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # check our VFD mirrow float wallet and make sure we've the amount to give out
        if FloatWallet.get_float_wallet(source="RETAIL_RTP_WALLET").amount < amount:
            data = {"message": "Withdrawal network error. Please try again later"}

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        print("KAKKKKKA 2")
        # check if agent has already claimed this amount
        _pos_lottery_winner = PayoutTransactionTable.objects.filter(game_play_id=game_id, disbursed=True)
        if _pos_lottery_winner.exists():
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=agent_wallet.agent.phone,
                amount=amount,
                channel="POS",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="DEBIT",
            )

            wallet_payload = {
                "transaction_from": "SPECIAL_CASE",
            }

            UserWallet.deduct_wallet(
                user=agent_wallet.agent,
                amount=int(amount),
                channel="POS",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="WINNINGS_WALLET",
                **wallet_payload,
            )

            # agent_wallet.winnings_bal -= amount
            # agent_wallet.transaction_from = "SPECIAL_CASE"
            # agent_wallet.phone_number = phone
            # agent_wallet.save()

            pos_lottery_winner.is_win_claimed = True
            pos_lottery_winner.withdrawl_initiated = True
            pos_lottery_winner.payout_successful = True
            pos_lottery_winner.payout_verified = True
            pos_lottery_winner.save()

            data = {"message": "ticket already claimed."}

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        print("KAKKKKKA 3")
        # update agent wallet
        # agent_wallet.winnings_bal -= amount
        # agent_wallet.transaction_from = "WINNINGS_WITHDRAW"
        # agent_wallet.phone_number = phone
        # agent_wallet.save()

        payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent_wallet.agent.phone,
            amount=amount,
            channel="POS",
            reference=payout_reference,
            transaction_type="DEBIT",
        )

        wallet_payload = {
            "transaction_from": "WINNINGS_WITHDRAW",
            "game_type": pos_lottery_winner.lottery_type,
            "game_play_id": pos_lottery_winner.game_id,
        }

        print("KAKKKKKA 4")

        UserWallet.deduct_wallet(
            user=agent_wallet.agent,
            amount=int(amount),
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **wallet_payload,
        )

        # remove the money from general withdrawable wallet
        # GeneralWithdrawableWallet.deduct_fund(amount=amount, phone=_agent_instance.phone)

        Wallet.debit_wallet(
            wallet_type="RETAIL_RTP_WALLET",
            amount=amount,
            game_type=pos_lottery_winner.lottery_type,
            user_phone=pos_lottery_winner.agent.phone,
            user_name=f"{pos_lottery_winner.agent.first_name} {pos_lottery_winner.agent.last_name}",
            game_play_id=pos_lottery_winner.game_id,
        )

        amount = pos_lottery_winner.amount_won

        # check_if_it_should_remiit = (
        #     AgentWallet().check_lotto_winner_fund_play_wallet(
        #         agent_id=_agent_instance.id,
        #         amount=amount,
        #         game_id=game_id,
        #         narration=narration,
        #     )
        # )

        # if check_if_it_should_remiit is True:
        #     data = {"message": "initiated and your play balnce have been update"}

        #     # update PosLotteryWinners db
        #     pos_lottery_winner.is_win_claimed = True
        #     pos_lottery_winner.withdrawl_initiated = True
        #     pos_lottery_winner.save()

        #     return Response(data=data, status=status.HTTP_200_OK)

        # ------------------------------ LIBERTY PAY BUDDY TRANSFER ------------------------------ #

        print("KAKKKKKA 5")
        payload = {
            "from_wallet_type": "COLLECTION",
            "to_wallet_type": "COLLECTION",
            "data": [
                {
                    "buddy_phone_number": _agent_instance.phone,
                    "amount": amount,
                    "narration": "LOTTO_WINNING_WITHDRAWAL",
                    "is_beneficiary": "False",
                    "save_beneficiary": "True",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                    "customer_reference": payout_reference,
                }
            ],
        }

        agent_wallet.refresh_from_db()

        # reference = "withdraw-{}".format(uuid.uuid4())

        pos_lottery_winner.payout_ref = payout_reference
        pos_lottery_winner.save()

        _withdraw_table_instance = PayoutTransactionTable.objects.create(
            source="BUDDY",
            amount=amount,
            disbursement_unique_id=payout_reference,
            phone=_agent_instance.phone,
            payout_trans_ref=payout_reference,
            channel="POS",
            game_play_id=game_id,
            payout_payload=payload,
            unique_game_play_id=game_id,
            date_won=pos_lottery_winner.date_created,
            balance_before=agent_wallet.winnings_bal + amount,
            balance_after=agent_wallet.winnings_bal,
            joined_since=_agent_instance.get_duration(),
            name=f"{_agent_instance.first_name} {_agent_instance.last_name}",
            source_wallet="RETAIL_RTP_WALLET",
            recipient_wallet="USER_WALLET",
        )

        print("KAKKKKKA 6")

        cp_withdraw_table_instance = _withdraw_table_instance

        cp_payload = payload
        # cp_payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN

        # update PosLotteryWinners db
        pos_lottery_winner.is_win_claimed = True
        pos_lottery_winner.withdrawl_initiated = True
        pos_lottery_winner.save()

        # agent_transaction_insatnce = AgentWalletTransaction.objects.create(
        #     agent_wallet=agent_wallet,
        #     amount=amount,
        #     transaction_from="LIBERTRY_PAY_LOTTERY_WINNING_COMMISSION_REWARD",
        # )

        # -----------------------------------------

        print("KAKKKKKA 7")
        response = pos_agent_helper.agency_buddy_transfer(**cp_payload)
        cp_response = response

        print("KAKKKKKA 8")

        if isinstance(cp_response, dict):
            if cp_response.get("message") != "success":
                # agent_transaction_insatnce.status = "FAILED"
                # agent_transaction_insatnce.save()
                pass

            else:
                # agent_transaction_insatnce.status = "SUCCESSFUL"
                # agent_transaction_insatnce.save()

                cp_withdraw_table_instance.disbursed = True
                cp_withdraw_table_instance.is_verified = True
                cp_withdraw_table_instance.save()

                pos_lottery_winner.payout_successful = True
                pos_lottery_winner.payout_verified = True
                pos_lottery_winner.save()

        else:
            # agent_transaction_insatnce.status = "FAILED"
            # agent_transaction_insatnce.save()

            pass

        _withdraw_table_instance.source_response_payload = cp_response
        _withdraw_table_instance.save()

        print("KAKKKKKA 9")
        data = {
            "status": "success",
            "message": "Withdrawal initiated",
            "transaction_ref": payout_reference,
            "agent_id": _agent_instance.user_uuid,
            "account_name": f"{_agent_instance.first_name} {_agent_instance.last_name}",
            "bank_name": "Liberty Pay vfd",
            "amount": amount,
            "reference": cp_withdraw_table_instance.payout_trans_ref,
            "date": datetime.now(),
            "data": None,
        }
        return Response(data, status=status.HTTP_200_OK)


class IndividualMobileUserWinningWithdrawal(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    serializer_class = IndividualMobileUserWinningWithdrawalSerializer

    def post(self, request):
        # print(f"""
        #     IndividualMobileUserWinningWithdrawal: {request.data}
        # """)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        if AgentConstantVariables().allow_mobile_users_to_withdrwal_from_different_flow() is False:
            data = {"message": "Withdrawal from this channel not allowed"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        amount = serializer.validated_data.get("amount")
        amount = float(amount)
        if amount > 20000:
            amount = 20000

        agent_transaction_pin = serializer.validated_data.get("pin")

        if amount <= 50:
            return Response(
                {"message": "You can't withdraw less than 50 naira"},
            )

        _agent_instance = Agent.objects.filter(phone=request.user.phone).last()

        if (
            _agent_instance.phone not in ConstantVariable.get_constant_variable().get("users_exempted_from_limit")
        ) and MaxWithdrawalThresold.check_and_update_thresold(phone_number=_agent_instance.phone, amount=float(amount)) is True:
            return Response(
                {"message": "Please, try withdrawal in the next 24 hours"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not _agent_instance:
            _agent_instance = Agent.objects.filter(email=request.user.email).last()

        if not _agent_instance:
            data = {
                "message": "Agent does not exist",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if _agent_instance.terminal_id is not None:
            data = {"message": "You can't initiate withdrawal from this channel, please contact admin"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # agent wallet
        agent_wallet = AgentWallet.objects.filter(agent=_agent_instance).last()

        if agent_wallet is None:
            data = {"message": "You have no withdrawal wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        pos_agent_helper = PosAgentHelper(agent_instance=_agent_instance, amount=amount)

        if pos_agent_helper.verify_agent_transaction_pin(agent_transaction_pin) is False:
            data = {
                "message": "Invalid transaction pin",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if amount > agent_wallet.winnings_bal:
            data = {"message": "Insufficient balance in your withdrawable wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if retail_has_enough_money_to_give_out(amount) is False:
            data = {"message": "We're sorry, an error occured while processing your request. Please try again later"}

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # check our VFD mirrow float wallet and make sure we've the amount to give out
        if FloatWallet.get_float_wallet(source="RETAIL_RTP_WALLET").amount < amount:
            data = {"message": "Withdrawal network error. Please try again later"}

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent_wallet.agent.phone,
            amount=amount,
            channel="POS",
            reference=payout_reference,
            transaction_type="DEBIT",
        )

        wallet_payload = {"transaction_from": "WINNINGS_WITHDRAW"}

        UserWallet.deduct_wallet(
            user=agent_wallet.agent,
            amount=int(amount),
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **wallet_payload,
        )

        agent_wallet.refresh_from_db()

        # remove the money from general withdrawable wallet
        # GeneralWithdrawableWallet.deduct_fund(amount=amount, phone=_agent_instance.phone)

        Wallet.debit_wallet(
            wallet_type="RETAIL_RTP_WALLET",
            amount=float(amount),
            user_phone=_agent_instance.phone,
            user_name=f"{_agent_instance.first_name} {_agent_instance.last_name}",
        )

        payload = {
            "from_wallet_type": "COLLECTION",
            "to_wallet_type": "COLLECTION",
            "data": [
                {
                    "buddy_phone_number": _agent_instance.phone,
                    "amount": amount,
                    "narration": "LOTTO_WINNING_WITHDRAWAL",
                    "is_beneficiary": "False",
                    "save_beneficiary": "True",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                    "reference": payout_reference,
                }
            ],
        }

        _withdraw_table_instance = PayoutTransactionTable.objects.create(
            source="BUDDY",
            amount=amount,
            disbursement_unique_id=payout_reference,
            phone=_agent_instance.phone,
            payout_trans_ref=payout_reference,
            channel="POS",
            payout_payload=payload,
            balance_before=agent_wallet.winnings_bal + amount,
            balance_after=agent_wallet.winnings_bal,
            joined_since=_agent_instance.get_duration(),
            name=f"{_agent_instance.first_name} {_agent_instance.last_name}",
            source_wallet="RETAIL_RTP_WALLET",
            recipient_wallet="USER_WALLET",
        )

        cp_withdraw_table_instance = _withdraw_table_instance

        cp_payload = payload
        cp_payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN

        response = pos_agent_helper.agency_buddy_transfer(**cp_payload)
        cp_response = response

        if isinstance(cp_response, dict):
            if cp_response.get("message") != "success":
                pass

            else:
                cp_withdraw_table_instance.disbursed = True
                cp_withdraw_table_instance.is_verified = True
                cp_withdraw_table_instance.save()
        else:
            pass

        cp_withdraw_table_instance.source_response_payload = cp_response
        cp_withdraw_table_instance.save()

        data = {
            "status": "success",
            "message": "Withdrawal initiated",
            "transaction_ref": payout_reference,
            "agent_id": _agent_instance.user_uuid,
            "account_name": f"{_agent_instance.first_name} {_agent_instance.last_name}",
            "bank_name": "Liberty Pay vfd",
            "amount": amount,
            "reference": cp_withdraw_table_instance.payout_trans_ref,
            "date": datetime.now(),
            "data": None,
        }
        return Response(data, status=status.HTTP_200_OK)


class TicketDetails(APIView):
    """
    This endpoint returns details of ticket won
    """

    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request):
        serilaizer = TicketDetailsSerializer(data=request.data)
        serilaizer.is_valid(raise_exception=True)

        game_id = str(serilaizer.validated_data.get("game_id")).strip()
        pin = serilaizer.validated_data.get("pin")
        phone = serilaizer.validated_data.get("phone")

        _formated_phone = LotteryModel.format_number_from_back_add_234(phone)

        # get agent_instance
        _agent_instance = Agent.objects.filter(email=request.user.email).last()

        if not _agent_instance:
            _agent_instance = Agent.objects.filter(phone=request.user.phone).last()

        if not _agent_instance:
            data = {
                "message": "Agent does not exist",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        pos_lottery_winner_qs = PosLotteryWinners.objects.filter(agent=_agent_instance, game_id__iexact=game_id, player__phone_number=_formated_phone)

        PosWithdrawalRequest.objects.create(
            agent=_agent_instance,
            game_id=game_id,
            stage_one_payload=serilaizer.validated_data,
        )

        # ---------------------- check if this cashout is awoof coupon ticket game claim
        awoof_ticket_data = AwoofGameTable().awoof_pos_won_ticket_confirmation(phone=phone, game_id=game_id, pin=pin)

        if awoof_ticket_data.get("status") is True:
            data = {
                "game_id": game_id,
                "phone": phone,
                "amount_won": None,
            }

            return Response(data, status=status.HTTP_200_OK)

        # ---------------------------- END AWOOF COUPON TICKET CLAIM

        if pos_lottery_winner_qs.exists():
            pos_lottery_winner = pos_lottery_winner_qs.last()

            if pos_lottery_winner.is_win_claimed and pos_lottery_winner.withdrawl_initiated:
                data = {
                    "message": "You have already claimed your winnings",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if pos_lottery_winner.is_win_claimed is False and pos_lottery_winner.withdrawl_initiated:
                data = {
                    "message": "Withdrawal already initiated. Please wait for payout",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if PosLotteryWinners.validate_pin(
                agent=_agent_instance,
                pin=pin,
                game_id=game_id,
                player_phone=_formated_phone,
            ):
                data = {
                    "game_id": game_id,
                    "phone": phone,
                    "amount_won": pos_lottery_winner.amount_won,
                }
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = {"message": "Invalid pin"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        else:
            data = {"message": "Sorry we could not find your ticket"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class TransactionHistory(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        page = request.GET.get("page", 1)
        # wallet_type = str(request.GET.get("wallet_type")).strip().upper()

        # get agent_instance
        _agent_instance = Agent.objects.filter(phone=request.user.phone).last()

        if not _agent_instance:
            _agent_instance = Agent.objects.filter(email=request.user.email).last()

        if not _agent_instance:
            data = {
                "message": "Agent does not exist",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # filter by date #

        date_from = request.GET.get("date_from", None)
        date_to = request.GET.get("date_to", None)

        if date_from is not None and date_to is None:
            data = {
                "message": "Please provide date_from and date_to",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        elif date_from is None and date_to is not None:
            data = {
                "message": "Please provide date_from and date_to",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        elif (date_from is not None and date_from != "") and (date_to is not None and date_to != ""):
            try:
                date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
                date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
            except Exception:
                data = {
                    "message": "Invalid date format",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            _withdraw_table_qs = AgentWalletTransaction.objects.filter(
                Q(agent_wallet__agent__id=_agent_instance.id),
                ~Q(transaction_from="COMMISSION"),
                Q(date_created__date__range=(date_from, date_to)),
                Q(show_transaction=True),
            ).order_by("-id")

            paginate_all_history = CustomPaginator.paginate(request, _withdraw_table_qs, page)

            withdraw_serialized_data = AgentTransactionHistorySerializer(paginate_all_history, many=True)

            data = withdraw_serialized_data.data
            return Response(data, status=status.HTTP_200_OK)

        # End filter by date #

        # filter by transaction type #
        transaction_type = request.GET.get("transaction_type", None)
        if transaction_type is not None and transaction_type != "":
            transaction_from = ["CREDIT", "WITHDRAWAL"]

            if transaction_type.upper() not in transaction_from:
                data = {
                    "message": "Invalid transaction type",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if transaction_type.upper() == "CREDIT":
                _withdraw_table_qs = AgentWalletTransaction.objects.filter(transaction_type="CREDIT", show_transaction=True).order_by("-id")

                paginate_all_history = CustomPaginator.paginate(request, _withdraw_table_qs, page)

                withdraw_serialized_data = AgentTransactionHistorySerializer(paginate_all_history, many=True)

                data = withdraw_serialized_data.data
                return Response(data, status=status.HTTP_200_OK)

            elif transaction_type.upper() == "WITHDRAWAL":
                _withdraw_table_qs = AgentWalletTransaction.objects.filter(transaction_type="WITHDRAWAL", show_transaction=True).order_by("-id")

                paginate_all_history = CustomPaginator.paginate(request, _withdraw_table_qs, page)

                withdraw_serialized_data = AgentTransactionHistorySerializer(paginate_all_history, many=True)

                data = withdraw_serialized_data.data
                return Response(data, status=status.HTTP_200_OK)

        # End filter by transaction type #

        _withdraw_table_qs = AgentWalletTransaction.objects.filter(
            Q(agent_wallet__agent__id=_agent_instance.id),
            ~Q(transaction_from="COMMISSION"),
            Q(show_transaction=True),
        ).order_by("-id")

        paginate_all_history = CustomPaginator.paginate(request, _withdraw_table_qs, page)

        withdraw_serialized_data = AgentTransactionHistorySerializer(paginate_all_history, many=True).data

        agent_wallet_transaction_data = withdraw_serialized_data

        agent_payout_transaction_qs = PayoutTransactionTable.objects.filter(phone = _agent_instance.phone).order_by("-id")[:100]

        serialized_agent_payout_transaction_data = MobileUserPayoutTransactionHistory(agent_payout_transaction_qs, many = True).data

        combined_data = agent_wallet_transaction_data + serialized_agent_payout_transaction_data

        import dateutil.parser

        # sorted_combined_data = sorted(combined_data, key=lambda x: datetime.strptime(x['date_created'], '%Y-%m-%d %H:%M:%S'), reverse=True)

        # print(serialized_agent_payout_transaction_data, "\n\n")

        # sorted_combined_data = sorted(combined_data, key=lambda x: dateutil.parser.parse(x['date_created']), reverse=True)


        # Extract dictionaries from tuples in both datasets
        agent_wallet_data_extracted = [item[0] if isinstance(item, tuple) and len(item) > 0 else item 
                                    for item in agent_wallet_transaction_data]

        agent_payout_data_extracted = [item[0] if isinstance(item, tuple) and len(item) > 0 else item 
                                    for item in serialized_agent_payout_transaction_data]

        # Combine the extracted data
        combined_data = agent_wallet_data_extracted + agent_payout_data_extracted

        # Function to safely get the date with fallback
        def get_date(item):
            # Check for 'date_created' first since that's what we saw in your sample
            if 'date_created' in item and item['date_created']:
                return dateutil.parser.parse(item['date_created'])
            # Then check other possible date field names
            for key in ['date_added', 'created_at', 'timestamp']:
                if key in item and item[key]:
                    return dateutil.parser.parse(item[key])
            # Fallback
            return dateutil.parser.parse('1970-01-01T00:00:00+00:00')

        # Sort the combined data
        sorted_combined_data = sorted(combined_data, key=get_date, reverse=True)


        return Response(sorted_combined_data, status=status.HTTP_200_OK)


class LotteryPayment(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    serializer_class = PosLotteryPaymentSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        pin = request.data.get("pin", "0000")

        print("sdfghjkljhgfdsasdfghjjhgfdsasdfghjhgfdsaasdfghhgfdasdfghgf")

        agent_instance = Agent.objects.filter(phone=request.user.phone).last()

        if not agent_instance:
            agent_instance = Agent.objects.filter(email=request.user.email).last()
            if not agent_instance:
                data = {
                    "message": "Agent does not exist",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        game_play_id = serializer.validated_data.get("game_play_id")

        # filter lotto ticket by game play id

        lottery_ticket_qs = LottoTicket.objects.filter(
            game_play_id=game_play_id,
            paid=False,
            agent_profile__id=agent_instance.id,
            is_duplicate=False,
        )

        # ------------------------------ VALIDATE AGENT LIBERTY PAY TRANSACTION PIN ------------------------------ #

        # pos_agent_helper = PosAgentHelper(agent_instance=agent_instance, amount=0)

        # if pos_agent_helper.verify_agent_transaction_pin(pin) is False:
        #     data = {
        #         "message": "Invalid transaction pin",
        #     }

        #     return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # ------------------------------ END VALIDATE AGENT LIBERTY PAY TRANSACTION PIN ------------------------------ #

        # validate remiitance amount
        def validate_remtance_amount(amount):
            response = LottoAgentRemittanceTable().valuate_amount_to_be_added(amount=amount, agent=agent_instance)
            if response.get("is_amount_much") is False:
                return False, None
            else:
                return True, response.get("amount")

        if not lottery_ticket_qs.exists():
            print("TICKET EXISTS")
            lottery_ticket_qs = LotteryModel.objects.filter(
                game_play_id=game_play_id,
                paid=False,
                agent_profile=agent_instance,
                is_duplicate=False,
            )

            if not lottery_ticket_qs.exists():
                return verify_game_play_id_payment(game_play_id)

            lottery_ticket = lottery_ticket_qs.last()

            total_new_amount = 0
            wyse_cash_price_model = WyseCashPriceModel()

            for wyse_cash_data in lottery_ticket_qs:
                price_model = wyse_cash_price_model.ticket_price(channel="POS", band=wyse_cash_data.band, no_of_line=1)

                total_new_amount += (
                    price_model.get("stake_amount") + price_model.get("woven_service_charge", 0) + price_model.get("africastalking_charge", 0)
                )

            (
                validate_amount_in_remiitance_status,
                validate_amount_in_remiitance_status_amount,
            ) = validate_remtance_amount(total_new_amount)

            if validate_amount_in_remiitance_status is True:
                data = {
                    "message": f"Hi, please, fund your wallet with this amount {Utility.currency_formatter(validate_amount_in_remiitance_status_amount)} to continue game play",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            print("ABOUT TO VERIFY AMOUNT")
            # amount=lottery_ticket.expected_amount * lottery_ticket_qs.count(),
            pos_agent_helper = PosAgentHelper(
                agent_instance=agent_instance,
                amount=total_new_amount,
                pin=pin,
            )

            response = pos_agent_helper.handle_agent_charge_and_lottery_play(lottery_ticket_qs)

        else:
            print("TICKET DOES NOT EXISTS")
            lottery_ticket = lottery_ticket_qs.last()
            # amount=lottery_ticket.expected_amount * lottery_ticket_qs.count(),
            print("lottery_ticket.lottery_type", lottery_ticket.lottery_type)
            if lottery_ticket.lottery_type == "INSTANT_CASHOUT":
                print("INSTANT CASHOUT")
                i_cash_price_model = InstantCashOutPriceModel()

                # new_payment_amount = LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
                #     ticket_count=lottery_ticket_qs.count()
                # ).get(
                #     "stake_amount"
                # )

                price_model = i_cash_price_model.ticket_price(channel="POS", ticket_line=lottery_ticket_qs.count())
                new_payment_amount = (
                    price_model.get("ticket_price", 0)
                    + price_model.get("illusion_price", 0)
                    + price_model.get("woven_service_charge", 0)
                    + price_model.get("africastalking_charge", 0)
                )
            else:
                sal_for_life_price_model = SalaryForLifePriceModel()

                # new_payment_amount = LottoTicket.salary_for_life_stake_amount_pontential_winning_for_lotto_agents(
                #     ticket_count=lottery_ticket_qs.count()
                # ).get(
                #     "stake_amount"
                # )

                price_model = sal_for_life_price_model.ticket_price(channel="POS", ticket_line=lottery_ticket_qs.count())
                new_payment_amount = (
                    price_model.get("ticket_price", 0)
                    + price_model.get("illusion_price", 0)
                    + price_model.get("woven_service_charge", 0)
                    + price_model.get("africastalking_charge", 0)
                )

            (
                validate_amount_in_remiitance_status,
                validate_amount_in_remiitance_status_amount,
            ) = validate_remtance_amount(new_payment_amount)

            if validate_amount_in_remiitance_status is True:
                data = {
                    "message": f"Hi, please, fund your wallet with this amount {Utility.currency_formatter(validate_amount_in_remiitance_status_amount)} to continue game play",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if new_payment_amount <= 0:
                data = {
                    "message": f"Hi, this amount {Utility.currency_formatter(new_payment_amount)} is invalid",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            print("ABOUT TO VERIFY AMOUNT ::::::::::::::::::::: 125789")
            pos_agent_helper = PosAgentHelper(
                agent_instance=agent_instance,
                amount=new_payment_amount,
                pin=pin,
            )
            response = pos_agent_helper.handle_agent_charge_and_lottery_play(lottery_ticket_qs, instant_cashout_game=True, _game_play_id=game_play_id)

        if isinstance(response, dict):
            if response.get("status") == "success":
                if response.get("lottery_type") == "INSTANT_CASHOUT":
                    paid_lottery_queryset = LottoTicket.objects.filter(game_play_id=game_play_id, is_duplicate=False)

                    paid_lottery_instance = paid_lottery_queryset.last()

                    # get_total_paid_amount = LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
                    #     paid_lottery_instance.number_of_ticket
                    # )

                    instant_cashout_price_model = InstantCashOutPriceModel()
                    instant_cashout_price = instant_cashout_price_model.ticket_price(
                        channel="POS",
                        ticket_line=paid_lottery_instance.number_of_ticket,
                    )

                    if paid_lottery_instance is None:
                        data = {
                            "message": "Payment failed",
                        }
                        return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                    # data = {
                    #     "status": "Accepted",
                    #     "agent_id": agent_instance.user_uuid,
                    #     "ticket_owner": paid_lottery_instance.user_profile.phone_number,
                    #     "game_id": paid_lottery_instance.game_play_id,
                    #     "game_type": "INSTANT_CASHOUT",
                    #     "stake_per_pick": get_total_paid_amount.get("stake_amount")
                    #     / paid_lottery_instance.number_of_ticket,
                    #     "total_stake": get_total_paid_amount.get("stake_amount"),
                    #     "total_ticket": paid_lottery_instance.number_of_ticket,
                    #     "pin": paid_lottery_instance.pin,
                    #     "tickets": list(
                    #         map(
                    #             lambda x: {
                    #                 "ticket": [int(i) for i in x.ticket.split(",")]
                    #             },
                    #             paid_lottery_queryset,
                    #         )
                    #     ),
                    #     "system_pick": paid_lottery_instance.system_generated_num,
                    # }

                    data = {
                        "status": "Accepted",
                        "agent_id": agent_instance.user_uuid,
                        "ticket_owner": paid_lottery_instance.user_profile.phone_number,
                        "game_id": paid_lottery_instance.game_play_id,
                        "game_type": "INSTANT_CASHOUT",
                        "stake_per_pick": instant_cashout_price.get("ticket_price") / paid_lottery_instance.number_of_ticket,
                        "total_stake": instant_cashout_price.get("ticket_price"),
                        "total_ticket": paid_lottery_instance.number_of_ticket,
                        "pin": paid_lottery_instance.pin,
                        "tickets": list(
                            map(
                                lambda x: {"ticket": [int(i) for i in x.ticket.split(",")]},
                                paid_lottery_queryset,
                            )
                        ),
                        "system_pick": paid_lottery_instance.system_generated_num,
                    }

                    if (paid_lottery_instance.pin is not None) or (paid_lottery_instance.pin != ""):
                        data["pin"] = paid_lottery_instance.pin
                    else:
                        LottoTicket().generate_ticket_pin(paid_lottery_instance.game_play_id)
                        sleep(1)
                        data["pin"] = paid_lottery_instance.pin

                    return Response(data, status=status.HTTP_200_OK)

                elif response.get("lottery_type") == "SALARY_FOR_LIFE":
                    # sleep(5)

                    print("game_play_id", game_play_id, "\n\n\n\n")

                    paid_lottery_queryset = LottoTicket.objects.filter(game_play_id=game_play_id, is_duplicate=False)

                    # print("paid_lottery_queryset", paid_lottery_queryset, "\n\n\n\n")

                    paid_lottery_instance = paid_lottery_queryset.last()

                    # get_total_paid_amount = LottoTicket.salary_for_life_stake_amount_pontential_winning_for_lotto_agents(
                    #     paid_lottery_queryset.count()
                    # ).get("stake_amount")

                    if paid_lottery_instance is None:
                        data = {
                            "message": "Payment failed",
                        }
                        return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                    # check if the ticket with won jackpot
                    won_jackpot = PosLotteryWinners.objects.filter(game_id=paid_lottery_instance.game_play_id, jackpot=True).last()

                    data = {
                        "status": "Accepted",
                        "agent_id": agent_instance.user_uuid,
                        "ticket_owner": paid_lottery_instance.user_profile.phone_number,
                        "game_id": paid_lottery_instance.game_play_id,
                        "game_type": "SALARY_FOR_LIFE",
                        "stake_per_pick": paid_lottery_instance.amount_paid,
                        "total_stake": paid_lottery_instance.amount_paid * len(paid_lottery_queryset),
                        "total_ticket": paid_lottery_queryset.count(),
                        "pin": paid_lottery_instance.pin,
                        "tickets": list(
                            map(
                                lambda x: {"ticket": [int(i) for i in x.ticket.split(",")]},
                                paid_lottery_queryset,
                            )
                        ),
                        "jackpot": {
                            "won": True if won_jackpot is not None else False,
                            "win_amount": won_jackpot.amount_won if won_jackpot is not None else 0.0,
                            "pin": won_jackpot.pin if won_jackpot is not None else None,
                        },
                    }

                    if paid_lottery_instance.pin is not None or paid_lottery_instance.pin != "":
                        data["pin"] = paid_lottery_instance.pin
                    else:
                        LottoTicket().generate_ticket_pin(paid_lottery_instance.game_play_id)
                        sleep(1)
                        data["pin"] = paid_lottery_instance.pin

                    return Response(data, status=status.HTTP_200_OK)

                elif response.get("lottery_type") == "WYSE_CASH":
                    # stake amount
                    # sleep(5)
                    paid_lottery_queryset = LotteryModel.objects.filter(game_play_id=game_play_id, is_duplicate=False)

                    paid_lottery_instance = paid_lottery_queryset.last()

                    if paid_lottery_instance is None:
                        data = {
                            "message": "Payment failed",
                        }
                        return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                    lottery_stake = {
                        "150": [],
                        "250": [],
                        "600": [],
                        "1200": [],
                    }

                    for lottery in paid_lottery_queryset:
                        if int(lottery.stake_amount) == 100:
                            new_stake = "150"
                        elif int(lottery.stake_amount) == 200:
                            new_stake = "250"
                        elif int(lottery.stake_amount) == 500:
                            new_stake = "600"
                        elif int(lottery.stake_amount) == 1000:
                            new_stake = "1200"

                        lottery_stake[new_stake].append(
                            {
                                "stake_amount": int(new_stake),
                                "lucky_number": lottery.lucky_number,
                                "consent": True,
                                "band": lottery.band,
                                "id": lottery.id,
                                "get_game_play_id": lottery.game_play_id,
                            },
                        )

                    using_agent_phone_number = False if paid_lottery_queryset.last().pin is None or paid_lottery_queryset.last().pin == "" else True

                    if paid_lottery_instance.pin is not None or paid_lottery_instance.pin != "":
                        pass
                    else:
                        LotteryModel().generate_ticket_pin(paid_lottery_instance.game_play_id)
                        sleep(1)
                    # check if the ticket with won jackpot
                    won_jackpot = PosLotteryWinners.objects.filter(game_id=paid_lottery_instance.game_play_id, jackpot=True).last()

                    serialized_WYSE_CASH_data = PosLottoHelper.serilaize_WYSE_CASH_data(
                        lottery_stake,
                        paid_lottery_instance.user_profile.phone_number,
                        agent_instance,
                        using_agent_phone_number=using_agent_phone_number,
                        pin=paid_lottery_queryset.last().pin,
                        total_stake=total_new_amount,
                    )
                    serialized_WYSE_CASH_data["jackpot"] = {
                        "won": True if won_jackpot is not None else False,
                        "win_amount": won_jackpot.amount_won if won_jackpot is not None else 0.0,
                        "pin": won_jackpot.pin if won_jackpot is not None else None,
                    }

                    return Response(data=serialized_WYSE_CASH_data, status=status.HTTP_200_OK)

            else:
                data = {
                    "message": response.get("message"),
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        else:
            data = {
                "message": "Unable to process request",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)





class NewLotteryPayment(APIView):
    """
    API view for handling lottery payment processing.
    
    This endpoint validates payment requests, processes transactions,
    and returns appropriate payment confirmations.
    """

    permission_classes = [
        TokenAuthentication
    ]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]
    serializer_class = PosLotteryPaymentSerializer

    def post(self, request):
        """Process lottery payment request."""
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        pin = request.data.get("pin", "0000")
        game_play_id = serializer.validated_data.get("game_play_id")
        
        # Get agent instance
        agent_instance = self._get_agent_instance(request)
        if not agent_instance:
            return Response(
                {"message": "Agent does not exist"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Find lottery tickets for this game play
        lottery_ticket_qs = self._get_lottery_tickets(game_play_id, agent_instance)
        
        # Process payment based on lottery type
        if not lottery_ticket_qs.exists():
            return self._process_wyse_cash_payment(game_play_id, agent_instance, pin)
        else:
            return self._process_standard_lottery_payment(lottery_ticket_qs, agent_instance, pin, game_play_id)
            
    def _get_agent_instance(self, request):
        """Get the agent instance from the user's credentials."""
        agent_instance = Agent.objects.filter(phone=request.user.phone).last()
        if not agent_instance:
            agent_instance = Agent.objects.filter(email=request.user.email).last()
        return agent_instance
        
    def _get_lottery_tickets(self, game_play_id, agent_instance):
        """Get lottery tickets for the given game play ID and agent."""
        return LottoTicket.objects.filter(
            game_play_id=game_play_id,
            agent_profile=agent_instance,
            is_duplicate=False,
            channel = "POS_AGENT"
        )
    
    def _validate_remittance_amount(self, amount, agent_instance):
        """Validate if agent has sufficient remittance amount."""
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(
            amount=amount, 
            agent=agent_instance
        )
        if response.get("is_amount_much") is False:
            return False, None
        return True, response.get("amount")
        
    def _process_wyse_cash_payment(self, game_play_id, agent_instance, pin):
        print("GGGGGGGGGGGGGG")
        """Process payment for WYSE CASH lottery type."""
        # Check if lottery exists in lottery model
        lottery_ticket_qs = LotteryModel.objects.filter(
            game_play_id=game_play_id,
            paid=False,
            agent_profile=agent_instance,
            is_duplicate=False,
            channel = "POS_AGENT"
        )
        
        if not lottery_ticket_qs.exists():
            return verify_game_play_id_payment(game_play_id)
            
        lottery_ticket = lottery_ticket_qs.last()
        
        # Calculate total amount
        total_amount = self._calculate_wyse_cash_amount(lottery_ticket_qs)
        
        # Validate remittance amount
        is_valid, required_amount = self._validate_remittance_amount(total_amount, agent_instance)
        if is_valid:
            return Response(
                {
                    "message": f"Hi, please, fund your wallet with this amount {Utility.currency_formatter(required_amount)} to continue game play"
                },
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Process payment
        pos_agent_helper = PosAgentHelper(
            agent_instance=agent_instance,
            amount=total_amount,
            pin=pin,
        )
        
        response = pos_agent_helper.handle_agent_charge_and_lottery_play(lottery_ticket_qs)
        return self._format_response(response, agent_instance, game_play_id, total_amount)
        
    def _calculate_wyse_cash_amount(self, lottery_ticket_qs):
        """Calculate total amount for WYSE CASH lottery."""
        total_amount = 0
        wyse_cash_price_model = WyseCashPriceModel()
        
        for wyse_cash_data in lottery_ticket_qs:
            price_model = wyse_cash_price_model.ticket_price(
                channel="POS", 
                band=wyse_cash_data.band, 
                no_of_line=1
            )
            
            total_amount += (
                price_model.get("stake_amount") + 
                price_model.get("woven_service_charge", 0) + 
                price_model.get("africastalking_charge", 0)
            )
            
        return total_amount
        
    def _process_standard_lottery_payment(self, lottery_ticket_qs, agent_instance, pin, game_play_id):
        """Process payment for standard lottery types (INSTANT_CASHOUT or SALARY_FOR_LIFE)."""
        lottery_ticket = lottery_ticket_qs.last()
        lottery_type = lottery_ticket.lottery_type
        
        # Calculate payment amount based on lottery type
        if lottery_type == "INSTANT_CASHOUT":
            price_model_class = InstantCashOutPriceModel()
            price_model = price_model_class.ticket_price(
                channel="POS", 
                ticket_line=lottery_ticket_qs.count()
            )
        else:  # SALARY_FOR_LIFE
            price_model_class = SalaryForLifePriceModel()
            price_model = price_model_class.ticket_price(
                channel="POS", 
                ticket_line=lottery_ticket_qs.count()
            )
            
        payment_amount = (
            price_model.get("ticket_price", 0) +
            price_model.get("illusion_price", 0) +
            price_model.get("woven_service_charge", 0) +
            price_model.get("africastalking_charge", 0)
        )
        
        # Validate remittance and payment amount
        # is_valid, required_amount = self._validate_remittance_amount(payment_amount, agent_instance)
        # if is_valid:
        #     return Response(
        #         {
        #             "message": f"Hi, please, fund your wallet with this amount {Utility.currency_formatter(required_amount)} to continue game play"
        #         },
        #         status=status.HTTP_400_BAD_REQUEST
        #     )
            
        # if payment_amount <= 0:
        #     return Response(
        #         {
        #             "message": f"Hi, this amount {Utility.currency_formatter(payment_amount)} is invalid"
        #         },
        #         status=status.HTTP_400_BAD_REQUEST
        #     )
            
        # Process payment
        is_instant_cashout = lottery_type == "INSTANT_CASHOUT"
        pos_agent_helper = PosAgentHelper(
            agent_instance=agent_instance,
            amount=payment_amount,
            pin=pin,
        )
        
        # response = pos_agent_helper.handle_agent_charge_and_lottery_play(
        #     lottery_ticket_qs, 
        #     instant_cashout_game=is_instant_cashout, 
        #     _game_play_id=game_play_id
        # )

        response = {
            "lottery_type": lottery_type,
            "status": "success",
            "message": "Payment successful",
        }
        
        return self._format_response(response, agent_instance, game_play_id, payment_amount)
    
    def _format_response(self, response, agent_instance, game_play_id, payment_amount):
        """Format the API response based on payment processing results."""
        if not isinstance(response, dict) or response.get("status") != "success":
            error_message = response.get("message") if isinstance(response, dict) else "Unable to process request"
            return Response(
                {"message": error_message},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        lottery_type = response.get("lottery_type")
        
        if lottery_type == "INSTANT_CASHOUT":
            return self._format_instant_cashout_response(agent_instance, game_play_id)
        elif lottery_type == "SALARY_FOR_LIFE":
            return self._format_salary_for_life_response(agent_instance, game_play_id)
        elif lottery_type == "WYSE_CASH":
            return self._format_wyse_cash_response(agent_instance, game_play_id, payment_amount)
            
        return Response(
            {"message": "Unknown lottery type"},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    def _format_instant_cashout_response(self, agent_instance, game_play_id):
        """Format response for INSTANT_CASHOUT lottery type."""
        paid_lottery_queryset = LottoTicket.objects.filter(
            channel = "POS_AGENT",
            game_play_id=game_play_id, 
            is_duplicate=False,
            agent_profile = agent_instance
        )
        
        paid_lottery_instance = paid_lottery_queryset.last()
        if not paid_lottery_instance:
            return Response(
                {"message": "Payment failed"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        instant_cashout_price_model = InstantCashOutPriceModel()
        instant_cashout_price = instant_cashout_price_model.ticket_price(
            channel="POS",
            ticket_line=paid_lottery_instance.number_of_ticket,
        )
        
        data = {
            "status": "Accepted",
            "agent_id": agent_instance.user_uuid,
            "ticket_owner": paid_lottery_instance.user_profile.phone_number,
            "game_id": paid_lottery_instance.game_play_id,
            "game_type": "INSTANT_CASHOUT",
            "stake_per_pick": instant_cashout_price.get("ticket_price") / paid_lottery_instance.number_of_ticket,
            "total_stake": instant_cashout_price.get("ticket_price"),
            "total_ticket": paid_lottery_instance.number_of_ticket,
            "pin": paid_lottery_instance.pin,
            "tickets": list(
                map(
                    lambda x: {"ticket": [int(i) for i in x.ticket.split(",")]},
                    paid_lottery_queryset,
                )
            ),
            "system_pick": paid_lottery_instance.system_generated_num,
        }
        
        # Generate PIN if needed
        if not paid_lottery_instance.pin:
            LottoTicket().generate_ticket_pin(paid_lottery_instance.game_play_id)
            sleep(1)
            data["pin"] = paid_lottery_instance.pin
            
        return Response(data, status=status.HTTP_200_OK)
    
    def _format_salary_for_life_response(self, agent_instance, game_play_id):
        """Format response for SALARY_FOR_LIFE lottery type."""
        paid_lottery_queryset = LottoTicket.objects.filter(
            channel = "POS_AGENT",
            game_play_id=game_play_id, 
            is_duplicate=False,
            agent_profile = agent_instance

        )
        
        paid_lottery_instance = paid_lottery_queryset.last()
        if not paid_lottery_instance:
            return Response(
                {"message": "Payment failed"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Check if ticket won jackpot
        won_jackpot = PosLotteryWinners.objects.filter(
            game_id=paid_lottery_instance.game_play_id, 
            jackpot=True
        ).last()
        
        data = {
            "status": "Accepted",
            "agent_id": agent_instance.user_uuid,
            "ticket_owner": paid_lottery_instance.user_profile.phone_number,
            "game_id": paid_lottery_instance.game_play_id,
            "game_type": "SALARY_FOR_LIFE",
            "stake_per_pick": paid_lottery_instance.amount_paid,
            "total_stake": paid_lottery_instance.amount_paid * len(paid_lottery_queryset),
            "total_ticket": paid_lottery_queryset.count(),
            "pin": paid_lottery_instance.pin,
            "tickets": list(
                map(
                    lambda x: {"ticket": [int(i) for i in x.ticket.split(",")]},
                    paid_lottery_queryset,
                )
            ),
            "jackpot": {
                "won": won_jackpot is not None,
                "win_amount": won_jackpot.amount_won if won_jackpot else 0.0,
                "pin": won_jackpot.pin if won_jackpot else None,
            },
        }
        
        # Generate PIN if needed
        if not paid_lottery_instance.pin:
            LottoTicket().generate_ticket_pin(paid_lottery_instance.game_play_id)
            sleep(1)
            data["pin"] = paid_lottery_instance.pin
            
        return Response(data, status=status.HTTP_200_OK)
    
    def _format_wyse_cash_response(self, agent_instance, game_play_id, total_amount):
        """Format response for WYSE_CASH lottery type."""
        paid_lottery_queryset = LotteryModel.objects.filter(
            game_play_id=game_play_id, 
            is_duplicate=False,
            channel = "POS_AGENT",
            agent_profile = agent_instance
        )
        
        paid_lottery_instance = paid_lottery_queryset.last()
        if not paid_lottery_instance:
            return Response(
                {"message": "Payment failed"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Organize lottery stakes by amount
        lottery_stake = self._organize_wyse_cash_stakes(paid_lottery_queryset)
        
        # Generate PIN if needed
        using_agent_phone_number = bool(paid_lottery_queryset.last().pin)
        if not paid_lottery_instance.pin:
            LotteryModel().generate_ticket_pin(paid_lottery_instance.game_play_id)
            sleep(1)
            
        # Check if ticket won jackpot
        won_jackpot = PosLotteryWinners.objects.filter(
            game_id=paid_lottery_instance.game_play_id, 
            jackpot=True
        ).last()
        
        # Serialize data
        serialized_data = PosLottoHelper.serilaize_WYSE_CASH_data(
            lottery_stake,
            paid_lottery_instance.user_profile.phone_number,
            agent_instance,
            using_agent_phone_number=using_agent_phone_number,
            pin=paid_lottery_queryset.last().pin,
            total_stake=total_amount,
        )
        
        serialized_data["jackpot"] = {
            "won": won_jackpot is not None,
            "win_amount": won_jackpot.amount_won if won_jackpot else 0.0,
            "pin": won_jackpot.pin if won_jackpot else None,
        }
        
        return Response(serialized_data, status=status.HTTP_200_OK)
    
    def _organize_wyse_cash_stakes(self, paid_lottery_queryset):
        """Organize WYSE_CASH lottery stakes by amount."""
        lottery_stake = {
            "150": [],
            "250": [],
            "600": [],
            "1200": [],
        }
        
        stake_mapping = {
            100: "150",
            200: "250",
            500: "600",
            1000: "1200"
        }
        
        for lottery in paid_lottery_queryset:
            stake_amount = int(lottery.stake_amount)
            new_stake = stake_mapping.get(stake_amount, "150")  # Default to 150 if unknown
            
            lottery_stake[new_stake].append({
                "stake_amount": int(new_stake),
                "lucky_number": lottery.lucky_number,
                "consent": True,
                "band": lottery.band,
                "id": lottery.id,
                "get_game_play_id": lottery.game_play_id,
            })
            
        return lottery_stake





class JackpotApiView(APIView):
    """
    This view is used to get agent details
    """

    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        jackpot = Jackpot.get_jackpot()
        return Response(data=jackpot, status=status.HTTP_200_OK)


class GameResultsView(APIView):
    """
    This view is used to get agent details
    """

    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        lottery_batch_qs = LotteryBatch.objects.filter(
            Q(lottery_type="SALARY_FOR_LIFE"),
            Q(is_active=False),
            Q(lottery_winner_ticket_number__isnull=False),
            ~Q(lottery_winner_ticket_number=""),
        ).order_by("-id")[:10]

        salary_for_life_serializer = LotteryBatchSerializer(lottery_batch_qs, many=True)

        salary_4_life_data = salary_for_life_system_pick_serializer(salary_for_life_serializer)

        instant_cashout_filter = LottoWinners.objects.filter(lotto_type="INSTANT_CASHOUT").order_by("-earning")[:10]
        instant_cashout_serializer = LottoTicketGameResultSerializer(instant_cashout_filter, many=True)

        wyse_cash_filter = LotteryWinnersTable.objects.all().order_by("-earning")[:10]
        wyse_cash_serializer = WyseCashGameResultSerializer(wyse_cash_filter, many=True)

        # bank result
        lottery_batch_qs = LotteryBatch.objects.filter(Q(is_active=False), lottery_type="BANKER").order_by("-id")[:10]

        paginate_all_banker_games = CustomPaginator.paginate(request, lottery_batch_qs, 1)

        lottery_batch_serialize_data = LotteryBatchSerializer(paginate_all_banker_games, many=True)

        banker_data = salary_for_life_lotto_data_sort_by_date(lottery_batch_serialize_data)

        banker_data = banker_data[0]

        data = {
            # "salary_for_life": salary_for_life_serializer.data,
            "salary_for_life": salary_4_life_data,
            "instant_cashout": instant_cashout_serializer.data,
            "wyse_cash": wyse_cash_serializer.data,
            "banker": banker_data,
        }
        return Response(data=data, status=status.HTTP_200_OK)


class GameDetails(APIView):
    """
    API view for retrieving detailed information about a game based on game_play_id
    """

    authentication_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    serializer_class = GamePlayIdSerializer

    def _get_agent_profile(self, user):
        """Helper method to get agent profile by email or phone"""
        agent_profile = Agent.objects.filter(email=user.email).first()
        if not agent_profile:
            agent_profile = Agent.objects.filter(phone=user.phone).first()
        return agent_profile

    def _calculate_lotto_ticket_data(self, lotto_ticket_qs):
        """Calculate ticket prices, stakes, and potential winnings"""
        lotto_ticket_instance = lotto_ticket_qs.first()

        # Determine ticket price based on lottery type
        if lotto_ticket_instance.lottery_type == "INSTANT_CASHOUT":
            ticket_price = LottoTicket().instant_cashout_stake_amount_pontential_winning_for_lotto_agents(lotto_ticket_qs.count())
        else:
            ticket_price = LottoTicket().salary_for_life_stake_amount_pontential_winning_for_lotto_agents(lotto_ticket_qs.count())

        # Calculate stakings
        stake_per_pick = lotto_ticket_instance.amount_paid
        total_stake = lotto_ticket_instance.amount_paid * lotto_ticket_qs.count()
        potential_winning = ticket_price.get("stake_amount")
        total_ticket = lotto_ticket_qs.count()

        # Special handling for QUIKA type
        if lotto_ticket_instance.lottery_type == "QUIKA":
            return self._handle_quika_ticket(lotto_ticket_instance, lotto_ticket_qs)

        return {"stake_per_pick": stake_per_pick, "total_stake": total_stake, "potential_winning": potential_winning, "total_ticket": total_ticket}

    def _handle_quika_ticket(self, lotto_ticket_instance, lotto_ticket_qs):
        """Handle QUIKA lottery type calculations"""
        if lotto_ticket_instance.is_new_quika_game:
            user_tickets = [int(x) for x in lotto_ticket_instance.ticket.split(",") if x and x != "0"]

            quika_price_model = PosNewQuikaPriceModel()
            quika_price_dict = quika_price_model.ticket_price(ticket_line=len(user_tickets))

            stake_per_pick = lotto_ticket_instance.amount_paid
            total_stake = stake_per_pick * len(user_tickets)
            potential_winning = quika_price_dict.get("potential_winning")
            total_ticket = len(user_tickets)
        else:
            quika_price_model = QuikaPriceModel()
            quika_price_dict = quika_price_model.ticket_price(ticket_line=lotto_ticket_qs.count(), channel="POS")

            stake_per_pick = lotto_ticket_instance.amount_paid
            total_stake = stake_per_pick * lotto_ticket_qs.count()
            potential_winning = quika_price_dict.get("potential_winning")
            total_ticket = lotto_ticket_qs.count()

        return {"stake_per_pick": stake_per_pick, "total_stake": total_stake, "potential_winning": potential_winning, "total_ticket": total_ticket}

    def _process_ticket_status(self, lotto_ticket_qs, game_play_id, base_data):
        """Process ticket statuses and append ticket information"""
        result = base_data.copy()
        result["tickets"] = []

        lotto_ticket_instance = lotto_ticket_qs.first()

        # Handle unpaid tickets
        if not lotto_ticket_instance.paid:
            for ticket in lotto_ticket_qs:
                result["tickets"].append({"ticket": self._parse_ticket_numbers(ticket.ticket), "status": "pending payment"})
            return result

        # Handle paid tickets
        if not lotto_ticket_instance.batch.is_active:
            return self._process_completed_draw_tickets(lotto_ticket_qs, game_play_id, result)
        else:
            # Pending draw tickets
            for ticket in lotto_ticket_qs:
                result["tickets"].append({"ticket": self._parse_ticket_numbers(ticket.ticket), "status": "pending draw"})

        # Add system pick numbers
        result["system_pick"] = self._get_system_pick(lotto_ticket_instance)
        return result

    def _process_completed_draw_tickets(self, lotto_ticket_qs, game_play_id, data):
        """Process tickets for completed draws"""
        result = data.copy()

        for ticket in lotto_ticket_qs:
            winning_qs = LottoWinners.objects.filter(game_play_id=game_play_id, ticket=ticket.ticket)

            if winning_qs.exists():
                winning_instance = winning_qs.last()

                if winning_instance.lotto_type == "INSTANT_CASHOUT":
                    if (winning_instance.win_flavour == "WHITE") and (winning_instance.match_type != "PERM_0"):
                        # Handle winning ticket
                        result.pop("potential_winning", None)
                        result["amount_won"] = winning_qs.aggregate(Sum("earning"))["earning__sum"]
                        result["status"] = "won"
                        result["tickets"].append({"ticket": self._parse_ticket_numbers(ticket.ticket), "status": "won"})
                    else:
                        # Handle cashback
                        cash_back_perc = winning_instance.get_cashback_percentage
                        result.pop("potential_winning", None)
                        result["cashback_amount"] = winning_qs.aggregate(Sum("earning"))["earning__sum"]
                        result["status"] = f"{cash_back_perc}% cashback"
                        result["tickets"].append({"ticket": self._parse_ticket_numbers(ticket.ticket), "status": f"{cash_back_perc}% cashback"})
                else:
                    # Handle other winning lottery types
                    result.pop("potential_winning", None)
                    result["amount_won"] = winning_qs.aggregate(Sum("earning"))["earning__sum"]
                    result["status"] = "won"
                    result["tickets"].append({"ticket": self._parse_ticket_numbers(ticket.ticket), "status": "won"})
            else:
                # Handle lost tickets
                if result["status"] != "won" and "cashback" not in str(result["status"]):
                    result["status"] = "lost"

                result["tickets"].append({"ticket": self._parse_ticket_numbers(ticket.ticket), "status": "lost"})

        return result

    def _get_system_pick(self, ticket_instance):
        """Get system pick numbers"""
        if not ticket_instance.paid:
            return []

        if ticket_instance.lottery_type == "INSTANT_CASHOUT":
            if not ticket_instance.system_generated_num:
                return []
            return [int(i) for i in ticket_instance.system_generated_num.split(",")]
        else:
            if ticket_instance.batch.is_active:
                return []

            system_pick_num = machine_number_serializer(ticket_instance.batch.lottery_winner_ticket_number)
            system_pick_num = system_pick_num[0].split(",") if system_pick_num else []
            return [int(i) for i in system_pick_num]

    def _parse_ticket_numbers(self, ticket_str):
        """Parse ticket numbers from string format"""
        return [int(i) for i in ticket_str.split(",") if i and i != "0"]

    def _handle_lottery_model(self, lottery_qs, agent_profile):
        """Handle LotteryModel data"""
        if not lottery_qs.exists():
            return None

        lottery_instance = lottery_qs.last()
        lottery_stake = {
            "150": [],
            "250": [],
            "600": [],
            "1200": [],
        }

        for lottery in lottery_qs:
            # Map old stake amounts to new ones
            stake_mapping = {"100": "150", "200": "250", "500": "600", "1000": "1200"}

            # new_stake = stake_mapping.get(str(int(lottery.stake_amount)), "150")

            new_stake = stake_mapping.get(str(int(lottery.stake_amount)), "150")

            lottery_stake[new_stake].append(
                {
                    "stake_amount": lottery.amount_paid,
                    "lucky_number": lottery.lucky_number,
                    "consent": True,
                    "band": lottery.band,
                    "id": lottery.id,
                    "get_game_play_id": lottery.game_play_id,
                }
            )

        return PosLottoHelper.serilaize_WYSE_CASH_data(
            lottery_stake,
            lottery_instance.user_profile.phone_number,
            agent_profile,
        )

    def post(self, request):
        """Handle POST request to get game details"""
        # Validate input data
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Get agent profile
        agent_profile = self._get_agent_profile(request.user)
        if not agent_profile:
            return Response({"message": "Agent does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        game_play_id = serializer.validated_data.get("game_play_id")

        # Try to get LottoTicket data first
        lotto_ticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id, agent_profile=agent_profile, is_duplicate=False, channel = "POS_AGENT")
        if lotto_ticket_qs.exists():
            _last_instance = lotto_ticket_qs.last()
            lotto_ticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id, agent_profile=agent_profile, is_duplicate=False, batch = _last_instance.batch, channel = "POS_AGENT")
        

        if lotto_ticket_qs.exists():
            lotto_ticket_instance = lotto_ticket_qs.first()

            # Calculate base data
            ticket_data = self._calculate_lotto_ticket_data(lotto_ticket_qs)

            # Prepare response data
            base_data = {
                "status": "Accepted",
                "agent_id": lotto_ticket_instance.agent_profile.user_uuid,
                "ticket_owner": lotto_ticket_instance.user_profile.phone_number,
                "game_id": lotto_ticket_instance.game_play_id,
                "pin": lotto_ticket_instance.pin,
                "game_type": lotto_ticket_instance.lottery_type,
                "date": lotto_ticket_instance.date,
                **ticket_data,
            }

            # Process tickets and their statuses
            result = self._process_ticket_status(lotto_ticket_qs, game_play_id, base_data)
            return Response(data=result, status=status.HTTP_200_OK)

        # Fall back to LotteryModel if no LottoTicket found
        lottery_qs = LotteryModel.objects.filter(
            game_play_id=game_play_id,
            agent_profile=agent_profile,
            is_duplicate=False,
        )

        lottery_data = self._handle_lottery_model(lottery_qs, agent_profile)
        if not lottery_data:

            # Check if game_play id is found in africa_lotto
            africa_lotto_qs = AfricaLotto.objects.filter(
                game_play_id=game_play_id,
                agent_phone_number=agent_profile.phone,
            )
            if not africa_lotto_qs.exists():
                return Response({"message": "Game not found"}, status=status.HTTP_400_BAD_REQUEST)
            else:
                lottery_data = AfricaLotto.get_game_details(game_id=game_play_id)

        return Response(data=lottery_data, status=status.HTTP_200_OK)


class WyseCashGameResult(APIView):
    """
    Filter wyse cash games and sort them by date
    """

    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        page = request.GET.get("page", 1)
        filter_date = request.GET.get("date", None)

        agent = Agent.objects.filter(email=request.user.email).last()
        if agent is None:
            agent = Agent.objects.filter(phone=request.user.phone).last()

            if agent is None:
                data = {
                    "message": "Agent not found",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if filter_date is not None:
            try:
                filter_date = datetime.strptime(filter_date, "%Y-%m-%d").date()

            except Exception:
                data = {
                    "message": "Invalid date format",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            lottery_qs = LotteryWinnersTable.objects.filter(date_won__date=filter_date, lottery__agent_profile__id=agent.id).order_by("-id")
            paginate_all_WYSE_CASH_games = CustomPaginator.paginate(request, lottery_qs, page)

            wyse_cash_serializer = WyseCashGameResultSortSerializer(paginate_all_WYSE_CASH_games, many=True)

            sorted_data = lotto_data_sort_by_date(wyse_cash_serializer)

            return Response(data=sorted_data, status=status.HTTP_200_OK)

        lottery_qs = LotteryWinnersTable.objects.filter(lottery__agent_profile__id=agent.id).order_by("-id")
        paginate_all_WYSE_CASH_games = CustomPaginator.paginate(request, lottery_qs, page)

        wyse_cash_serializer = WyseCashGameResultSortSerializer(paginate_all_WYSE_CASH_games, many=True)

        sorted_data = lotto_data_sort_by_date(wyse_cash_serializer)

        try:
            sorted_data = sorted_data[0]
        except Exception:
            pass

        return Response(data=sorted_data, status=status.HTTP_200_OK)


class InstantCashGameResult(APIView):
    """
    Filter instant cash games and sort them by date
    """

    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        page = request.GET.get("page", 1)

        filter_date = request.GET.get("date", None)

        agent = Agent.objects.filter(email=request.user.email).last()
        if agent is None:
            agent = Agent.objects.filter(phone=request.user.phone).last()

            if agent is None:
                data = {
                    "message": "Agent not found",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if filter_date is not None:
            try:
                filter_date = datetime.strptime(filter_date, "%Y-%m-%d").date()

            except Exception:
                data = {
                    "message": "Invalid date format",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            lottery_qs = LottoWinners.objects.filter(
                Q(date_won__date=filter_date),
                Q(lotto_type="INSTANT_CASHOUT"),
            ).order_by("-id")
            paginate_all_WYSE_CASH_games = CustomPaginator.paginate(request, lottery_qs, page)

            wyse_cash_serializer = InstantCashGameResultSortSerializer(paginate_all_WYSE_CASH_games, many=True)

            sorted_data = lotto_data_sort_by_date(wyse_cash_serializer)

            return Response(data=sorted_data, status=status.HTTP_200_OK)

        lottery_qs = LottoWinners.objects.filter(
            lotto_type="INSTANT_CASHOUT",
        ).order_by("-id")
        paginate_all_instant_cash_games = CustomPaginator.paginate(request, lottery_qs, page)
        print("agent.id", agent.id)
        print("lottery_qs", lottery_qs, "\n\n\n")

        instant_cash_serializer = InstantCashGameResultSortSerializer(paginate_all_instant_cash_games, many=True)

        sorted_data = lotto_data_sort_by_date(instant_cash_serializer)
        try:
            sorted_data = sorted_data[0]
        except Exception:
            pass

        return Response(data=sorted_data, status=status.HTTP_200_OK)


class SalaryForLifeNumberPick(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        page = request.GET.get("page", 1)

        filter_date = request.GET.get("date", None)

        agent = Agent.objects.filter(email=request.user.email).last()
        if agent is None:
            agent = Agent.objects.filter(phone=request.user.phone).last()

            if agent is None:
                data = {
                    "message": "Agent not found",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if filter_date is not None:
            try:
                filter_date = datetime.strptime(filter_date, "%Y-%m-%d").date()

            except Exception:
                data = {
                    "message": "Invalid date format",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            lottery_batch_qs = LotteryBatch.objects.filter(
                is_active=False,
                created_date__date=filter_date,
                lottery_type="SALARY_FOR_LIFE",
            ).order_by("-id")

            paginate_all_salary_for_life_games = CustomPaginator.paginate(request, lottery_batch_qs, page)

            instant_cash_serializer = LotteryBatchSerializer(paginate_all_salary_for_life_games, many=True)

            data = salary_for_life_lotto_data_sort_by_date(instant_cash_serializer)

            # print("instant_cash_serializer", instant_cash_serializer.data)

            # return Response(data={"message": "ok"}, status=status.HTTP_200_OK)
            return Response(data=data, status=status.HTTP_200_OK)

        # lottery_batch_qs = LotteryBatch.objects.filter(
        #     Q(lottery_type="SALARY_FOR_LIFE"),
        #     Q(is_active=False),
        #     Q(lottery_winner_ticket_number__isnull=False),
        #     ~Q(lottery_winner_ticket_number=""),
        # )

        lottery_batch_qs = LotteryBatch.objects.filter(Q(is_active=False), lottery_type="SALARY_FOR_LIFE").order_by("-id")

        paginate_all_salary_for_life_games = CustomPaginator.paginate(request, lottery_batch_qs, page)

        lottery_batch_serialize_data = LotteryBatchSerializer(paginate_all_salary_for_life_games, many=True)

        data = salary_for_life_lotto_data_sort_by_date(lottery_batch_serialize_data)

        data = data[0]

        # return Response(data={"message": "ok"}, status=status.HTTP_200_OK)
        return Response(data=data, status=status.HTTP_200_OK)


class GameHistory(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        page = request.GET.get("page", 1)

        # print(
        #     f"""
        # HEADERS: {request.headers}
        # \n\n\n\n
        # """
        # )

        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        agent_instance = Agent.objects.filter(email=request.user.email).last()
        if not agent_instance:
            agent_instance = Agent.objects.filter(phone=request.user.phone).last()

            if not agent_instance:
                data = {
                    "message": "Agent does not exist",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # filter by date #
        date_from = request.GET.get("date_from", None)
        date_to = request.GET.get("date_to", None)

        if date_from is not None and date_to is None:
            data = {
                "message": "Please provide date_from and date_to",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        elif date_from is None and date_to is not None:
            data = {
                "message": "Please provide date_from and date_to",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        elif (date_from is not None and date_from != "") and (date_to is not None and date_to != ""):
            return Response(data, status=status.HTTP_200_OK)
            try:
                date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
                date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
            except Exception:
                data = {
                    "message": "Invalid date format",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            _loto_game_history = LottoTicket.objects.filter(
                agent_profile=agent_instance,
                date__date__range=(date_from, date_to),
                is_duplicate=False,
            ).order_by("-id")

            paginate_all_lottery_ticket = CustomPaginator.paginate(request, _loto_game_history, page)

            lotto_serialized_data = GameHistorySerializer(paginate_all_lottery_ticket, many=True)

            # remove duplicates
            _lotto_data = remove_duplicate_game_history(lotto_serialized_data.data)

            _WYSE_CASH_game_history = LotteryModel.objects.filter(
                agent_profile=agent_instance,
                date__date__range=(date_from, date_to),
                is_duplicate=False,
            ).order_by("-id")

            # africa lotto
            africa_lotto = (
                AfricaLotto.objects.filter(
                    agent_phone_number=agent_instance.phone,
                    created_at__date__range=(date_from, date_to),
                )
                .order_by("game_play_id", "-id")
                .distinct("game_play_id")
            )

            paginate_all_africa_lotto_ticket = CustomPaginator.paginate(request, africa_lotto, page)

            serialized_africa_lotto = AfricaLottoGameHistorySerializer(paginate_all_africa_lotto_ticket, many=True)

            distintc_africa_lotto_data = remove_duplicate_game_history(serialized_africa_lotto.data)

            paginate_all_WYSE_CASH_games = CustomPaginator.paginate(request, _WYSE_CASH_game_history, page)

            WYSE_CASH_serialized_data = SecondGameHistorySerializer(paginate_all_WYSE_CASH_games, many=True)

            whyse_cahs_data = serialize_WYSE_CASH_game_history(WYSE_CASH_serialized_data.data)

            data = new_merge_and_sort_game_history(_lotto_data, whyse_cahs_data, distintc_africa_lotto_data)
            # print("length ")
            return Response(data, status=status.HTTP_200_OK)

        # End of filter by date #

        # filter by game status #
        game_status = request.GET.get("game_status", None)
        if game_status is not None:
            types_of_status = ["won", "lost", "accepted", "all"]

            if game_status.casefold() not in types_of_status:
                data = {
                    "message": "Invalid game status",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if game_status.casefold() == "won":
                # _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance).order_by("-id")

                _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance, date__date=current_datetime.date()).order_by("-id")

                # paginator = Paginator(_loto_game_history, 30)

                # paginate_all_lottery_ticket = CustomPaginator.paginate(
                #     request, _loto_game_history, page
                # )

                lotto_serialized_data = PosLottoFilterWinnersSerializer(_loto_game_history, many=True)

                # remove duplicates
                _lotto_data = remove_duplicate_game_history(lotto_serialized_data.data)

                # _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance).order_by("-id")

                _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance, date__date=current_datetime.date()).order_by("-id")
                # paginate_all_WYSE_CASH_games = Paginator.paginate(request=request, queryset=_WYSE_CASH_game_history, page=page)
                # paginate_all_WYSE_CASH_games = CustomPaginator.paginate(
                #     request, _WYSE_CASH_game_history, page
                # )

                WYSE_CASH_serialized_data = PosLotteryFilterWinnersSerializer(_WYSE_CASH_game_history, many=True)

                whyse_cahs_data = serialize_WYSE_CASH_game_history(WYSE_CASH_serialized_data.data)

                data = merge_and_sort_game_history(_lotto_data, whyse_cahs_data)
                # print("length ")
                return Response(data, status=status.HTTP_200_OK)

            elif game_status.casefold() == "lost":
                # _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance).order_by("-id")

                _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance, date__date=current_datetime.date()).order_by("-id")

                # paginator = Paginator(_loto_game_history, 30)

                # paginate_all_lottery_ticket = CustomPaginator.paginate(
                #     request, _loto_game_history, page
                # )

                lotto_serialized_data = PosLottoFilterLostSerializer(_loto_game_history, many=True)

                # remove duplicates
                _lotto_data = remove_duplicate_game_history(lotto_serialized_data.data)

                # _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance).order_by("-id")

                _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance, date__date=current_datetime.date()).order_by("-id")
                # paginate_all_WYSE_CASH_games = Paginator.paginate(request=request, queryset=_WYSE_CASH_game_history, page=page)
                # paginate_all_WYSE_CASH_games = CustomPaginator.paginate(
                #     request, _WYSE_CASH_game_history, page
                # )

                WYSE_CASH_serialized_data = PosLotteryFilterLostSerializer(_WYSE_CASH_game_history, many=True)

                whyse_cahs_data = serialize_WYSE_CASH_game_history(WYSE_CASH_serialized_data.data)

                data = merge_and_sort_game_history(_lotto_data, whyse_cahs_data)
                # print("length ")
                return Response(data, status=status.HTTP_200_OK)

            elif game_status.casefold() == "accepted":
                # _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance, paid=True, batch__is_active=True).order_by("-id")

                _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance, paid=True, batch__is_active=True, date__date=current_datetime.date()).order_by("-id")

                # paginator = Paginator(_loto_game_history, 30)

                # paginate_all_lottery_ticket = CustomPaginator.paginate(
                #     request, _loto_game_history, page
                # )

                lotto_serialized_data = PosLottoFilterPendingDrawSerializer(_loto_game_history, many=True)

                # remove duplicates
                _lotto_data = remove_duplicate_game_history(lotto_serialized_data.data)

                # _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance, paid=True, batch__is_active=True).order_by("-id")

                _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance, paid=True, batch__is_active=True, date__date=current_datetime.date()).order_by("-id")

                # paginate_all_WYSE_CASH_games = Paginator.paginate(request=request, queryset=_WYSE_CASH_game_history, page=page)
                # paginate_all_WYSE_CASH_games = CustomPaginator.paginate(
                #     request, _WYSE_CASH_game_history, page
                # )

                WYSE_CASH_serialized_data = PosLotteryFilterPendingDrawSerializer(_WYSE_CASH_game_history, many=True)

                whyse_cahs_data = serialize_WYSE_CASH_game_history(WYSE_CASH_serialized_data.data)

                data = merge_and_sort_game_history(_lotto_data, whyse_cahs_data)
                # print("length ")
                return Response(data, status=status.HTTP_200_OK)

            elif game_status.casefold() == "all":
                # _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance).order_by("-id")

                _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance, date__date=current_datetime.date()).order_by("-id")

                # paginator = Paginator(_loto_game_history, 30)

                paginate_all_lottery_ticket = CustomPaginator.paginate(request, _loto_game_history, page)

                lotto_serialized_data = GameHistorySerializer(paginate_all_lottery_ticket, many=True)

                # remove duplicates
                _lotto_data = remove_duplicate_game_history(lotto_serialized_data.data)

                # _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance).order_by("-id")

                _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance, date__date=current_datetime.date()).order_by("-id")

                # paginate_all_WYSE_CASH_games = Paginator.paginate(request=request, queryset=_WYSE_CASH_game_history, page=page)
                paginate_all_WYSE_CASH_games = CustomPaginator.paginate(request, _WYSE_CASH_game_history, page)

                WYSE_CASH_serialized_data = SecondGameHistorySerializer(paginate_all_WYSE_CASH_games, many=True)

                whyse_cahs_data = serialize_WYSE_CASH_game_history(WYSE_CASH_serialized_data.data)

                data = merge_and_sort_game_history(_lotto_data, whyse_cahs_data)
                # print("length ")
                return Response(data, status=status.HTTP_200_OK)

        # End of filter by game status #

        # filter by game type #
        game_type = request.GET.get("game_type", None)

        if game_type is not None:
            game_types = ["salary_for_life", "wyse_cash", "instant_cashout"]

            if game_type.casefold() not in game_types:
                data = {"messsage": "Invalid game type"}

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if game_type.casefold() == "salary_for_life":
                # _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance, lottery_type="SALARY_FOR_LIFE").order_by("-id")

                _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance, lottery_type="SALARY_FOR_LIFE", date__date=current_datetime.date()).order_by("-id")

                # paginator = Paginator(_loto_game_history, 30)

                # paginate_all_lottery_ticket = CustomPaginator.paginate(
                #     request, _loto_game_history, page
                # )

                lotto_serialized_data = GameHistorySerializer(_loto_game_history, many=True)

                # remove duplicates
                _lotto_data = remove_duplicate_game_history(lotto_serialized_data.data)

                return Response(data=_lotto_data, status=status.HTTP_200_OK)

            elif game_type.casefold() == "instant_cashout":
                # _loto_game_his     = LottoTicket.objects.filter(agent_profile=agent_instance, lottery_type="INSTANT_CASHOUT").order_by("-id")

                _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance, lottery_type="INSTANT_CASHOUT", date__date=current_datetime.date()).order_by("-id")

                # paginator = Paginator(_loto_game_history, 30)

                # paginate_all_lottery_ticket = CustomPaginator.paginate(
                #     request, _loto_game_history, page
                # )

                lotto_serialized_data = GameHistorySerializer(_loto_game_history, many=True)

                # remove duplicates
                _lotto_data = remove_duplicate_game_history(lotto_serialized_data.data)

                return Response(data=_lotto_data, status=status.HTTP_200_OK)

            elif game_type.casefold() == "wyse_cash":
                # _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance).order_by("-id")

                _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance, date__date=current_datetime.date()).order_by("-id")

                # paginate_all_WYSE_CASH_games = Paginator.paginate(request=request, queryset=_WYSE_CASH_game_history, page=page)
                # paginate_all_WYSE_CASH_games = CustomPaginator.paginate(
                #     request, _WYSE_CASH_game_history, page
                # )

                WYSE_CASH_serialized_data = SecondGameHistorySerializer(_WYSE_CASH_game_history, many=True)

                whyse_cahs_data = serialize_WYSE_CASH_game_history(WYSE_CASH_serialized_data.data)

                return Response(data=whyse_cahs_data, status=status.HTTP_200_OK)

        # Emd of filter by game type #

        # _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance).order_by("-id")[:100]

        _loto_game_history = LottoTicket.objects.filter(agent_profile=agent_instance, date__date=current_datetime.date()).order_by("-id")[:100]

        # paginator = Paginator(_loto_game_history, 30)

        paginate_all_lottery_ticket = CustomPaginator.paginate(request, _loto_game_history, page)

        lotto_serialized_data = GameHistorySerializer(paginate_all_lottery_ticket, many=True)

        # remove duplicates
        _lotto_data = remove_duplicate_game_history(lotto_serialized_data.data)

        # _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance).order_by("-id")[:100]

        _WYSE_CASH_game_history = LotteryModel.objects.filter(agent_profile=agent_instance, date__date=current_datetime.date()).order_by("-id")[:100]

        # paginate_all_WYSE_CASH_games = Paginator.paginate(request=request, queryset=_WYSE_CASH_game_history, page=page)
        paginate_all_WYSE_CASH_games = CustomPaginator.paginate(request, _WYSE_CASH_game_history, page)

        WYSE_CASH_serialized_data = SecondGameHistorySerializer(paginate_all_WYSE_CASH_games, many=True)

        whyse_cahs_data = serialize_WYSE_CASH_game_history(WYSE_CASH_serialized_data.data)

        # africa lotto
        # africa_lotto = AfricaLotto.objects.filter(agent_phone_number=agent_instance.phone).order_by("-id")[:500]

        africa_lotto = AfricaLotto.objects.filter(agent_phone_number=agent_instance.phone, created_at__date=current_datetime.date()).order_by("-id")[:500]

        # print("agent_instance.phone", agent_instance.phone)

        # print("africa_lotto", africa_lotto)

        paginate_all_africa_lotto_ticket = CustomPaginator.paginate(request, africa_lotto, page)

        serialized_africa_lotto = AfricaLottoGameHistorySerializer(paginate_all_africa_lotto_ticket, many=True)

        distintc_africa_lotto_data = remove_duplicate_game_history(serialized_africa_lotto.data)

        # print("distintc_africa_lotto_data", distintc_africa_lotto_data)

        data = new_merge_and_sort_game_history(distintc_africa_lotto_data, _lotto_data, whyse_cahs_data)

        # print("length ")
        return Response(data, status=status.HTTP_200_OK)


class WinnersApiView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        game_types = ["WYSE_CASH", "INSTANT_CASHOUT", "SALARY_FOR_LIFE"]

        page = request.GET.get("page", 1)
        game_type = request.GET.get("game_type", None)

        if game_type is None:
            data = {
                "message": "game_type is required",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if game_type.upper() not in game_types:
            data = {
                "message": "Invalid game_type",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        agent_instance = Agent.objects.filter(phone=request.user.phone).last()
        if agent_instance is None:
            agent_instance = Agent.objects.filter(email=request.user.email).last()
            if agent_instance is None:
                data = {"message": "Agent not found"}

                return Response(data=data, status=status.HTTP_404_NOT_FOUND)

        if game_type.upper() == "WYSE_CASH":
            wyse_cash_filter = LotteryWinnersTable.objects.filter(lottery__agent_profile=agent_instance).order_by("-earning")

            # print("wyse_cash_filter", wyse_cash_filter)

            paginate_all_wyse_cash_games = CustomPaginator.paginate(request, wyse_cash_filter, page)
            wyse_cash_serializer = WyseCashGameResultSerializer(paginate_all_wyse_cash_games, many=True)

            return Response(data=wyse_cash_serializer.data, status=status.HTTP_200_OK)

        elif game_type.upper() == "INSTANT_CASHOUT":
            instant_cash_filter = LottoWinners.objects.filter(lotto_type="INSTANT_CASHOUT", lottery__agent_profile=agent_instance).order_by(
                "-earning"
            )

            paginate_all_instant_cash_games = CustomPaginator.paginate(request, instant_cash_filter, page)

            instant_cash_serializer = LottoTicketGameResultSerializer(paginate_all_instant_cash_games, many=True)

            return Response(data=instant_cash_serializer.data, status=status.HTTP_200_OK)

        elif game_type.upper() == "SALARY_FOR_LIFE":
            instant_cash_filter = LottoWinners.objects.filter(lotto_type="SALARY_FOR_LIFE", lottery__agent_profile=agent_instance).order_by(
                "-earning"
            )

            paginate_all_instant_cash_games = CustomPaginator.paginate(request, instant_cash_filter, page)

            instant_cash_serializer = LottoTicketGameResultSerializer(paginate_all_instant_cash_games, many=True)

            return Response(data=instant_cash_serializer.data, status=status.HTTP_200_OK)


class CheckInstantCashoutInstantResult(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    serializer_class = GamePlayIdSerializer

    def post(self, request):
        serilizer = self.serializer_class(data=request.data)
        serilizer.is_valid(raise_exception=True)

        game_play_id = serilizer.validated_data.get("game_play_id")

        # instant_cashout_qs = LottoTicket.objects.filter(
        #     game_play_id=game_play_id, lottery_type="INSTANT_CASHOUT"
        # )
        instant_cashout_qs = LottoTicket.objects.filter(
            Q(lottery_type="QUIKA") | Q(lottery_type="INSTANT_CASHOUT"),
            game_play_id=game_play_id,
            paid=True,
        )

        instant_cashout_qs.aggregate(Sum("stake_amount"))["stake_amount__sum"]

        if not instant_cashout_qs.exists():
            data = {
                "message": "Invalid game_play_id",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        instant_cashout_instance = instant_cashout_qs.last()

        tickets = []

        # get_total_paid_amount = LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
        #     instant_cashout_qs.count()
        # )

        i_cash_price_model = InstantCashOutPriceModel()

        price_model = i_cash_price_model.ticket_price(channel="POS", ticket_line=instant_cashout_instance.number_of_ticket)

        # check if the ticket with won jackpot

        won_jackpot = PosLotteryWinners.objects.filter(game_id=instant_cashout_instance.game_play_id, jackpot=True).last()

        # data = {
        #     "ticket_owner": instant_cashout_instance.user_profile.phone_number,
        #     "game_id": instant_cashout_instance.game_play_id,
        #     "game_type": instant_cashout_qs.last().lottery_type,
        #     "stake_per_pick": get_total_paid_amount.get("stake_amount")
        #     / instant_cashout_qs.count(),
        #     "total_stake": get_total_paid_amount.get("stake_amount"),
        #     "total_ticket": instant_cashout_qs.count(),
        #     "date": instant_cashout_instance.date,
        #     "pin": instant_cashout_instance.pin,
        # }

        data = {
            "ticket_owner": instant_cashout_instance.user_profile.phone_number,
            "game_id": instant_cashout_instance.game_play_id,
            "game_type": instant_cashout_qs.last().lottery_type,
            "stake_per_pick": price_model.get("ticket_price") / instant_cashout_qs.count(),
            "total_stake": price_model.get("ticket_price"),
            "total_ticket": instant_cashout_qs.count(),
            "date": instant_cashout_instance.date,
            "pin": instant_cashout_instance.pin,
        }

        # check winners status
        winning_tickets = []
        winning_instance = LottoWinners.objects.filter(
            game_play_id=game_play_id,
            lottery__user_profile__phone_number=instant_cashout_instance.user_profile.phone_number,
        )

        existing_gen_winning = winning_instance.filter(won_jackpot=False)

        if existing_gen_winning.exists():
            winning_ins = existing_gen_winning.last()
            if (winning_ins.win_flavour == "WHITE") and (winning_ins.match_type != "PERM_0"):
                data["status"] = "won"
                data["amount_won"] = existing_gen_winning.aggregate(Sum("earning"))["earning__sum"]
                won_tickets = [ticket.ticket for ticket in existing_gen_winning]
                for x in won_tickets:
                    winning_tickets.append(x)

            # elif winning_ins.win_flavour == "BLACK":
            #     data["status"] = f"{cash_back_perc}% cashback"
            #     data["amount_won"] = existing_gen_winning.aggregate(Sum("earning"))[
            #         "earning__sum"
            #     ]
            #     won_tickets = [ticket.ticket for ticket in existing_gen_winning]
            #     for x in won_tickets:
            #         winning_tickets.append(x)

            else:
                cash_back_perc = winning_ins.get_cashback_percentage
                data["status"] = f"{cash_back_perc}% cashback"
                data["cashback_amount"] = existing_gen_winning.aggregate(Sum("earning"))["earning__sum"]
                won_tickets = [ticket.ticket for ticket in existing_gen_winning]
                for x in won_tickets:
                    winning_tickets.append(x)
        else:
            data["status"] = "lost"
            data["amount_won"] = 0

        for ticket in instant_cashout_qs:
            ticket_data = {
                "ticket": [int(x) for x in ticket.ticket.split(",")],
                "status": "lost",
            }
            if ticket.ticket in winning_tickets:
                if (winning_ins.win_flavour == "WHITE") and (winning_ins.match_type != "PERM_0"):
                    ticket_data["status"] = "won"
                else:
                    cash_back_perc = winning_ins.get_cashback_percentage
                    ticket_data["status"] = f"{cash_back_perc}% cashback"

            tickets.append(ticket_data)

        data["tickets"] = tickets
        data["system_pick"] = (
            []
            if instant_cashout_instance.system_generated_num is None
            else [int(x) for x in instant_cashout_instance.system_generated_num.split(",")]
        )

        if instant_cashout_instance.channel == "POS_AGENT":
            data["agent_id"] = instant_cashout_instance.agent_profile.user_uuid
            if (instant_cashout_instance.pin is not None) or (instant_cashout_instance.pin != ""):
                data["pin"] = instant_cashout_instance.pin
            else:
                LottoTicket().generate_ticket_pin(instant_cashout_instance.game_play_id)
                sleep(1)
                data["pin"] = instant_cashout_instance.pin

            # Add Jackpot winning checks
            data["jackpot"] = {
                "won": True if won_jackpot is not None else False,
                "win_amount": won_jackpot.amount_won if won_jackpot is not None else 0.0,
                "pin": won_jackpot.pin if won_jackpot is not None else None,
            }
        elif instant_cashout_instance.channel == "WEB":
            jackpot_winning = winning_instance.filter(won_jackpot=True).last()
            # Add Jackpot winning checks
            data["jackpot"] = {
                "won": True if jackpot_winning is not None else False,
                "win_amount": jackpot_winning.earning if jackpot_winning is not None else 0.0,
                "pin": None,
            }

        print(
            f"""

        INSTANT CASHOUT RESULT:
        {data}
        \n\n\n\n\n\n

        """
        )

        return Response(data=data, status=status.HTTP_200_OK)


# --------------------------------------------- POS AGENT RETAIL SERVICE -------------------------------------------------- #
class LotteryRetailCreation(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    serializer_class = LotteryRetailCreationSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        print(
            f"""

        CREATING ACCOUNT FOR RETAIL CUSTOMER:
        {request.data}
        \n\n\n\n\n\n

        """
        )

        agent = Agent.objects.filter(phone=request.user.phone).last()

        if not agent:
            agent = Agent.objects.filter(email=request.user.email).last()
            if not agent:
                data = {
                    "message": "Agent does not exist",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # LOGS
        RetailTicketRequestLogs.objects.create(agent=agent, request_payload=serializer.validated_data)

        # charge agent agency wallet
        amount = serializer.validated_data.get("amount")
        pin = serializer.validated_data.get("pin")

        # ----------- verify agent pin
        pos_agent_helper = PosAgentHelper(agent_instance=agent, amount=amount, pin=pin)
        if pos_agent_helper.verify_agent_transaction_pin(pin) is False:
            data = {
                "message": "Invalid pin",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # --------------------------- validate remiitance amount

        def validate_remtance_amount(amount):
            response = LottoAgentRemittanceTable().valuate_amount_to_be_added(amount=amount, agent=agent)
            if response.get("is_amount_much") is False:
                return False, None
            else:
                return True, response.get("amount")

        (
            validate_amount_in_remiitance_status,
            validate_amount_in_remiitance_status_amount,
        ) = validate_remtance_amount(amount)
        if validate_amount_in_remiitance_status is True:
            data = {
                    "message": f"Hi, please, fund your wallet with this amount {Utility.currency_formatter(validate_amount_in_remiitance_status_amount)} to continue game play",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)
        # ----------------------------------- # validate remiitance amount

        # ------------------ validate retail amount ------------------ #
        if validate_retail_amount(serialized_data=serializer.validated_data, amount=amount) is False:
            data = {
                "message": "Invalid amount",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # ------------------ validate retail amount ------------------ #

        for lottery_type, lottery_data in (serializer.validated_data).items():
            if str(lottery_type).upper() == "INSTANT_CASHOUT" and len(lottery_data) > 0:
                if AgentConstantVariables().get_is_instant_cashout_enabled() is False:
                    data = {
                        "message": "Instant cashout is undergoing maintenance",
                    }

                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                if BlackListed().can_play_instant_cashout(email=agent.email, phone=agent.phone) is False:
                    data = {
                        "message": "Instant cashout is not accessible for you at the moment",
                    }

                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)
            if str(lottery_type).upper() == "SALARY_FOR_LIFE" and len(lottery_data) > 0:
                if BlackListed().can_play_salary_for_life(email=agent.email, phone=agent.phone) is False:
                    data = {
                        "message": "salary for life is not accessible for you at the moment",
                    }

                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            elif str(lottery_type).upper() == "WYSE_CASH" and len(lottery_data) > 0:
                if agent.email not in [
                    "<EMAIL>",
                ]:
                    data = {
                        "message": "wyse cash is undergoing maintenance",
                    }

                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            # elif (
            #     str(lottery_type).upper() == "SALARY_FOR_LIFE" and len(lottery_data) > 0
            # ):
            #     if agent.email not in [
            #         "<EMAIL>",
            #     ]:
            #         data = {
            #             "message": "salary for life is undergoing maintenance",
            #         }

            #         return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # get agent play balance
        agent_wallet = AgentWallet.objects.filter(agent=agent).last()
        if agent_wallet is None:
            agent_wallet = AgentWallet.objects.create(
                agent=agent,
                agent_name=agent.name,
                agent_phone_number=agent.phone,
                agent_email=agent.email,
            )

        if agent_wallet.game_play_bal < amount:
            print("charge liberty pay wallet")

            charge_response = pos_agent_helper.charge_agent_wallet()

            # charge_response = {"message": "success"}
        else:
            charge_response = PosAgentHelper.charge_agent_play_wallet(agent_id=agent.id, amount=amount, ticket_instance="RETAIL")

        charge_res_message = charge_response.get("message")
        if charge_res_message == "success":
            del serializer.validated_data["pin"]
            del serializer.validated_data["amount"]

            combined_retail_ticket = serializer.validated_data.get("combined_ticket")
            del serializer.validated_data["combined_ticket"]

            lottery_game_ids = create_retail_lottery(agent, serializer.validated_data, combined_retail_ticket)
            data = {
                "message": "Retail lottery created successfully",
                "data": lottery_game_ids,
            }

            return Response(data=data, status=status.HTTP_200_OK)

        elif charge_response.get("message") == "failed":
            data = {
                "message": charge_res_message,
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)
        else:
            data = {
                "message": charge_res_message,
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)


class LotteryRetailCreationViaBonus(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    serializer_class = LotteryRetailCreationViaBonusSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        agent = Agent.objects.filter(phone=request.user.phone).last()
        if not agent:
            agent = Agent.objects.filter(email=request.user.email).last()

            if agent is None:
                data = {
                    "message": "agent not found",
                }

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        agent_wallet = AgentWallet.objects.filter(agent=agent).last()

        if agent_wallet is None:
            agent_bonus_amount = ConstantVariable.get_constant_variable().get("agent_bonus_amount")

            agent_wallet = AgentWallet.objects.create(
                agent=agent,
                bonus_bal=agent_bonus_amount,
                agent_name=agent.name,
                agent_phone_number=agent.phone,
                agent_email=agent.email,
            )

        if agent_wallet.is_bonus_available is True:
            return Response(
                data={"message": "Bonus balance already used"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if agent_wallet.used_bonus_bal <= 0:
            return Response(data={"message": "No active bonus"}, status=status.HTTP_400_BAD_REQUEST)

        if agent_wallet.used_bonus_bal < 100:
            return Response(
                data={"message": "Bonus balance is not enough"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        response = create_retail_bonus_lottery(agent)

        data = {
            "message": "Retail lottery created successfully",
            "data": response,
        }

        return Response(data=data, status=status.HTTP_200_OK)


class RetailLotteryHistory(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        page = request.GET.get("page", 1)

        filter_by = request.GET.get("filter_by", "bought")
        date = request.GET.get("date", None)
        game_type = request.GET.get("game_type", None)

        agent = Agent.objects.filter(phone=request.user.phone).last()
        if agent is None:
            agent = Agent.objects.filter(email=request.user.email).last()

            if agent is None:
                data = {
                    "message": "agent not found",
                }

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if filter_by == "bought":
            retail_lottery_qs = BoughtLotteryTickets.objects.filter(agent=agent, paid=True, is_bonus_ticket=False).distinct("game_id")
            if date is not None:
                retail_lottery_qs = retail_lottery_qs.filter(created_at__date=date)

            if game_type is not None:
                retail_lottery_qs = retail_lottery_qs.filter(game_type=game_type.upper())

            if retail_lottery_qs.count() == 0:
                data = []

                return Response(data=data, status=status.HTTP_200_OK)

            paginated_data = CustomPaginator.paginate(request, retail_lottery_qs, page)

            serilaizer = GetRetailLotteryHistorySerializer(paginated_data, many=True)

            return Response(data=serilaizer.data, status=status.HTTP_200_OK)

        else:
            retail_lottery_qs = BoughtLotteryTickets.objects.filter(agent=agent, paid=True, is_available=False, is_bonus_ticket=False).distinct(
                "game_id"
            )
            if date is not None:
                retail_lottery_qs = retail_lottery_qs.filter(created_at__date=date)

            if game_type is not None:
                retail_lottery_qs = retail_lottery_qs.filter(game_type=game_type.upper())

            if retail_lottery_qs.count() == 0:
                data = []

                return Response(data=data, status=status.HTTP_200_OK)

            paginated_data = CustomPaginator.paginate(request, retail_lottery_qs, page)

            serilaizer = GetRetailLotteryHistorySerializer(paginated_data, many=True)

            return Response(data=serilaizer.data, status=status.HTTP_200_OK)


class RetailLotteryHistoryDetail(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request, game_id):
        agent = Agent.objects.filter(phone=request.user.phone).last()
        if agent is None:
            agent = Agent.objects.filter(email=request.user.email).last()

            if agent is None:
                data = {
                    "message": "agent not found",
                }

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        retail_lottery_qs = BoughtLotteryTickets.objects.filter(game_id=game_id, agent=agent)

        if not retail_lottery_qs.exists():
            data = []

            return Response(data=data, status=status.HTTP_200_OK)

        retail_lottery_instance = retail_lottery_qs.last()

        if retail_lottery_instance.game_type == "SALARY_FOR_LIFE" or retail_lottery_instance.game_type == "INSTANT_CASHOUT":
            data = {
                "ussd_code": retail_lottery_instance.ussd_code,
                "no_of_lines": retail_lottery_qs.count(),
                "amount": retail_lottery_qs.aggregate(Sum("amount"))["amount__sum"],
                "pontential_winning": retail_lottery_instance.pontential_win,
                "tickets": [],
            }

            ticket_data = []
            for ticket in retail_lottery_qs:
                ticket_data.append(
                    {
                        "ticket": [int(x) for x in ticket.ticket.split(",")],
                    }
                )

            data["tickets"] = ticket_data

        elif retail_lottery_instance.game_type == "WYSE_CASH":
            data = {
                "ussd_code": retail_lottery_instance.ussd_code,
                "no_of_lines": retail_lottery_qs.count(),
                "amount": retail_lottery_qs.aggregate(Sum("amount"))["amount__sum"],
                "pontential_winning": retail_lottery_qs.aggregate(Sum("pontential_win"))["pontential_win__sum"],
            }

            ticket_data = []
            for ticket in retail_lottery_qs:
                ticket_data.append(
                    {
                        "ticket": [x for x in ticket.ticket.split(",")],
                    }
                )

            data["tickets"] = ticket_data

        else:
            data = []

        return Response(data=data, status=status.HTTP_200_OK)


class RetailBonusLotteryHistory(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        page = request.GET.get("page", 1)

        filter_by = request.GET.get("filter_by", "bought")
        date = request.GET.get("date", None)
        game_type = request.GET.get("game_type", None)

        agent = Agent.objects.filter(phone=request.user.phone).last()
        if agent is None:
            agent = Agent.objects.filter(email=request.user.email).last()

            if agent is None:
                data = {
                    "message": "agent not found",
                }

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if filter_by == "bought":
            retail_lottery_qs = BoughtLotteryTickets.objects.filter(agent=agent, paid=True, is_bonus_ticket=True).distinct("game_id")
            if date is not None:
                retail_lottery_qs = retail_lottery_qs.filter(created_at__date=date)

            if game_type is not None:
                retail_lottery_qs = retail_lottery_qs.filter(game_type=game_type.upper())

            if retail_lottery_qs.count() == 0:
                data = []

                return Response(data=data, status=status.HTTP_200_OK)

            paginated_data = CustomPaginator.paginate(request, retail_lottery_qs, page)

            serilaizer = GetRetailLotteryHistorySerializer(paginated_data, many=True)

            return Response(data=serilaizer.data, status=status.HTTP_200_OK)

        else:
            retail_lottery_qs = BoughtLotteryTickets.objects.filter(agent=agent, paid=True, is_available=False, is_bonus_ticket=True).distinct(
                "game_id"
            )
            if date is not None:
                retail_lottery_qs = retail_lottery_qs.filter(created_at__date=date)

            if game_type is not None:
                retail_lottery_qs = retail_lottery_qs.filter(game_type=game_type.upper())

            if retail_lottery_qs.count() == 0:
                data = []

                return Response(data=data, status=status.HTTP_200_OK)

            paginated_data = CustomPaginator.paginate(request, retail_lottery_qs, page)

            serilaizer = GetRetailLotteryHistorySerializer(paginated_data, many=True)

            return Response(data=serilaizer.data, status=status.HTTP_200_OK)


class RetailSoccerCashCreation(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = [IsAuthenticated]

    serializer_class = SoccerCashGameSelectSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        if user_profile is None:
            data = {"message": "User not found"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SoccerPredictionApiview(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    serializer_class = LotteryRetailSoccerCashSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        # phone = ""
        # agent = Agent.objects.filter(phone=phone).last()

        agent = Agent.objects.filter(phone=request.user.phone).last()
        if agent is None:
            agent = Agent.objects.filter(email=request.user.email).last()

            if agent is None:
                data = {
                    "message": "agent not found",
                }

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        game_play = serializer.validated_data.get("game_play")
        amount = serializer.validated_data.get("amount")
        pin = serializer.validated_data.get("pin")
        play_type = serializer.validated_data.get("play_type")
        single = serializer.validated_data.get("single")

        if verify_stake_amount(game_play, amount):
            create_ticket_response = create_prediction_ticket_object(
                game_play=game_play,
                agent=agent,
                single=single,
                amount=amount,
                pin=pin,
                play_type=play_type,
            )

            print(create_ticket_response)

            if create_ticket_response.get("succeeded") is True:
                return Response(create_ticket_response, status=status.HTTP_200_OK)

            else:
                return Response(create_ticket_response, status=status.HTTP_400_BAD_REQUEST)

        else:
            response = {
                "succeeded": False,
                "message": "amount not equal",
            }
        return Response(response, status=status.HTTP_400_BAD_REQUEST)


class AgentFundingViaAgencyBankingWebhook(APIView):
    authentication_classes = [AgencyBankingFundingAuthentication]

    def post(self, request):
        data = request.data

        """

        SAMPLE DATA:
        {'lotto_agent_user_id': '00000000000000000000000', 'lotto_agent_user_phone': '23463950553884216293', 'status': 'SUCCESSFUL', 'transaction_type': 'FUND_LOTTO_WALLET', 'amount': 10.0, 'liberty_commission': 0.0, 'sms_charge': 0.0, 'balance_before': 9160.0, 'balance_after': 9170.0, 'source_account_name': 'CHUKWUEMEKA NWAOMA OJUKWU 1', 'source_nuban': None, 'liberty_reference': 'LGLP_SND_BUDDY-8b125825-672e-493f-a34a-751e990cabe0', 'is_reversed': False, 'type_of_user': 'MERCHANT'}


        """

        if isinstance(data, str):
            data = json.loads(data)

        reference = data.get("liberty_reference")
        agent_user_id = data.get("lotto_agent_user_id")

        raw_funding = RawFundingData.objects.create(
            reference=reference,
            payload=data,
            source="AGENCY_BANKING",
        )

        # print(f"""
        #       agent_user_id = data.get("lotto_agent_user_id"): {data.get("lotto_agent_user_id")}
        #       """)
        amount = data.get("amount")

        data.get("lotto_agent_user_phone")

        agent_wallet = AgentWallet.objects.filter(agent__user_id=agent_user_id).last()

        if agent_wallet is None:
            response = {"message": "agent or wallet doesn't exist", "status": "Failed"}
            return Response(data=response, status=status.HTTP_404_NOT_FOUND)

        agent_instance = agent_wallet.agent

        agent_funding_table_instance = AgentFundingTable.objects.filter(reference=reference).first()

        if agent_funding_table_instance is not None:
            print(
                "agent vfd funding already verified \n\n\n\n\n",
            )
            response = {
                "message": "agent vfd funding already verified",
                "status": "Failed",
            }

            raw_funding.lotto_response = response
            raw_funding.save()

            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

        verify_vfd_trans = verify_agency_banking_transaction_for_vfd_funding(reference)

        raw_funding.verification_response_payload = verify_vfd_trans
        raw_funding.save()

        if verify_vfd_trans["status"] is True:
            res = verify_vfd_trans["data"]
            amount = float(amount)

            if (
                res.get("data", {}).get("status") == "SUCCESSFUL"
                and res.get("data", {}).get("transaction_type") == "FUND_LOTTO_WALLET"
                and res.get("data", {}).get("liberty_reference") == reference
                and res.get("data", {}).get("amount") == amount
                and res.get("data", {}).get("lotto_agent_user_id") == agent_user_id
            ):
                debit_credit_record = DebitCreditRecord.create_record(
                    phone_number=agent_instance.phone,
                    amount=amount,
                    channel="POS/MOBILE",
                    reference=reference,
                    transaction_type="CREDIT",
                )

                AgentFundingTable.objects.create(
                    agent=agent_instance,
                    amount=amount,
                    source="AGENCY_BANKING",
                    reference=reference,
                    payload=data,
                    is_verified=True,
                    verification_response_payload=verify_vfd_trans,
                    verification_status="VERIFIED",
                    debit_credit_id=debit_credit_record.debit_credit_record_id,
                )


                try:
                    WalletFundingAnalytics.add_or_create_record(
                        amount = amount
                    )
                except:
                    pass



                raw_funding.authorized = True
                raw_funding.save()

                if AgentWalletTransaction.objects.filter(transaction_reference=reference).exists():
                    response = {
                        "message": "Funding Already Processed",
                        "status": "Failed",
                    }

                    raw_funding.lotto_response = response
                    raw_funding.save()

                    return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

                payload = {
                    "transaction_from": "SEND_TO_LOTTO_FUNDING",
                }

                UserWallet.fund_wallet(
                    user=agent_instance,
                    amount=amount,
                    channel="POS",
                    transaction_id=debit_credit_record.debit_credit_record_id,
                    user_wallet_type="GAME_PLAY_WALLET",
                    **payload,
                )

                data = {"message": "data received", "status": "Success"}
                raw_funding.lotto_response = data
                raw_funding.save()

                return Response(data=data, status=status.HTTP_200_OK)

            else:
                response = {
                    "message": "Funding Cannot Be Verified",
                    "status": "Failed",
                }

                raw_funding.lotto_response = response
                raw_funding.save()

                return Response(data=response, status=status.HTTP_400_BAD_REQUEST)
        else:
            response = {
                "message": "Funding Verification Data Is Bad",
                "status": "Failed",
            }

            raw_funding.lotto_response = response
            raw_funding.save()

            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)


class AgentEnquiry(APIView):
    def get(self, request, phone):
        # phone = request.GET.get("phone")
        phone_number = LotteryModel.format_number_from_back_add_234(phone)
        agent = Agent.objects.filter(phone=phone_number).last()
        if agent is None:
            data = {"message": "agent not found", "status": "error"}
            return Response(data=data, status=status.HTTP_404_NOT_FOUND)

        # serializer = CreateAgentSerializer(agent)
        # serializer_data = serializer.data
        # serializer_data.update({"status": "success"})

        serializer_data = {
            "status": "success",
            "message": "agent found",
            "data": {
                "user_uuid": agent.user_uuid,
                "phone": agent.phone,
                "name": agent.name,
                "email": agent.email,
                "address": agent.address,
            },
        }

        return Response(data=serializer_data, status=status.HTTP_200_OK)


class GetAgentWallet(APIView):
    """
    GET AGENT WALLET AND GET GAME AVAILABLE FOR AGENT
    """

    def get(self, request, user_uuid):
        print(
            f"""

        USER WALLET: {user_uuid}
        user_uuid: {user_uuid}
        \n\n\n\n\n

        """
        )

        agent = Agent.objects.filter(user_uuid=user_uuid).last()
        if agent is None:
            data = {"message": "agent not found", "status": "error"}
            return Response(data=data, status=status.HTTP_404_NOT_FOUND)

        agent_wallet_instance = AgentWallet.objects.filter(agent=agent).last()
        if agent_wallet_instance is None:
            data = {"message": "agent wallet not found", "status": "error"}
            return Response(data=data, status=status.HTTP_404_NOT_FOUND)

        banks = []
        if agent_wallet_instance.woven_account is not None:
            # banks.append(
            #     {
            #         "bank_name": agent_wallet_instance.woven_account.bank_name,
            #         "account_name": agent_wallet_instance.woven_account.acct_name,
            #         "account_number": agent_wallet_instance.woven_account.vnuban,
            #     }
            # )
            pass

        if agent_wallet_instance.vfd_account is not None:
            if "wise" in str(agent.last_name).casefold():
                pass
            else:
                banks.append(
                    {
                        "bank_name": agent_wallet_instance.vfd_account.bank_name,
                        "account_name": agent_wallet_instance.vfd_account.acct_name,
                        "account_number": agent_wallet_instance.vfd_account.vnuban,
                    }
                )

        commission_value = WinwiseEmployeeSalary.objects.filter(agent=agent).last()
        if commission_value is None:
            commission_value = 0
        else:
            commission_value = commission_value.amount_to_be_paid

        data = {
            "status": "success",
            "bonus_bal": agent_wallet_instance.bonus_bal,
            "winnings_bal": agent_wallet_instance.winnings_bal,
            "game_play_bal": agent_wallet_instance.game_play_bal,
            "available_games": AgentConstantVariables().get_available_games_list(),
            "bank_details": banks,
            "is_suspended": agent.is_suspended,
            "debt": LottoAgentRemittanceTable().is_remittance_due(agent.id),
            "commission_value": commission_value,
        }

        return Response(data=data, status=status.HTTP_200_OK)


class DynamicDataApiView(APIView):
    def get(self, request):
        from main.signals import winnings

        const_object = AgentConstantVariables()
        gen_const_var = ConstantVariable()
        const_variable = ConstantVariable.objects.last()
        soccer_multiplier = FootballTable.get_personal_soccer_multiplier()

        is_promotion_running = const_object.is_promotion_running()
        promotion_games = const_object.get_promotion_games()
        promotion_games_data = []

        for promotion_game in promotion_games:
            promotion_games_data.append(
                {
                    "game_name": promotion_game.game_type,
                    "game_id": promotion_game.id,
                    "image_url": promotion_game.image_url,
                }
            )

        """
        SALARY FOR LIFE GAME PRICES
        """
        salary_for_life_price_model = SalaryForLifePriceModel()

        mobile_sal_4_life_game_price = []
        mobile_price_data = salary_for_life_price_model.ticket_price(channel="POS")
        for mobile_item in mobile_price_data.values():
            mobile_sal_4_life_game_price.append(
                {
                    "stake_amount": mobile_item.get("ticket_price")
                    + mobile_item.get("woven_service_charge", 0)
                    + mobile_item.get("africastalking_charge", 0),
                    "line_number": mobile_item.get("line_number"),
                    "winning_amount": mobile_item.get("potential_winning"),
                }
            )

        web_sal_4_life_game_price = []
        web_price_data = salary_for_life_price_model.ticket_price(channel="WEB")
        for web_item in web_price_data.values():
            web_sal_4_life_game_price.append(
                {
                    "line_number": web_item.get("line_number"),
                    # "ticket_count": web_item.get("line_number"),
                    "stake_amount": web_item.get("ticket_price") + web_item.get("woven_service_charge", 0) + web_item.get("africastalking_charge", 0),
                    "winning_amount": web_item.get("potential_winning"),
                }
            )

        """
        QUICK PRICING UPDATE
        """
        quicka_price_model = QuikaPriceModel()
        mobile_quicka_price = []
        mobile_quicka_price_data = quicka_price_model.ticket_price(channel="POS")
        for mobile_item in mobile_quicka_price_data.values():
            mobile_quicka_price.append(
                {
                    "ticket_line": mobile_item.get("ticket_line"),
                    "ticket_count": mobile_item.get("line_number"),
                    "stake_amount": mobile_item.get("ticket_price")
                    + mobile_item.get("illusion_price", 0)
                    + mobile_item.get("woven_service_charge", 0)
                    + mobile_item.get("africastalking_charge", 0),
                    "winning_amount": mobile_item.get("potential_winning"),
                }
            )

        web_quicka_price = []
        web_quicka_price_data = quicka_price_model.ticket_price(channel="WEB")
        for web_item in web_quicka_price_data.values():
            web_quicka_price.append(
                {
                    "ticket_line": web_item.get("ticket_line"),
                    "ticket_count": web_item.get("line_number"),
                    "stake_amount": web_item.get("ticket_price")
                    + web_item.get("illusion_price", 0)
                    + web_item.get("woven_service_charge", 0)
                    + web_item.get("africastalking_charge", 0),
                    "winning_amount": web_item.get("potential_winning"),
                }
            )

        new_quika_pos_game_prices_json = PosNewQuikaPriceModel().ticket_price()
        new_quika_pos_game_prices = []

        for key, value in new_quika_pos_game_prices_json.items():
            new_quika_pos_game_prices.append(
                {
                    "ticket_line": key,
                    "ticket_count": 1,
                    "stake_amount": value.get("ticket_price"),
                    "winning_amount": value.get("potential_winning"),
                }
            )

        kenya_games_draw_time = [
            {
                "name": "Tokyo",
                "draw_time": "9:00 AM",
            },
            {
                "name": "Ultimate",
                "draw_time": "12:00 PM",
            },
            {
                "name": "Angelina Special",
                "draw_time": "3:00 PM",
            },
            {
                "name": "Supreme Max",
                "draw_time": "6:00 PM",
            },
            {
                "name": "Barnabas",
                "draw_time": "9:00 PM",
            },
        ]

        salary_for_life_game_draw_times = [
            {
                "draw_time": "9:00 AM",
            },
            {
                "draw_time": "11:00 AM",
            },
            {
                "draw_time": "1:00 PM",
            },
            {
                "draw_time": "5:00 PM",
            },
            {
                "draw_time": "7:00 PM",
            },
            {
                "draw_time": "9:00 PM",
            },
        ]

        banker_game_draw_time = [
            {
                "draw_time": "8:00 AM",
            },
            {
                "draw_time": "8:30 AM",
            },
            {
                "draw_time": "9:00 AM",
            },
            {
                "draw_time": "9:30 AM",
            },
            {
                "draw_time": "10:00 AM",
            },
            {
                "draw_time": "10:30 AM",
            },
            {
                "draw_time": "11:00 AM",
            },
            {
                "draw_time": "11:30 AM",
            },
            {
                "draw_time": "12:00 PM",
            },
            {
                "draw_time": "12:30 PM",
            },
            {
                "draw_time": "1:00 PM",
            },
            {
                "draw_time": "1:30 PM",
            },
            {
                "draw_time": "2:00 PM",
            },
            {
                "draw_time": "2:30 PM",
            },
            {
                "draw_time": "3:00 PM",
            },
            {
                "draw_time": "3:30 PM",
            },
            {
                "draw_time": "4:00 PM",
            },
            {
                "draw_time": "4:30 PM",
            },
            {
                "draw_time": "5:00 PM",
            },
            {
                "draw_time": "5:30 PM",
            },
            {
                "draw_time": "6:00 PM",
            },
            {
                "draw_time": "6:30 PM",
            },
            {
                "draw_time": "7:00 PM",
            },
            {
                "draw_time": "7:30 PM",
            },
            {
                "draw_time": "8:00 PM",
            },
            {
                "draw_time": "8:30 PM",
            },
            {
                "draw_time": "9:00 PM",
            },
        ]

        wyse_cash_game_draw_time = [
            {
                "draw_time": "9:00 AM",
            },
            {
                "draw_time": "11:00 AM",
            },
            {
                "draw_time": "1:00 PM",
            },
            {
                "draw_time": "5:00 PM",
            },
            {
                "draw_time": "7:00 PM",
            },
            {
                "draw_time": "9:00 PM",
            },
        ]
        # africa lotto constant
        africa_lotto_constant = AfricaLottoConstants.objects.last()
        if africa_lotto_constant is None:
            africa_lotto_constant = AfricaLottoConstants.objects.create(draw_start_time_period="AM")
        data = {
            "status": "success",
            "phone_number_is_required": const_object.get_phone_number_is_required(),
            "banker_multiplier": BankerConstant.get_multiplier(),
            "banker_minimum_stake": BankerConstant.get_banker_minimum_stake(),
            "instant_cashout_line_restriction": gen_const_var.instant_cash_out_line_restriction(),
            "personalSoccerMultiplier": {
                "max": max(soccer_multiplier),
                "min": min(soccer_multiplier),
            },
            "banker_draw_time": const_variable.banker_draw_countdown_time,
            # "quika_pricing": LottoObjectConverter(winnings(const_variable)).quika_pricing(),
            # "quika_pricing": quick_pricing_update(),
            "quika_pricing": mobile_quicka_price,
            "new_quika_pricing": new_quika_pos_game_prices,
            "web_quika_pricing": web_quicka_price,
            "quika_line_restriction": gen_const_var.quika_line_restriction_and_amount(),
            "africa_lotto_minimum_stake_amount": int(africa_lotto_constant.minimum_stake),
            "africa_lotto_perm_minimum_stake_amount": int(africa_lotto_constant.per_minimum_stake),
            "africa_lotto_perm_maximum_balls": int(africa_lotto_constant.perm_maximum_balls),
            "africa_lotto_banker_maximum_balls": int(africa_lotto_constant.banker_maximum_balls),
            "africa_lotto_against_top_maximum_balls": int(africa_lotto_constant.against_top_maximum_balls),
            "africa_lotto_against_bottom_maximum_balls": int(africa_lotto_constant.against_bottom_maximum_balls),
            "kenya_games_draw_time": kenya_games_draw_time,
            "salary_for_life_game_draw_times": salary_for_life_game_draw_times,
            "banker_game_draw_time": banker_game_draw_time,
            "wyse_cash_game_draw_time": wyse_cash_game_draw_time,
            "prices": {
                "salary_for_life": mobile_sal_4_life_game_price,
                "web_salary_for_life": web_sal_4_life_game_price,
                # "instant_cashout": LottoObjectConverter(
                #     LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents()
                # ).to_list_of_dictionary(),
                "instant_cashout": LottoObjectConverter(winnings(const_variable)).i_cash_pricing(),
                "web_instant_cashout": LottoObjectConverter(winnings(const_variable)).web_i_cash_pricing(),
                "wyse_cash": WyseCashObjConverter(
                    LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents()
                ).convert_to_list_of_object(channel="POS"),
                "web_wyse_cash": WyseCashObjConverter(
                    LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents()
                ).convert_to_list_of_object(channel="WEB"),
                # "quika": gen_const_var.quika_admin_stake_and_winning_amount(),
                "quika": mobile_quicka_price,
                "web_quika": web_quicka_price,
                "virtual_soccer": LottoObjectConverter(winnings(const_variable)).virtual_soccer_pricing(channel="WEB"),
                "web_virtual_soccer": LottoObjectConverter(winnings(const_variable)).virtual_soccer_pricing(channel="WEB"),
            },
            "is_promotion_running": is_promotion_running,
            "promotion_games": promotion_games_data,
        }
        return Response(data=data, status=status.HTTP_200_OK)


class LottoAgentGuarantorAPIView(APIView):
    def get(self, request, *args, **kwargs):
        query_params = request.GET
        auth = request.headers.get("Authorization")
        if auth is not None:
            token_type, _, credentials = auth.partition(" ")
            if token_type != settings.SELF_SERVICE_AUTH.split(" ")[0] and credentials != settings.SELF_SERVICE_AUTH.split(" ")[-1]:
                data = {
                    "status": False,
                    "message": "invalid authentication credentials.",
                }
                return Response(data=data, status=status.HTTP_401_UNAUTHORIZED)
            else:
                send_guarantor_verification_details.delay(verification_id=query_params.get("verification_unique_id"))
                data = {
                    "status": True,
                    "message": "KYC3 verification details has been triggered.",
                }
                return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {
                "status": False,
                "message": "authentication credentials were not provided.",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request, *args, **kwargs):
        request_data = request.data
        serializer = KYC3Serializer(data=request_data)
        serializer.is_valid(raise_exception=True)
        check_record = LottoAgentGuarantorDetail.objects.filter(agent_email=serializer.validated_data.get("agent_email")).first()
        if check_record is not None:
            data = {
                "status": False,
                "message": "agent's guarantor details (KYC3) exists already.",
                "is_verified": check_record.verified,
            }
        else:
            guarantor_details = LottoAgentGuarantorDetail.create_agent_record(libertypay_request=request_data, **serializer.validated_data)
            guarantor_verification_messenger.apply_async(queue="celery1", args=[guarantor_details.verification_id])
            data = {
                "status": True,
                "message": "agent's guarantor details (KYC3) was submitted succesfully.",
                "is_verified": guarantor_details.verified,
            }
        return Response(data=data, status=status.HTTP_201_CREATED)


class LibertyPayVfdFundingCallback(APIView):
    authentication_classes = [AgencyBankingFundingAuthentication]

    def post(self, request):
        request_data = request.data

        print("type of data: ", type(request_data))
        print("data: ", request_data, "\n\n\n")

        if isinstance(request_data, str):
            try:
                data = json.loads(request_data)
            except Exception:
                try:
                    data = ast.literal_eval(request_data)
                except Exception:
                    return Response(
                        data={"MESSAGE": "ERROR PROCESSING DATA"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
        else:
            data = request_data

        """
        SAMPLE RESPONSE:
        {
            "amount":4500.0,
            "user_id":1,
            "reference":"LGLP_FND_COLL_ACCT-**********-6a2a4837-f71f-465e-aed9-bab8a62d44c3",
            "agent_phone":"234ASSUREDREF",
            "account_number":"**********",
            "liberty_commission":0.0
        }
        """

        reference = data.get("reference")
        agent_user_id = data.get("user_id")
        amount = data.get("amount")
        data.get("liberty_commission")

        data.get("lotto_agent_user_phone")
        data.get("account_number")
        data.get("transaction_type")

        raw_funding = RawFundingData.objects.create(
            reference=reference,
            payload=data,
            source="AGENCY_BANKING_VFD",
        )

        # if transaction_type == "FUND_COLLECTION_ACCOUNT":
        #     source = "AGENCY_BANKING_VFD"
        # elif transaction_type == "FUND_LOTTO_WALLET":
        #     soruce = "AGENCY_BANKING"
        # else:
        #     data = {
        #         "message": "transaction type not supported",
        #         "status": "Success",
        #     }
        #     return Response(data=data, status=status.HTTP_200_OK)
        # amount = float(amount) - float(liberty_commission)

        # Check If Agent Exists
        # agent_instance = Agent.objects.filter(user_id=agent_user_id).last()

        agent_wallet = AgentWallet.objects.filter(agent__user_id=agent_user_id).last()

        if agent_wallet is None:
            response = {"message": "agent or wallet doesn't exist", "status": "Failed"}
            return Response(data=response, status=status.HTTP_404_NOT_FOUND)

        agent_instance = agent_wallet.agent

        # Check If Reference Exists In Funding Table
        agent_funding_table_instance = AgentFundingTable.objects.filter(reference=reference).first()

        if agent_funding_table_instance is not None:
            print(
                "agent vfd funding already verified \n\n\n\n\n",
            )
            response = {
                "message": "agent vfd funding already verified",
                "status": "Failed",
            }

            raw_funding.lotto_response = response
            raw_funding.save()

            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

        # Verify Transaction
        verify_vfd_trans = verify_agency_banking_transaction_for_vfd_funding(reference)

        raw_funding.verification_response_payload = verify_vfd_trans
        raw_funding.save()

        if verify_vfd_trans["status"] is True:
            res = verify_vfd_trans["data"]
            amount = float(amount)

            try:
                RemunerationChargeRemittance().handle_remittance_charge_remuneration(agent_instance.id, amount)
            except Exception:
                pass

            print(
                f"""
                {res.get("data", {}).get("status")}: SUCCESSFUL
                type of {res.get("data", {}).get("status")}: {type(res.get("data", {}).get("status"))}
                {res.get("data", {}).get("transaction_type")}: FUND_COLLECTION_ACCOUNT
                type of {res.get("data", {}).get("transaction_type")}: {type(res.get("data", {}).get("transaction_type"))}
                {res.get("data", {}).get("liberty_reference")}: {reference}
                type of {res.get("data", {}).get("liberty_reference")}: {type(res.get("data", {}).get("liberty_reference"))}
                {res.get("data", {}).get("amount")}: {amount}
                type of  {res.get("data", {}).get("amount")}: {type(res.get("data", {}).get("amount"))}
                typw of {amount}: {type(amount)}
                {res.get("data", {}).get("lotto_agent_user_id")}: {agent_user_id}
                type of {res.get("data", {}).get("lotto_agent_user_id")}: {type(res.get("data", {}).get("lotto_agent_user_id"))}
                \n\n\n\n
            """
            )

            if (
                res.get("data", {}).get("status") == "SUCCESSFUL"
                and res.get("data", {}).get("transaction_type") == "FUND_COLLECTION_ACCOUNT"
                and res.get("data", {}).get("liberty_reference") == reference
                and res.get("data", {}).get("amount") == float(amount)
                and int(res.get("data", {}).get("lotto_agent_user_id")) == int(agent_user_id)
            ):
                debit_credit_record = DebitCreditRecord.create_record(
                    phone_number=agent_instance.phone,
                    amount=amount,
                    channel="POS/MOBILE",
                    reference=reference,
                    transaction_type="CREDIT",
                )

                AgentFundingTable.objects.create(
                    agent=agent_instance,
                    amount=amount,
                    source="AGENCY_BANKING_VFD",
                    reference=reference,
                    payload=data,
                    is_verified=True,
                    verification_response_payload=verify_vfd_trans,
                    verification_status="VERIFIED",
                    debit_credit_id=debit_credit_record.debit_credit_record_id,
                )

                raw_funding.authorized = True
                raw_funding.save()

                if AgentWalletTransaction.objects.filter(transaction_reference=reference).exists():
                    response = {
                        "message": "Funding Already Processed",
                        "status": "Failed",
                    }

                    raw_funding.lotto_response = response
                    raw_funding.save()

                    return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

                payload = {
                    "transaction_from": "VFD_ACCOUNT_FUNDING",
                }

                UserWallet.fund_wallet(
                    user=agent_instance,
                    amount=amount,
                    channel="POS",
                    transaction_id=debit_credit_record.debit_credit_record_id,
                    user_wallet_type="GAME_PLAY_WALLET",
                    **payload,
                )

                data = {"message": "data received", "status": "Success"}
                raw_funding.lotto_response = data
                raw_funding.save()

                return Response(data=data, status=status.HTTP_200_OK)

            else:
                response = {
                    "message": "Funding Cannot Be Verified",
                    "status": "Failed",
                }

                raw_funding.lotto_response = response
                raw_funding.save()

                return Response(data=response, status=status.HTTP_400_BAD_REQUEST)
        else:
            response = {
                "message": "Funding Verification Data Is Bad",
                "status": "Failed",
            }

            raw_funding.lotto_response = response
            raw_funding.save()

            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)


class GamePricesApiView(APIView):
    def get(self, request):
        data = {
            "status": "success",
            "data": {
                "salary_for_life": LottoTicket.salary_for_life_stake_amount_pontential_winning_for_lotto_agents(),
                "instant_cashout": LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(),
                "wyse_cash": LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(),
            },
        }
        return Response(data=data, status=status.HTTP_200_OK)


class ManuallyPreFundAgentApiView(APIView):
    serializer_class = ManuallyPreFundAgentApiViewSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        filter_obj = serializer.validated_data.get("phone_or_email")

        agent = Agent.objects.filter(email=filter_obj).last()
        if agent is None:
            try:
                phone = LotteryModel.format_number_from_back_add_234(filter_obj)
            except Exception:
                data = {"message": "Invalid phone number"}

                return Response(data=data, status=status.HTTP_200_OK)

            agent = Agent.objects.filter(phone=phone).last()

            if agent is None:
                data = {
                    "status": False,
                    "message": "Agent not found",
                }
                return Response(data=data, status=status.HTTP_200_OK)

        if agent.agent_type != "LOTTO_AGENT":
            data = {
                "message": "Agent is not a lotto agent",
            }

            return Response(data=data, status=status.HTTP_200_OK)

        agent_wallet = AgentWallet.objects.filter(agent=agent).last()
        if agent_wallet is None:
            agent_wallet = AgentWallet.objects.create(
                agent=agent,
                agent_name=agent.name,
                agent_email=agent.email,
                agent_phone_number=agent.phone,
            )

        agent_pre_funding_amt = AgentConstantVariables().get_lotto_agent_pre_funding_amount()

        agent_wallet_transaction = AgentWalletTransaction.objects.filter(agent_wallet=agent_wallet, transaction_from="PRE_FUNDING")
        if agent_wallet_transaction.exists():
            data = {
                "message": "Agent has already been pre funded",
            }
            return Response(data=data, status=status.HTTP_200_OK)

        if agent_wallet.game_play_bal > 0:
            agent_wallet.game_play_bal = 0
            agent_wallet.commission_rewarded = 0
            agent_wallet.commission_bal = 0
            agent_wallet.transaction_from = "DEBITED_FOR_MANUAL_PRE_FUNDING"
            agent_wallet.save()

        agent_wallet = AgentWallet.objects.filter(agent=agent).last()

        agent_wallet.game_play_bal += agent_pre_funding_amt
        agent_wallet.transaction_from = "PRE_FUNDING"
        agent_wallet.save()

        data = {
            "message": "Agent pre funded successfully",
            "data": {
                "agent": agent.email,
                "amount": agent_pre_funding_amt,
                "balance": agent_wallet.game_play_bal,
            },
        }

        return Response(data=data, status=status.HTTP_200_OK)


class LibertyPayVFDPayoutVerification(APIView):
    serializer_class = LibertyPayVFDPayoutVerificationSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        unique_payout_id = serializer.validated_data.get("unique_payout_id")
        vfd_disbursement_helper = VfdDisbursementHelperFunc()
        verify_payout_response = vfd_disbursement_helper.verify_payout(unique_payout_id)

        return Response(data=verify_payout_response, status=status.HTTP_200_OK)


class QuikaLottoGamePlay(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request, instance=None):
        # Agent---------------------

        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = MultipleLottoPlaySerializer(
            data=request.data,
            context={"agent_instance": agent_instance, "lotto_type": "QUIKA"},
        )

        serializer.is_valid(raise_exception=True)

        amounts = [play["amount"] for play in serializer.data["lottery_play"]]
        total_amount = sum(amounts)

        # validate_remittance_amount
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(amount=total_amount, agent=agent_instance)
        # print(response)
        if response.get("is_amount_much"):
            response = {
                "message": f"Hi, kindly top up your lottowallet with this amount {Utility.currency_formatter(response.get('amount'))} to continue game play"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        phone = serializer.data["phone_number"]

        if phone == "" or phone is None:
            phone = agent_instance.phone

        serializer.data["phone_number"] = agent_instance.phone

        phone_no = LotteryModel.format_number_from_back_add_234(phone)

        # if AgentConstantVariables().get_phone_number_is_required() is True:

        # if agent_instance.phone == phone_no:

        #     response = {"message": "You cannot use your own phone number"}
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # if phone is None or phone == "":
        #     phone = _agent_instance.phone

        # serializer.data["phone_number"] = phone

        # get or create user profile
        user_instance = UserProfile.create_user_profile_if_none_exist(phone_no=phone_no, channel="POS")
        response = PlayMultipleLottoTicket(
            request_body=serializer.data,
            lotto_type="QUIKA",
            agent_instance=agent_instance,
            channel="pos",
            player=user_instance,
            phone_no=phone_no,
        ).payment_game_check()

        return Response(response, status=status.HTTP_200_OK)


class InstantCashOutMultiplePlay(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request, instance=None):
        # print("----------------> INSTANT CASHOUT MULTIPLE REQUEST BODY <----------------\n\n\n")
        # print(request.body)
        # print("----------------> INSTANT CASHOUT MULTIPLE REQUEST BODY <----------------\n\n\n")
        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        if BlackListed().can_play_instant_cashout(email=agent_instance.email, phone=agent_instance.phone) is False:
            data = {
                "message": "Instant cashout is not accessible for you at the moment",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        serializer = MultipleLottoPlaySerializer(
            data=request.data,
            context={"agent_instance": agent_instance, "lotto_type": "INSTANT_CASHOUT"},
        )

        serializer.is_valid(raise_exception=True)

        amounts = [play["amount"] for play in serializer.data["lottery_play"]]
        total_amount = sum(amounts)

        # validate_remittance_amount
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(amount=total_amount, agent=agent_instance)
        # print(response)
        if response.get("is_amount_much"):
            response = {
                "message": f"Hi, kindly top up your lottowallet with this amount {Utility.currency_formatter(response.get('amount'))} to continue game play"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        phone = serializer.data["phone_number"]
        if phone == "" or phone is None:
            phone = agent_instance.phone

        serializer.data["phone_number"] = agent_instance.phone

        phone_no = LotteryModel.format_number_from_back_add_234(phone)

        # get or create user profile
        user_instance = UserProfile.create_user_profile_if_none_exist(phone_no=phone_no, channel="POS")
        response = PlayMultipleLottoTicket(
            request_body=serializer.data,
            lotto_type="INSTANT_CASHOUT",
            agent_instance=agent_instance,
            channel="pos",
            player=user_instance,
            phone_no=phone_no,
        ).payment_game_check()

        return Response(response, status=status.HTTP_200_OK)


class MoveBonusToPlayWalletView(APIView):
    def get(self, request, user_uuid):
        agent = Agent.objects.filter(user_uuid=user_uuid).last()
        if agent is None:
            data = {"message": "agent not found", "status": "error"}
            return Response(data=data, status=status.HTTP_404_NOT_FOUND)

        agent_wallet_instance = AgentWallet.objects.filter(agent=agent).last()
        if agent_wallet_instance is None:
            data = {"message": "agent wallet not found", "status": "error"}
            return Response(data=data, status=status.HTTP_404_NOT_FOUND)

        if agent_wallet_instance.bonus_bal < 1:
            data = {"message": "your bonus balance is low", "status": "error"}
            return Response(data=data, status=status.HTTP_404_NOT_FOUND)

        amount_to_removed = agent_wallet_instance.bonus_bal
        agent_wallet_instance.bonus_bal = 0
        agent_wallet_instance.transaction_from = "BONUS_WITHDRAWAL_TO_PLAY_WALLET"
        agent_wallet_instance.save()

        agent_wallet_instance = AgentWallet.objects.filter(agent=agent).last()
        agent_wallet_instance.game_play_bal += amount_to_removed
        agent_wallet_instance.save()

        return Response(data={"message": "success", "status": "success"}, status=status.HTTP_200_OK)


class BankerLotteryApiView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request, instance=None):
        # Agent---------------------

        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        if BlackListed().can_play_salary_for_life(email=agent_instance.email, phone=agent_instance.phone) is False:
            data = {
                "message": "salary for life is not accessible for you at the moment",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # serializer = MultipleLottoPlaySerializer(
        #     data=request.data,
        #     context={"agent_instance": agent_instance, "lotto_type": "INSTANT_CASHOUT"},
        # )
        #
        # serializer.is_valid(raise_exception=True)
        #
        # phone = serializer.data["phone_number"]
        # if phone == "" or phone is None:
        #     phone = agent_instance.phone
        #
        # serializer.data["phone_number"] = agent_instance.phone
        #
        # phone_no = LotteryModel.format_number_from_back_add_234(phone)
        #
        # # if AgentConstantVariables().get_phone_number_is_required() is True:
        #
        # # if agent_instance.phone == phone_no:
        #
        # #     response = {"message": "You cannot use your own phone number"}
        # #     return Response(response, status=status.HTTP_400_BAD_REQUEST)
        #
        # # if phone is None or phone == "":
        # #     phone = _agent_instance.phone
        #
        # # serializer.data["phone_number"] = phone
        #
        # # get or create user profile
        # user_instance = UserProfile.create_user_profile_if_none_exist(
        #     phone_no=phone_no, channel="POS"
        # )
        # response = PlayMultipleLottoTicket(
        #     request_body=serializer.data,
        #     lotto_type="INSTANT_CASHOUT",
        #     agent_instance=agent_instance,
        #     channel="pos",
        #     player=user_instance,
        # ).payment_game_check()
        #
        # return Response(response, status=status.HTTP_200_OK)


class WinWiseEmployeeView(APIView):
    def post(self, request):
        # print(
        #     f"""
        # \n\n\n\n\n\n
        # WinWiseEmployeeView:
        # \n\n\n\n\n\n
        # """
        # )

        if type(request.data) is dict:
            resp = dict(**request.data)
        else:
            resp = dict(**request.data.dict())
        raw = ast.literal_eval(resp.get("rawRequest"))
        # WinWiseEmployeeTable.create_user_data(raw)

        celery_winwise_agent_record_form.delay(raw)

        return Response({"data": "processing data"}, status=status.HTTP_200_OK)


class PosFundDebitPlayWinningWallet(APIView):
    permission_classes = [SuperUser2Permission]

    serializer_class = PosFundDebitPlayWinningWalletSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone = serializer.validated_data["phone"]
        amount = serializer.validated_data["amount"]
        transaction_from = serializer.validated_data["transaction_from"]
        transaction_id = serializer.validated_data["transaction_id"]
        wallet_type = serializer.validated_data["wallet_type"]
        transaction_type = serializer.validated_data["transaction_type"]
        game_type = serializer.validated_data["game_type"]
        game_play_id = serializer.validated_data["game_play_id"]

        formated_phone = LotteryModel.format_number_from_back_add_234(phone)

        try:
            user_profile = Agent.objects.get(phone=formated_phone)
        except Agent.DoesNotExist:
            user_profile = Agent.objects.filter(phone=formated_phone).first()

        if not user_profile:
            return Response(
                {"message": "user profile does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_profile.phone,
                amount=amount,
                channel="POS/MOBILE",
                reference=transaction_id,
                transaction_type=transaction_type,
            )
        except Exception as e:
            return Response(
                {"message": f"error creating debit credit record {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        wallet_payload = {
            "transaction_from": transaction_from,
            "game_type": game_type,
            "game_play_id": game_play_id,
        }

        if transaction_type == "CREDIT":
            res = UserWallet.fund_wallet(
                user=user_profile,
                amount=amount,
                channel="POS",
                transaction_id=debit_credit_record.reference,
                user_wallet_type=wallet_type,
                **wallet_payload,
            )

            # print(
            #     f"""
            # res: {res}
            # \n\n\n
            # """
            # )

            user_wallet = AgentWallet.objects.filter(agent=user_profile).first()

            log_save_operation(request.user, user_wallet)

            return Response({"data": res}, status=status.HTTP_200_OK)

        elif transaction_type == "DEBIT":
            res = UserWallet.deduct_wallet(
                user=user_profile,
                amount=amount,
                channel="POS",
                transaction_id=debit_credit_record.reference,
                user_wallet_type=wallet_type,
                **wallet_payload,
            )

            user_wallet = AgentWallet.objects.filter(agent=user_profile).first()

            log_save_operation(request.user, user_wallet)

            return Response(data={"data": res}, status=status.HTTP_200_OK)

        else:
            return Response(
                {"message": "transaction type not supported"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class UpdateAgentRemittanceAmountPaid(APIView):
    permission_classes = [SuperUser2Permission]

    serializer_class = UpdateAgentRemittanceAmountPaidSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        amount = serializer.validated_data.get("amount")
        db_id = serializer.validated_data.get("db_id")

        try:
            remittance_record = LottoAgentRemittanceTable.objects.get(id=db_id)
        except LottoAgentRemittanceTable.DoesNotExist:
            return Response(
                {"message": "remittance record does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if remittance_record.amount_paid + amount <= remittance_record.amount:
            _amount_paid = remittance_record.amount_paid + amount

            if remittance_record.amount_paid + amount == remittance_record.amount:
                remittance_record.amount_paid = _amount_paid
                remittance_record.remitted = True
                remittance_record.due = False
                remittance_record.save()

                LottoAgentRemittanceTable.objects.filter(id=remittance_record.id).update(amount_paid=_amount_paid, remitted=True, due=False)
                celery_trigger_agent_reward.delay(remittance_record.agent.id)

            else:
                remittance_record.amount_paid = _amount_paid
                remittance_record.save()

                LottoAgentRemittanceTable.objects.filter(id=remittance_record.id).update(amount_paid=_amount_paid)

        else:
            amount_diff = (remittance_record.amount_paid + amount) - remittance_record.amount

            _amount_paid = remittance_record.amount_paid + amount

            remittance_record.amount_paid = _amount_paid
            remittance_record.excess_amount = amount_diff
            remittance_record.remitted = True
            remittance_record.due = False
            remittance_record.save()

            LottoAgentRemittanceTable.objects.filter(id=remittance_record.id).update(
                amount_paid=_amount_paid,
                excess_amount=amount_diff,
                remitted=True,
                due=False,
            )
            celery_trigger_agent_reward.delay(remittance_record.agent.id)

        return Response({"message": "success"}, status=status.HTTP_200_OK)


class GetAgentDetailsOnAgencyBanking(APIView):
    serializer_class = PhoneNumberSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone = serializer.validated_data.get("phone_number")

        agent = Agent.objects.filter(phone=phone).first()

        if agent is None:
            return Response({"message": "agent not found"}, status=status.HTTP_404_NOT_FOUND)

        response = get_agent_sales_rep_super_agent_and_supervisor(user_id=agent.user_id)

        return Response({"data": response}, status=status.HTTP_200_OK)


class LeaderBoardView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        filter_type = request.GET.get("filter_type")
        type_of_player = request.GET.get("type_of_player")

        agent_instance = Agent.objects.filter(phone=request.user.phone).last()

        if not agent_instance:
            agent_instance = Agent.objects.filter(email=request.user.email).last()
            if not agent_instance:
                data = {
                    "message": "Agent does not exist",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        _filter_type = ["today", "this week", "this month", "custom date"]
        _type_of_players = ["retail_player", "app_player"]

        if filter_type not in _filter_type:
            return Response(
                {"message": "filter type not supported"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if type_of_player not in _type_of_players:
            return Response(
                {"message": "type of player not supported"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if type_of_player == "retail_player":
            is_retail_player = True
        else:
            is_retail_player = False

        today = date.today()

        if filter_type == "today":
            start_date = today
            end_date = today

        elif filter_type == "this week":
            start_date = today - timedelta(days=today.weekday())
            end_date = start_date + timedelta(days=6)

        elif filter_type == "this month":
            start_date = today.replace(day=1)
            next_month = today.replace(day=28) + timedelta(days=4)
            end_date = next_month - timedelta(days=next_month.day)

        elif filter_type == "custom date":
            start_date = request.GET.get("start_date")
            end_date = request.GET.get("end_date")

            if start_date is None or end_date is None:
                return Response(
                    {"message": "start date or end date not provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
                end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
            except Exception:
                return Response(
                    {"message": "start date or end date not properly formatted"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        else:
            start_date = None
            end_date = None

        if is_retail_player is True:
            queryset = AgentWalletTransaction.objects.filter(
                date_created__date__range=[start_date, end_date],
                agent_wallet__agent__terminal_id__isnull=False,
                transaction_from="GAME_PLAY",
            )
        else:
            queryset = AgentWalletTransaction.objects.filter(
                date_created__date__range=[start_date, end_date],
                agent_wallet__agent__terminal_id__isnull=True,
                transaction_from="GAME_PLAY",
            )

        # if is_retail_player is True:
        #     queryset = AgentWalletTransaction.objects.filter(
        #         agent_wallet__agent__terminal_id__isnull=False,
        #         transaction_from="GAME_PLAY",
        #     )

        # else:
        #     queryset = AgentWalletTransaction.objects.filter(
        #         agent_wallet__agent__terminal_id__isnull=True,
        #         transaction_from="GAME_PLAY",
        #     )

        # unique_queryset = queryset.values("agent_wallet").annotate(
        #     transaction_count=Count("id")
        # )

        unique_queryset = queryset.values("agent_wallet").annotate(transaction_sum=Sum("amount"))

        unique_queryset = unique_queryset.order_by("-transaction_sum")

        print("\n\n\n\n", "unique_queryset", unique_queryset, "\n\n\n\n\n")

        top_agent_transactions = unique_queryset[:20]

        top_agent_transactions_wallet_ids = unique_queryset.values_list("agent_wallet", flat=True)

        agent_wallets = AgentWallet.objects.filter(id__in=top_agent_transactions_wallet_ids)

        # transactions = queryset.filter(agent_wallet__id__in=top_agent_transactions)

        result = []

        for agent_wallet in agent_wallets:
            for index, agent_trans in enumerate(top_agent_transactions):
                if agent_wallet.id == agent_trans.get("agent_wallet"):
                    position = index + 1
                    data = {
                        "ranking": position,
                        "players_email": agent_wallet.agent.email,
                        "amount": "N" + currency_formatter(agent_trans.get("transaction_sum")),
                    }

                    result.append(data)

        # sort by ranking
        result = sorted(result, key=lambda k: k["ranking"])

        requesting_aegnt_wallet = AgentWallet.objects.get(agent__id=agent_instance.id)

        if (is_retail_player is True) and (agent_instance.terminal_id is None or agent_instance.terminal_id == ""):
            user_data = []

        elif (is_retail_player is True) and (agent_instance.terminal_id is not None or agent_instance.terminal_id != ""):
            user_data = []
            found = False
            _index = 0
            requesting_user_total_sum = 0

            for indx, i in enumerate(unique_queryset):
                if i.get("agent_wallet") == requesting_aegnt_wallet.id:
                    found = True
                    _index = indx
                    requesting_user_total_sum = i.get("transaction_sum")
                    break

            if found is True:
                user_data.append(
                    {
                        "ranking": _index + 1,
                        "players_email": agent_instance.email,
                        "amount": "N" + currency_formatter(requesting_user_total_sum),
                    }
                )

            else:
                user_data.append(
                    {
                        "ranking": len(unique_queryset) + random.randint(1, 100),
                        "players_email": agent_instance.email,
                        "amount": "N" + currency_formatter(0),
                    }
                )

        elif (is_retail_player is False) and (agent_instance.terminal_id is not None or agent_instance.terminal_id != ""):
            user_data = []

        elif (is_retail_player is False) and (agent_instance.terminal_id is None or agent_instance.terminal_id == ""):
            user_data = []
            found = False
            _index = 0
            requesting_user_total_sum = 0

            for indx, i in enumerate(unique_queryset):
                if i.get("agent_wallet") == requesting_aegnt_wallet.id:
                    found = True
                    _index = indx
                    requesting_user_total_sum = i.get("transaction_sum")
                    break

            if found is True:
                user_data.append(
                    {
                        "ranking": _index + 1,
                        "players_email": agent_instance.email,
                        "amount": "N" + currency_formatter(requesting_user_total_sum),
                    }
                )

            else:
                user_data.append(
                    {
                        "ranking": len(unique_queryset) + random.randint(1, 100),
                        "players_email": agent_instance.email,
                        "amount": "N" + currency_formatter(0),
                    }
                )

        else:
            user_data = []

        data = {
            "data": result,
            "user_data": user_data,
        }

        return Response(data, status=status.HTTP_200_OK)


class ChargeAgentOnAgencyBankingForRemittanceView(APIView):
    permission_classes = [SuperUser2Permission]

    serializer_class = ChargeAgentOnAgencyBankingForRemittanceSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone = serializer.validated_data.get("phone_number")
        amount = serializer.validated_data.get("amount")

        agent = Agent.objects.filter(phone=phone).first()
        if agent is None:
            return Response({"message": "agent not found"}, status=status.HTTP_404_NOT_FOUND)

        payload = {
            "service_name": "LOTTO_PLAY",
            "user_id": agent.user_id,
            "narration": "REMITTANCE_DEDUCTION",
            "total_amount": amount,
            "service_comm": amount,
            "agent_comm": 0,
        }

        record_instance = FailedRemittanceAgencyWalletCharge.objects.create(
            agent=agent,
            outstanding_remittance=amount,
            payload=payload,
            trans_ref=FailedRemittanceAgencyWalletCharge().generate_transaction_ref(),
        )

        cp_payload = payload
        cp_payload["transaction_pin"] = 0000
        cp_payload["unique_reference"] = record_instance.trans_ref

        pos_agent_helper = PosAgentHelper(
            agent_instance=agent,
            amount=0,
            pin=0,
        )

        deduction_response = pos_agent_helper.charge_defaulted_remittance(**cp_payload)

        record_instance.response_payload = deduction_response
        record_instance.save()

        return Response({"data": deduction_response}, status=status.HTTP_200_OK)


class PosQuikaLottoPlayApiView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    serializer_class = QuikaLottoPlayApiViewSerializer

    def post(self, request):
        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        channel = request.GET.get("channel", "pos")

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        if BlackListed().can_play_instant_cashout(email=agent_instance.email, phone=agent_instance.phone) is False:
            data = {
                "message": "Instant cashout is not accessible for you at the moment",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.serializer_class(data=request.data, context={"agent_instance": agent_instance})
        serializer.is_valid(raise_exception=True)

        phone = serializer.data["phone_number"]
        ticket_numbers = serializer.data["ticket_numbers"]
        # pin = serializer.data["pin"]

        # print("ticket_numbers", ticket_numbers)
        number_of_number_picked = len(ticket_numbers)
        # print("number_of_number_picked", number_of_number_picked)

        ticket = "0000"
        if number_of_number_picked == 4:
            ticket = ",".join(str(i) for i in ticket_numbers)
        elif number_of_number_picked == 3:
            ticket_numbers = ticket_numbers + [0]
            ticket = ",".join(str(i) for i in ticket_numbers)
        elif number_of_number_picked == 2:
            ticket_numbers = ticket_numbers + [0, 0]
            ticket = ",".join(str(i) for i in ticket_numbers)
        elif number_of_number_picked == 1:
            ticket_numbers = ticket_numbers + [0, 0, 0]
            ticket = ",".join(str(i) for i in ticket_numbers)

        # remove last comma
        # ticket = ticket[:-1]

        ticket = [int(i) for i in ticket.split(",")]

        quika_price_model = PosNewQuikaPriceModel()

        quika_price_dict = quika_price_model.ticket_price(ticket_line=number_of_number_picked)

        phone = serializer.data["phone_number"]

        if phone == "" or phone is None:
            phone = agent_instance.phone

        phone_no = LotteryModel.format_number_from_back_add_234(phone)

        user_instance = UserProfile.create_user_profile_if_none_exist(phone_no=phone_no, channel="POS")

        """
        CURRENT BRANCH
        """

        batch = LotteryBatch.objects.filter(is_active=True, lottery_type="INSTANT_CASHOUT")
        if batch.count() > 0:
            current_batch = batch.last()

        else:
            current_batch = LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT")

        game_play_id = generate_game_play_id()
        ticket_pin = generate_pin()

        identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}{ticket_pin}"

        LottoTicket.objects.create(
            user_profile=user_instance,
            agent_profile=agent_instance,
            batch=current_batch,
            phone=phone_no,
            stake_amount=quika_price_dict.get("amount_to_register"),
            expected_amount=quika_price_dict.get("amount_to_register"),
            illusion=0,
            potential_winning=quika_price_dict.get("potential_winning"),
            paid=False,
            number_of_ticket=1,
            channel="POS_AGENT" if channel == "pos" else "MOBILE",
            game_play_id=game_play_id,
            lottery_type="QUIKA",
            ticket=serialize_ticket(ticket),
            pin=ticket_pin,
            identity_id=identity_id,
            is_new_quika_game=True,
        )

        UssdLotteryPayment.objects.create(
            user=user_instance,
            amount=quika_price_dict.get("amount_to_register"),
            game_play_id=game_play_id,
            channel="POS_AGENT" if channel == "pos" else "MOBILE",
            lottery_type="QUIKA",
            illusion_amount=0,
            has_illusion=False,
        )

        result_status, result = pay_quika_lotto_ticket_and_check_game_result(
            game_play_id=game_play_id,
            agent_instance=agent_instance,
            pin="000",
            game_amount=quika_price_dict.get("ticket_price"),
            lotto_type="QUIKA",
        )

        if result_status is True:
            response_body = {
                "status": "success",
                "lotto_type": "QUIKA",
                "agent_id": agent_instance.user_uuid,
                "message": "Accepted",
            }
            response_body.update(result)
            print(response_body)

            return Response(response_body, status=status.HTTP_200_OK)

        else:
            response_body = {
                "status": "error",
                "paid": False,
                "message": result.get("message"),
            }

            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)


class GetAgentDownlinesApiView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            super_agent_instance = LottoSuperAgents.objects.get(phone=request.user.phone)
        except LottoSuperAgents.DoesNotExist:
            response_body = {
                "status": "error",
                "message": "sorry, you're not a super agent",
            }
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        downlines = Agent.objects.filter(super_agent=super_agent_instance)

        serialized_data = AgentModelSerializer(downlines, many=True).data

        return Response(data=serialized_data, status=status.HTTP_200_OK)


class AgentDownLinesAccountRestrictionApiView(APIView, ManageMobileRequest):

    serializer_class = AgentDownLinesAccountRestrictionSerializer

    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        type_of_restriction = serializer.validated_data.get("type_of_restriction")
        agent_id = serializer.validated_data.get("agent_id")

        try:
            agent_downline_instance = Agent.objects.get(id=agent_id, super_agent__phone=request.user.phone)
        except Agent.DoesNotExist:
            response_body = {
                "status": "error",
                "message": "agent does not exist",
            }
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        if type_of_restriction == "TERMINAL_RESTRICTION":
            res_status, res_data = self.suspend_or_unsuspend_terminal_on_agency_banking(
                agent_instance=agent_downline_instance, suspend_or_unsuspend_reason="suspended by super agent"
            )
            if res_status is False:
                response_body = {"status": "error", "message": res_data}
                return Response(response_body, status=status.HTTP_400_BAD_REQUEST)
            else:
                response_body = {"status": "success", "message": res_data}
                return Response(response_body, status=status.HTTP_200_OK)
        else:
            res_status, res_data = self.suspend_or_unsuspend_agent_from_withdrawing(agent_instance=agent_downline_instance)
            if res_status is False:
                response_body = {"status": "error", "message": res_data}
                return Response(response_body, status=status.HTTP_400_BAD_REQUEST)
            else:
                response_body = {"status": "success", "message": res_data}
                return Response(response_body, status=status.HTTP_200_OK)


class AgentDownLinesAccountUnRestrictionApiView(APIView, ManageMobileRequest):

    serializer_class = AgentDownLinesAccountRestrictionSerializer

    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        type_of_restriction = serializer.validated_data.get("type_of_restriction")
        agent_id = serializer.validated_data.get("agent_id")

        print(
            f"""
            agent_id: {agent_id}
            agent phone: {request.user.phone}
            """
        )

        try:
            agent_downline_instance = Agent.objects.get(id=agent_id, super_agent__phone=request.user.phone)
        except Agent.DoesNotExist:
            response_body = {
                "status": "error",
                "message": "agent does not exist",
            }
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        if type_of_restriction == "TERMINAL_RESTRICTION":
            print("TERMINAL_RESTRICTION")
            res_status, res_data = self.suspend_or_unsuspend_terminal_on_agency_banking(
                agent_instance=agent_downline_instance, suspend_or_unsuspend_reason="unsuspension request from super agent", suspend="false"
            )
            print("res_status", res_status)
            if res_status is False:
                response_body = {"status": "error", "message": res_data}
                return Response(response_body, status=status.HTTP_400_BAD_REQUEST)
            else:
                response_body = {"status": "success", "message": res_data}
                return Response(response_body, status=status.HTTP_200_OK)

        else:
            res_status, res_data = self.suspend_or_unsuspend_agent_from_withdrawing(agent_instance=agent_downline_instance, can_withdraw=True)
            if res_status is False:
                response_body = {"status": "error", "message": res_data}
                return Response(response_body, status=status.HTTP_400_BAD_REQUEST)
            else:
                response_body = {"status": "success", "message": res_data}
                return Response(response_body, status=status.HTTP_200_OK)


class AgentDownLineCommissionGivingApiView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    serializer_class = AgentDownLineCommissionGivingSerializer

    def post(self, request):
        try:
            super_agent_instance = LottoSuperAgents.objects.get(phone=request.user.phone)
        except LottoSuperAgents.DoesNotExist:
            response_body = {
                "status": "error",
                "message": "sorry, you're not a super agent",
            }
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        agent_id = serializer.validated_data.get("agent_id")
        commission_percentage = serializer.validated_data.get("commission_percentage")

        try:
            downline = Agent.objects.get(super_agent=super_agent_instance, id=agent_id)
        except Agent.DoesNotExist:
            return Response({"message": "downline does not exist"}, status=status.HTTP_404_NOT_FOUND)

        downline.downlines_sharing_commission_percentage = commission_percentage
        downline.save()

        response_body = {
            "status": "success",
            "message": "commission given to downline",
        }

        return Response(response_body, status=status.HTTP_200_OK)


class FetchAgentEndOfDaySalesApiView(APIView, ManageMobileRequest):
    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            agent_instance = Agent.objects.get(phone=request.user.phone)
        except Agent.DoesNotExist:
            response_body = {
                "status": "error",
                "message": "sorry, you're not an agent",
            }
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        return Response(self.generate_agent_end_of_day_report(agent_instance), status=status.HTTP_200_OK)


class GetStoreWinningDataApiView(APIView, ManageMobileRequest):
    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):

        try:
            agent_instance = Agent.objects.get(phone=request.user.phone)
        except Agent.DoesNotExist:
            response_body = {
                "status": "error",
                "message": "sorry, you're not an agent",
            }
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        return Response(self.agent_store_winnings(agent_instance), status=status.HTTP_200_OK)


class StoreWinningWithdrawalApiView(APIView, ManageMobileRequest):

    serializer_class = LibertyPayWinningsPayoutSerializer

    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer_class = self.serializer_class(data=request.data)
        serializer_class.is_valid(raise_exception=True)

        serialized_data_dict = serializer_class.validated_data

        try:
            agent_instance = Agent.objects.get(phone=request.user.phone)
        except Agent.DoesNotExist:
            response_body = {
                "status": "error",
                "message": "sorry, you're not an agent",
            }
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        _formated_phone = LotteryModel.format_number_from_back_add_234(serialized_data_dict.get("phone"))

        serialized_data_dict["phone"] = _formated_phone

        try:
            res_status, res_data = self.agent_new_withdrawal(agent_instance, **serialized_data_dict)
        except Exception as e:  # noqa
            # check if agent phone number have been exempted from limit list
            if agent_instance.phone not in ConstantVariable.get_constant_variable().get("users_exempted_from_limit"):
                const = ConstantVariable.objects.last()
                const.users_exempted_from_limit = const.users_exempted_from_limit + "," + agent_instance.phone
                const.save()
                const.refresh_from_db()

            
            LottoAgentFailedTransactionLog.objects.create(
                agent_name = agent_instance.name,
                agent_email = agent_instance.email,
                agent_phone_number = agent_instance.phone,
                payload = serialized_data_dict,
                response_payload = str(e)
            )


            response_body = {"status": "error", "message": "error processing withdrawal"}
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        if res_status is False:
            response_body = {"status": "error", "message": res_data}
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        return Response(res_data, status=status.HTTP_200_OK)


class SuperAgentDownlineNotification(APIView, ManageMobileRequest):
    permission_classes = []
    permission_classes = []

    def post(self, request):

        self.agent_downline_notification(request.data)

        response_body = {
            "status": "success",
            "message": "downlines notified",
        }

        return Response(response_body, status=status.HTTP_200_OK)


class ManuallyCreateWinningForATicketView(APIView, ManageMobileRequest):
    permission_classes = [SuperUser2Permission]

    serializer_class = ManuallyCreateWinningForATicketSerializer

    @method_decorator(staff_member_required)
    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        ticket_id = serializer.validated_data.get("ticket_id")
        amount_won = serializer.validated_data.get("amount_won")
        model = serializer.validated_data.get("db_model")
        game_play_id = serializer.validated_data.get("game_play_id")

        res_status, res = self.manually_create_winning_for_a_ticket(
            ticket_id=ticket_id, amount_won=amount_won, model=model, game_play_id=game_play_id
        )

        if res_status is False:
            return Response(data={"message": res}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(data={"message": res}, status=status.HTTP_200_OK)


class AgentSalesMetricsView(APIView, ManageMobileRequest):
    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            agent_instance = Agent.objects.get(phone=request.user.phone)
        except Agent.DoesNotExist:
            response_body = {
                "status": "error",
                "message": "sorry, you're not an agent",
            }
            return Response(response_body, status=status.HTTP_400_BAD_REQUEST)

        data = self.generate_agent_sales_metrics(agent_instance)
        # print(data)
        return Response(data, status=status.HTTP_200_OK)


class AgentTerminalIdUnAssignmentRequestApiView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    

    def post(self, request):
        data = request.data

        agent_name = data.get("agent_name")
        agent_email = data.get("agent_email")
        agent_phone_number = data.get("agent_phone_number")

        TerminalIdUnAssignmentRequestLogs.objects.create(
            agent_name=agent_name,
            agent_email=agent_email,
            agent_phone_number=agent_phone_number,
            payload=data,
        )
        
        try:
            agent_instance = Agent.objects.get(phone=agent_phone_number)
        except Agent.DoesNotExist:
            agent_instance = None
        
        if agent_instance is not None:
            agent_instance.terminal_id = None
            agent_instance.agent_type = "PERSONAL"
            agent_instance.terminal_retrieved = True
            agent_instance.has_pre_funding = False
            agent_instance.can_withdraw = False
            agent_instance.suspended_on_agency_banking = True
            agent_instance.suspension_reason = "terminal id unassignment request"
            agent_instance.save()

            # remove game play play balance
            agent_wallet_instance = AgentWallet.objects.filter(agent=agent_instance).last()
            if agent_wallet_instance is not None:
                agent_wallet_instance.game_play_bal = 0
                agent_wallet_instance.winnings_bal = 0
                agent_wallet_instance.save()

            
            # remove all pending winnings to claim
            PosLotteryWinners.objects.filter(
                agent=agent_instance,
            ).update(is_win_claimed=True, withdrawl_initiated = True, payout_successful = True,  payout_verified = True)




        return Response(data = {"message": "request received"}, status=status.HTTP_200_OK)



class FetchGameDrawTimeApiView(APIView):

    def get(self, request):
        ghana_games_draw_time = [
            {
                "day": "Monday",
                "draw_name": "Monday Special",
                "draw_time": "8:0pm",
            },
            {
                "day": "Tuesday",
                "draw_name": "Lucky Tuesday",
                "draw_time": "8:0pm",
            },
            {
                "day": "Wednesday",
                "draw_name": "Midweek",
                "draw_time": "8:0pm",
            },
            {
                "day": "Thursday",
                "draw_name": "Fortune Thursday",
                "draw_time": "8:0pm",
            },
            {
                "day": "Friday",
                "draw_name": "Friday Bonanza",
                "draw_time": "8:0pm",
            },
            {
                "day": "Saturday",
                "draw_name": "National Weekly",
                "draw_time": "8:0pm",
            },
            {
                "day": "Sunday",
                "draw_name": "Sunday Aseda",
                "draw_time": "8:0pm",
            }
        ]

        kenya_draw_times = [
            {
                "draw_name": "Barnabas",
                "draw_time": "09:00 AM",
            },
            {
                "draw_name": "Tokyo",
                "draw_time": "12:00 PM"
            },
            {
                "draw_name": "Ultimate",
                "draw_time": "03:00 PM"
            },
            {
                "draw_name": "Angelina Special",
                "draw_time": "06:00 PM"
            },
            {
                "draw_name": "Supreme Max",
                "draw_time": "09:00 PM"
            }
        ]

        salary_for_life_draw_time = {
            "draw_time_interval": "2 hours",
            "first_draw": "9:00 AM",
            "last_draw": "9:00 PM",
        }

        k_30_draw_time = {
            "draw_time_interval": "30 minutes",
            "first_draw": "8:15 AM",
            "last_draw": "9:45 PM",
        }

        banker_draw_time = {
            "draw_time_interval": "30 minutes",
            "first_draw": "8:00 AM",
            "last_draw": "9:30 PM",
        }

        wyse_cash_draw_time = {
            "draw_time_interval": "2 hours",
            "first_draw": "10:00 AM",
            "last_draw": "10:00 PM",
        }


        response_data = {
            "ghana_games_draw_time": ghana_games_draw_time,
            "kenya_draw_times": kenya_draw_times,
            "salary_for_life_draw_time": salary_for_life_draw_time,
            "k_30_draw_time": k_30_draw_time,
            "banker_draw_time": banker_draw_time,
            "wyse_cash_draw_time": wyse_cash_draw_time
        }

        return Response(data=response_data, status=status.HTTP_200_OK)
    


@method_decorator(staff_member_required, name="dispatch")
class ManuallyCheckAndCreateLottoWinningsView(APIView):
    permission_classes = [SuperUser2Permission]

    serializer_class = ManuallyCheckAndCreateLottoWinningsSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        winning_number = serializer.validated_data.get("winning_number")
        model_db = serializer.validated_data.get("model_db")
        game_type = serializer.validated_data.get("game_type")
        game_play_id = serializer.validated_data.get("game_play_id")
        agent_phone_number = serializer.validated_data.get("agent_phone_number")


        # check ifagent was charge for this game play
        agent_wallet_transaction = AgentWalletTransaction.objects.filter(
            agent_phone_number = agent_phone_number,
            transaction_from = "GAME_PLAY",
            game_play_id = game_play_id,
        ).last()

        if agent_wallet_transaction is None:
            return Response(data={"message": "agent was not charged for this game play"}, status=status.HTTP_400_BAD_REQUEST)
        
        # get ticket
        tickets = LottoTicket.objects.filter(game_play_id=game_play_id, agent_profile__phone = agent_phone_number)
        ticket_count = len(tickets)
        if len(tickets) < 1:
            return Response(data={"message": "ticket does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        
        last_instance = tickets.last()
        tickets = LottoTicket.objects.filter(game_play_id=game_play_id, agent_profile__phone = agent_phone_number, batch = last_instance.batch)

        winning_numbers = str(winning_number).split(":")


        draw_data = []

        for win_num in winning_numbers:
            winning_number = win_num.split(",")
            winning_number = [int(i) for i in winning_number]

            for ticket in tickets:

                try:
                    LottoWinners.objects.get(lottery = ticket)
                    continue
                except LottoWinners.DoesNotExist:
                    
                    # check matching
                    ticket_numbers = [int(num.strip()) for num in ticket.ticket.split(',')]

                    matches = [num for num in ticket_numbers if num in winning_number]
                    match_count = len(matches)

                    draw_data.append(
                        {
                            "ticket": ticket.ticket,
                            "winning_numbers": winning_number,
                            "matches": matches,
                            "match_count": match_count,
                            "game_play_id": ticket.game_play_id,
                        }
                    )

                    if match_count > 1:
                        amount_won = LottoTicket.salary_for_life_win_per_line(
                            match = match_count,
                            number_of_line = ticket_count
                        )

                        win_type = "PERM_4"

                        if match_count == 4:
                            win_type = "PERM_4"
                        elif match_count == 3:
                            win_type = "PERM_3"
                        elif match_count == 2:
                            win_type = "PERM_2"

                        
                        _draw_batch_id = generate_game_play_id()


                        LottoWinners.create_lotto_winner_obj(
                            batch=ticket.batch,
                            phone_number=ticket.user_profile.phone_number,
                            ticket=[int(i) for i in str(ticket.ticket).split(",")],
                            lottery=ticket,
                            win_type="ORDINARY_WINNER",
                            match_type=win_type,
                            lotto_type="SALARY_FOR_LIFE",
                            game_play_id=ticket.game_play_id,
                            stake_amount=ticket.stake_amount,
                            earning=amount_won,
                            channel_played_from=ticket.channel,
                            run_batch_id=_draw_batch_id,
                            played_via_telco_channel=ticket.played_via_telco_channel,
                        )




        return Response(data={"message": "Please check if winning was created", "data": draw_data}, status=status.HTTP_200_OK)









