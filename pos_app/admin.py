from django.conf import settings
from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from rest_framework.authtoken.models import Token

from pos_app.forms import AgentForm
from pos_app.models import WinWiseEmployeeTable

from .models import (
    AgencyBankingLoginAttempt,
    AgencyBankingLogs,
    AgencyBankingToken,
    Agent,
    AgentBonus,
    AgentConstantVariables,
    AgentFundingTable,
    AgentNoPreFunding,
    AgentOnBoardingPayload,
    AgentPayoutBeneficiary,
    AgentSuspensionRequestToAgencyBanking,
    AgentVFDAccountDetail,
    AgentWallet,
    AgentWalletTransaction,
    BoughtLotteryTickets,
    ChargeAgentTransaction,
    CreateAgentLogs,
    DailyInactiveAgents,
    FailedRemittanceAgencyWalletCharge,
    GamesDailyActivities,
    GamesTable,
    GeneralRetailLottoGames,
    LottoAgentDailySalesActivity,
    LottoAgentFailedTransactionLog,
    LottoAgentGuarantorDetail,
    LottoAgentRemittanceTable,
    LottoAgentSalesActivity,
    LottoSuperAgentCommissionAllocation,
    LottoSuperAgents,
    LottoSuperAgentWallet,
    LottoSuperAgentWalletTransaction,
    PosLotteryWinners,
    PosWithdrawalRequest,
    PromotionGames,
    RawFundingData,
    RemunerationChargeRemittance,
    RetailTicketRequestLogs,
    SalesRepAgencyDumpData,
    SalesReps,
    SalesRepWallet,
    SalesRepWalletTransaction,
    SuperAgentAgencyDumpData,
    Supervisor,
    SupervisorAgencyDumpData,
    SupervisorLocation,
    TerminalIdUnAssignmentRequestLogs,
    WinwiseEmployeeSalary,
)


# Register your models here.
class PosLotteryWinnersResource(resources.ModelResource):
    class Meta:
        model = PosLotteryWinners


class AgentPayoutBeneficiaryResource(resources.ModelResource):
    class Meta:
        model = AgentPayoutBeneficiary


class AgentModelResource(resources.ModelResource):
    class Meta:
        model = Agent


class WinWiseEmployeeTableResource(resources.ModelResource):
    class Meta:
        model = WinWiseEmployeeTable


class AgentWalletResource(resources.ModelResource):
    class Meta:
        model = AgentWallet


class ChargeAgentTransactionResource(resources.ModelResource):
    class Meta:
        model = ChargeAgentTransaction


class BoughtLotteryTicketsResource(resources.ModelResource):
    class Meta:
        model = BoughtLotteryTickets


class AgentBonusResource(resources.ModelResource):
    class Meta:
        model = AgentBonus


class AgentConstantVariablesResource(resources.ModelResource):
    class Meta:
        model = AgentConstantVariables


class GamesTableResource(resources.ModelResource):
    class Meta:
        model = GamesTable


class LottoAgentRemittanceTableResource(resources.ModelResource):
    class Meta:
        model = LottoAgentRemittanceTable


class LottoAgentGuarantorDetailResource(resources.ModelResource):
    class Meta:
        model = LottoAgentGuarantorDetail


class AgentVFDAccountDetailResource(resources.ModelResource):
    class Meta:
        model = AgentVFDAccountDetail


class CreateAgentLogsResource(resources.ModelResource):
    class Meta:
        model = CreateAgentLogs


class AgencyBankingLogsResource(resources.ModelResource):
    class Meta:
        model = AgencyBankingLogs


class PosWithdrawalRequestResource(resources.ModelResource):
    class Meta:
        model = PosWithdrawalRequest


class AgentFundingTableResource(resources.ModelResource):
    class Meta:
        model = AgentFundingTable


class RawFundingDataResource(resources.ModelResource):
    class Meta:
        model = RawFundingData


class LottoSuperAgentWalletResource(resources.ModelResource):
    class Meta:
        model = LottoSuperAgentWallet


class LottoSuperAgentsResource(resources.ModelResource):
    class Meta:
        model = LottoSuperAgents


class LottoSuperAgentWalletTransactionResource(resources.ModelResource):
    class Meta:
        model = LottoSuperAgentWalletTransaction


class RetailTicketRequestLogsResource(resources.ModelResource):
    class Meta:
        model = RetailTicketRequestLogs


class AgentOnBoardingPayloadResource(resources.ModelResource):
    class Meta:
        model = AgentOnBoardingPayload


class SalesRepsResource(resources.ModelResource):
    class Meta:
        model = SalesReps


class SalesRepWalletResource(resources.ModelResource):
    class Meta:
        model = SalesRepWallet


class SalesRepWalletTransactionResource(resources.ModelResource):
    class Meta:
        model = SalesRepWalletTransaction


class FailedRemittanceAgencyWalletChargeResource(resources.ModelResource):
    class Meta:
        model = FailedRemittanceAgencyWalletCharge


class AgentSuspensionRequestToAgencyBankingResource(resources.ModelResource):
    class Meta:
        model = AgentSuspensionRequestToAgencyBanking


class SupervisorResource(resources.ModelResource):
    class Meta:
        model = Supervisor


class SupervisorLocationResource(resources.ModelResource):
    class Meta:
        model = SupervisorLocation


class SalesRepAgencyDumpDataResource(resources.ModelResource):
    class Meta:
        model = SalesRepAgencyDumpData


class SupervisorAgencyDumpDataResource(resources.ModelResource):
    class Meta:
        model = SupervisorAgencyDumpData


class SuperAgentAgencyDumpDataResource(resources.ModelResource):
    class Meta:
        model = SuperAgentAgencyDumpData


class AgentCashBackResource(resources.ModelResource):
    class Meta:
        model = RemunerationChargeRemittance


class WinwiseEmployeeSalaryResource(resources.ModelResource):
    class Meta:
        model = WinwiseEmployeeSalary


class AgentNoPreFundingResource(resources.ModelResource):
    class Meta:
        model = AgentNoPreFunding


class LottoSuperAgentCommissionAllocationResource(resources.ModelResource):
    class Meta:
        model = LottoSuperAgentCommissionAllocation


class PromotionGamesResource(resources.ModelResource):
    class Meta:
        model = PromotionGames


class LottoAgentSalesActivityResource(resources.ModelResource):
    class Meta:
        model = LottoAgentSalesActivity


class LottoAgentDailySalesActivityResource(resources.ModelResource):
    class Meta:
        model = LottoAgentDailySalesActivity



class DailyInactiveAgentsResource(resources.ModelResource):
    class Meta:
        model = DailyInactiveAgents


class GamesDailyActivitiesResource(resources.ModelResource):
    class Meta:
        model = GamesDailyActivities



class AgentWalletTransactionResource(resources.ModelResource):
    class Meta:
        model = AgentWalletTransaction


class LottoAgentFailedTransactionLogResource(resources.ModelResource):
    class Meta:
        model = LottoAgentFailedTransactionLog


class TerminalIdUnAssignmentRequestLogsResource(resources.ModelResource):
    class Meta:
        model = TerminalIdUnAssignmentRequestLogs

        
class GeneralRetailLottoGamesResource(resources.ModelResource):
    class Meta:
        model = GeneralRetailLottoGames


class AgencyBankingTokenResource(resources.ModelResource):
    class Meta:
        model = AgencyBankingToken


class AgencyBankingLoginAttemptResource(resources.ModelResource):
    class Meta:
        model = AgencyBankingLoginAttempt


class AgentModelResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentModelResource
    search_fields = [
        "phone",
        "email",
        "user_uuid",
        "full_name",
        "first_name",
        "last_name",
    ]
    form = AgentForm
    list_filter = (
        "has_web_virtual_account",
        "agent_type",
        "performance_status",
        "is_super_agent",
        "terminal_retrieved",
        "suspended_on_agency_banking",
        "is_winwise_staff_agent",
        "has_pre_funding",
    )
    date_hierarchy = "created_date"

    raw_id_fields = [
        "super_agent",
    ]
    autocomplete_fields = [
        "super_agent",
    ]

    def get_list_display(self, request):
        data_fields = [field.name for field in self.model._meta.concrete_fields]
        data_fields.remove("full_name")
        data_fields.remove("icash_flavour_dict")
        data_fields.remove("suspension_reason")
        data_fields.remove("un_suspension_reason")
        data_fields.remove("old_quika_icash_count_to_giver")
        return data_fields


class AgentWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentWalletResource
    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "agent__user_uuid",
    ]
    # list_filter = (
    #     ('has_web_virtual_account',)
    # )
    date_hierarchy = "created_at"
    raw_id_fields = ["agent", "woven_account", "vfd_account"]
    autocomplete_fields = ["agent", "vfd_account", "woven_account"]

    if settings.DEBUG is False:
        readonly_fields = (
            "commission_bal",
            "commission_rewarded",
            "bonus_bal",
            "used_bonus_bal",
            "winnings_bal",
            "withdrawable_available_bal",
            "game_play_bal",
        )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]





# class AgentWalletTransactionModelFilter(FilterSet):
#     category = ChoiceFilter(
#         choices=[("A", "Option A"), ("B", "Option B"), ("C", "Option C")],
#         widget=forms.CheckboxSelectMultiple,
#     )


class AgentWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentWalletTransactionResource
    search_fields = [
        "agent_wallet__agent__phone",
        "agent_wallet__agent__email",
        "agent_wallet__agent__full_name",
        "agent_wallet__agent__first_name",
        "agent_wallet__agent__last_name",
        "transaction_reference",
    ]
    list_filter = ("date_created", "type_of_user", "type_of_agent", "transaction_from", "transaction_type",  "game_type")
    date_hierarchy = "date_created"

    raw_id_fields = ["agent_wallet"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PosLotteryWinnersResourceAdmin(ImportExportModelAdmin):
    resource_class = PosLotteryWinnersResource
    autocomplete_fields = ["player", "agent", "claimant"]   
    search_fields = [
        "game_id",
        "pin",
        "player__phone_number",
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "payout_ref",
    ]
    list_filter = [
        "date_created",
        "lottery_type",
        "win_flavour",
        "payout_successful",
        "payout_verified",
        "is_win_claimed",
        "withdrawl_initiated",
    ]

    raw_id_fields = ["agent", "player", "claimant"]
    date_hierarchy = "date_created"

    # autocomplete_fields = ["agent", "player"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        
        # Check if user is a superuser
        if not request.user.is_superuser:
            import datetime
            today = datetime.date.today()
            first_day_of_month = today.replace(day=1)
            
            queryset = queryset.filter(date_created__gte=first_day_of_month)
            
        return queryset


class AgentPayoutBeneficiaryResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentPayoutBeneficiaryResource
    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "beneficiary_name",
        "beneficiary_account_number",
    ]
    # list_filter = (
    #     ('transaction_from',)
    # )
    date_hierarchy = "created_at"
    raw_id_fields = ["agent"]
    autocomplete_fields = [
        "agent",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ChargeAgentTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = ChargeAgentTransactionResource
    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "transaction_ref",
    ]
    list_filter = ("is_verified", "payment_initiated", "charged")
    date_hierarchy = "created_at"
    raw_id_fields = ["agent"]
    autocomplete_fields = [
        "agent",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentBonusResourceAdmin(ImportExportModelAdmin):
    resource_class = ChargeAgentTransactionResource
    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
    ]
    list_filter = ("lottery_type",)
    date_hierarchy = "created_at"
    raw_id_fields = ["agent"]
    autocomplete_fields = [
        "agent",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BoughtLotteryTicketsResourceAdmin(ImportExportModelAdmin):
    resource_class = BoughtLotteryTicketsResource
    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "game_id",
        "ussd_code",
    ]
    list_filter = (
        "is_available",
        "game_type",
        "paid",
    )
    date_hierarchy = "created_at"
    raw_id_fields = ["agent"]
    autocomplete_fields = [
        "agent",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentConstantVariablesResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentConstantVariablesResource
    # search_fields = ["agent__phone", "ussd_code"]
    # list_filter = ("is_available", "game_type", "paid")
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class GamesTableResourceAdmin(ImportExportModelAdmin):
    resource_class = GamesTableResource
    # search_fields = ["agent__phone", "ussd_code"]
    # list_filter = ("is_available", "game_type", "paid")
    # date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoAgentRemittanceTableResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoAgentRemittanceTableResource
    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
    ]
    list_filter = ("due", "remitted", "terminal_retrieved")
    date_hierarchy = "created_at"
    raw_id_fields = ["agent"]
    autocomplete_fields = [
        "agent",
    ]

    if settings.DEBUG is False:
        readonly_fields = (
            "amount_paid",
            "amount",
            "excess_amount",
        )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoAgentGuarantorDetailResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoAgentGuarantorDetailResource
    search_fields = [
        "verification_id",
        "agent_email",
        "guarantor_email",
        "unique_code",
    ]
    list_filter = ("verified",)
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentVFDAccountDetailResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentVFDAccountDetailResource
    search_fields = [
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "agent__phone",
        "vnuban",
        "acct_name",
    ]
    date_hierarchy = "date"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CreateAgentLogsResourceAdmin(ImportExportModelAdmin):
    resource_class = CreateAgentLogsResource
    search_fields = [
        "phone",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgencyBankingLogsAdmin(ImportExportModelAdmin):
    resource_class = AgencyBankingLogsResource
    search_fields = [
        "agent__name",
        "token",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PosWithdrawalRequestResourceAdmin(ImportExportModelAdmin):
    resource_class = PosWithdrawalRequestResource
    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentFundingTableResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentFundingTableResource
    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "source",
        "reference",
    ]
    date_hierarchy = "created_at"

    list_filter = ["source", "created_at"]

    raw_id_fields = ["agent"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoSuperAgentsResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoSuperAgentsResource
    search_fields = [
        "first_name",
        "last_name",
        "full_name",
        "phone",
        "user_uuid",
    ]
    list_filter = [
        "created_at",
        "type",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoSuperAgentWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoSuperAgentWalletResource
    search_fields = [
        "super_agent__first_name",
        "super_agent__last_name",
        "super_agent__full_name",
        "super_agent__phone",
        "super_agent__user_uuid",
    ]
    date_hierarchy = "created_at"

    raw_id_fields = [
        "super_agent",
    ]
    autocomplete_fields = [
        "super_agent",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoSuperAgentWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoSuperAgentWalletTransactionResource
    search_fields = [
        "wallet__super_agent__first_name",
        "wallet__super_agent__last_name",
        "wallet__super_agent__full_name",
        "wallet__super_agent__phone",
        "wallet__super_agent__user_uuid",
    ]
    date_hierarchy = "created_at"

    raw_id_fields = [
        "wallet",
    ]
    autocomplete_fields = [
        "wallet",
    ]

    list_filter = ("transactton_type", "transaction_from")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RetailTicketRequestLogsResourceAdmin(ImportExportModelAdmin):
    resource_class = RetailTicketRequestLogsResource
    search_fields = [
        "agent__first_name",
        "agent__last_name",
        "agent__full_name",
        "agent__phone",
        "agent__user_uuid",
    ]
    date_hierarchy = "created_at"

    raw_id_fields = [
        "agent",
    ]
    autocomplete_fields = [
        "agent",
    ]

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentOnBoardingPayloadResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentOnBoardingPayloadResource
    search_fields = ["phone"]
    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesRepsResourceAdmin(ImportExportModelAdmin):
    resource_class = SalesRepsResource
    search_fields = ["full_name", "first_name", "last_name", "phone", "email"]
    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesRepWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = SalesRepWalletResource
    search_fields = [
        "sales_rep__full_name",
        "sales_rep__first_name",
        "sales_rep__last_name",
        "sales_rep__phone",
        "sales_rep__email",
    ]
    date_hierarchy = "created_at"
    list_filter = ("created_at",)
    raw_id_fields = [
        "sales_rep",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesRepWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = SalesRepWalletTransactionResource
    search_fields = [
        "wallet__sales_rep__full_name",
        "wallet__sales_rep__first_name",
        "wallet__sales_rep__last_name",
        "wallet__sales_rep__phone",
        "wallet__sales_rep__email",
    ]
    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    raw_id_fields = [
        "wallet",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class FailedRemittanceAgencyWalletChargeResourceAdmin(ImportExportModelAdmin):
    resource_class = FailedRemittanceAgencyWalletChargeResource

    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "agent__user_uuid",
        "trans_ref",
    ]
    date_hierarchy = "created_at"

    list_filter = ("created_at", "successfully_charged")

    raw_id_fields = [
        "agent",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentSuspensionRequestToAgencyBankingResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentSuspensionRequestToAgencyBankingResource

    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "agent__user_uuid",
    ]
    date_hierarchy = "created_at"

    list_filter = ("created_at", "status", "type_of_request")

    raw_id_fields = [
        "agent",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WinWiseEmployeeTableResourceAdmin(ImportExportModelAdmin):
    resource_class = WinWiseEmployeeTableResource
    search_fields = [
        "agent_first_name",
        "mobile_number",
        "bank_verification_no",
        "email",
        "business_address",
        "agent_last_name",
    ]
    # list_filter = ("agent_first_name", "mobile_number", "email", "bank_verification_no")

    def get_list_display(self, request):
        # return ["agent_first_name", "email", "bank_verification_no", "business_address"]
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("signature")
        data.remove("photo_of_agent_location")
        data.remove("photo_of_outlet")
        data.remove("photo_of_vlaid_id")
        data.remove("agent_head_shot_photograph")

        return data


class SupervisorResourceAdmin(ImportExportModelAdmin):
    resource_class = SupervisorResource
    search_fields = [
        "first_name",
        "last_name",
        "full_name",
        "phone",
        "user_uuid",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SupervisorLocationResourceAdmin(ImportExportModelAdmin):
    resource_class = SupervisorResource
    search_fields = ["name"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class TokenAdmin(admin.ModelAdmin):
    search_fields = [
        "key",
        "user__email",
        "user__phone",
        "user__first_name",
        "user__last_name",
    ]
    autocomplete_fields = [
        "user",
    ]
    raw_id_fields = [
        "user",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RawFundingDataResourceAdmin(ImportExportModelAdmin):
    resource_class = RawFundingDataResource

    search_fields = [
        "reference",
    ]
    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesRepAgencyDumpDataResourceAdmin(ImportExportModelAdmin):
    resource_class = SalesRepAgencyDumpDataResource

    # search_fields = [
    #     "reference",
    # ]
    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SupervisorAgencyDumpDataResourceAdmin(ImportExportModelAdmin):
    resource_class = SupervisorAgencyDumpData

    # search_fields = [
    #     "reference",
    # ]
    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SuperAgentAgencyDumpDataResourceAdmin(ImportExportModelAdmin):
    resource_class = SuperAgentAgencyDumpData

    # search_fields = [
    #     "reference",
    # ]
    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentCashBackResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentCashBackResource

    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "agent__user_uuid",
        "reference",
    ]

    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    raw_id_fields = [
        "agent",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WinwiseEmployeeSalaryResourceAdmin(ImportExportModelAdmin):
    resource_class = WinwiseEmployeeSalaryResource

    search_fields = [
        "agent__phone",
        "agent__email",
        "agent__full_name",
        "agent__first_name",
        "agent__last_name",
        "agent__user_uuid",
    ]

    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    raw_id_fields = [
        "agent",
    ]

    if settings.DEBUG is False:
        readonly_fields = ("total_commission", "amount_to_be_paid", "total_sales")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentNoPreFundingResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentNoPreFundingResource

    search_fields = [
        "phone_number",
    ]

    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoSuperAgentCommissionAllocationResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoSuperAgentCommissionAllocationResource

    search_fields = [
        "super_agent_phone_number",
    ]

    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PromotionGamesResourceAdmin(ImportExportModelAdmin):
    resource_class = PromotionGamesResource

    search_fields = [
        "game_type",
    ]

    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoAgentSalesActivityResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoAgentSalesActivityResource

    search_fields = ["agent_name", "agent_email", "agent_phone_number", "agent_terminal_id"]

    date_hierarchy = "created_at"

    list_filter = ("created_at", "activity_status", "week_date_range")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoAgentDailySalesActivityResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoAgentDailySalesActivityResource

    search_fields = ["agent_name", "agent_email", "agent_phone_number", "agent_terminal_id"]

    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class DailyInactiveAgentsResourceAdmin(ImportExportModelAdmin):
    resource_class = DailyInactiveAgentsResource

    search_fields = ["agent_name", "agent_email", "agent_phone_number", "agent_terminal_id"]

    date_hierarchy = "created_at"

    list_filter = ("created_at",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class GamesDailyActivitiesResourceAdmin(ImportExportModelAdmin):
    resource_class = GamesDailyActivitiesResource

    # search_fields = ["agent_name", "agent_email", "agent_phone_number", "agent_terminal_id"]

    date_hierarchy = "created_at"

    list_filter = ["created_at","game_type"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]

        data.remove("lotto_agent_sales")
        data.remove("lotto_agent_winnings")
        data.remove("other_sales")
        data.remove("other_winnings")
        data.remove("percentage_of_winnings_to_sales_for_lotto_agents")
        data.remove("percentage_of_winnings_to_sales_for_other_agents")

        return data
    
    
class LottoAgentFailedTransactionLogResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoAgentFailedTransactionLogResource

    search_fields = ["agent_name", "agent_email", "agent_phone_number",]

    date_hierarchy = "created_at"

    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class TerminalIdUnAssignmentRequestLogsResourceAdmin(ImportExportModelAdmin):
    resource_class = TerminalIdUnAssignmentRequestLogsResource

    search_fields = ["agent_name", "agent_email", "agent_phone_number",]

    date_hierarchy = "created_at"

    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class GeneralRetailLottoGamesResourceAdmin(ImportExportModelAdmin):
    resource_class = GeneralRetailLottoGamesResource

    search_fields = ["agent_name", "agent_email", "agent_phone_number", "batch_uuid", "game_play_id"]

    date_hierarchy = "created_at"

    list_filter = ["created_at", "double_chance", "type_of_agent", "ticket_type", "lotto_game_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class AgencyBankingTokenResourceAdmin(ImportExportModelAdmin):
    resource_class = AgencyBankingTokenResource

    date_hierarchy = "created_at"

    list_filter = ["created_at", "account"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class AgencyBankingLoginAttemptResourceAdmin(ImportExportModelAdmin):
    resource_class = AgencyBankingLoginAttemptResource

    date_hierarchy = "created_at"

    list_filter = ["created_at", "account"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(Token, TokenAdmin)
admin.site.register(AgentPayoutBeneficiary, AgentPayoutBeneficiaryResourceAdmin)
admin.site.register(PosLotteryWinners, PosLotteryWinnersResourceAdmin)
admin.site.register(AgentWalletTransaction, AgentWalletTransactionResourceAdmin)
admin.site.register(Agent, AgentModelResourceAdmin)
admin.site.register(AgentWallet, AgentWalletResourceAdmin)
admin.site.register(ChargeAgentTransaction, ChargeAgentTransactionResourceAdmin)
admin.site.register(BoughtLotteryTickets, BoughtLotteryTicketsResourceAdmin)
admin.site.register(AgentBonus, AgentBonusResourceAdmin)
admin.site.register(AgentConstantVariables, AgentConstantVariablesResourceAdmin)
admin.site.register(GamesTable, GamesTableResourceAdmin)
admin.site.register(LottoAgentRemittanceTable, LottoAgentRemittanceTableResourceAdmin)
admin.site.register(LottoAgentGuarantorDetail, LottoAgentGuarantorDetailResourceAdmin)
admin.site.register(AgentVFDAccountDetail, AgentVFDAccountDetailResourceAdmin)
admin.site.register(CreateAgentLogs, CreateAgentLogsResourceAdmin)
admin.site.register(PosWithdrawalRequest, PosWithdrawalRequestResourceAdmin)
admin.site.register(AgentFundingTable, AgentFundingTableResourceAdmin)
admin.site.register(LottoSuperAgents, LottoSuperAgentsResourceAdmin)
admin.site.register(LottoSuperAgentWallet, LottoSuperAgentWalletResourceAdmin)
admin.site.register(LottoSuperAgentWalletTransaction, LottoSuperAgentWalletTransactionResourceAdmin)
admin.site.register(AgencyBankingLogs, AgencyBankingLogsAdmin)
admin.site.register(RetailTicketRequestLogs, RetailTicketRequestLogsResourceAdmin)
admin.site.register(AgentOnBoardingPayload, AgentOnBoardingPayloadResourceAdmin)

admin.site.register(SalesReps, SalesRepsResourceAdmin)
admin.site.register(SalesRepWallet, SalesRepWalletResourceAdmin)
admin.site.register(SalesRepWalletTransaction, SalesRepWalletTransactionResourceAdmin)
admin.site.register(FailedRemittanceAgencyWalletCharge, FailedRemittanceAgencyWalletChargeResourceAdmin)
admin.site.register(
    AgentSuspensionRequestToAgencyBanking,
    AgentSuspensionRequestToAgencyBankingResourceAdmin,
)
admin.site.register(WinWiseEmployeeTable, WinWiseEmployeeTableResourceAdmin)

admin.site.register(
    Supervisor,
    SupervisorResourceAdmin,
)

admin.site.register(
    RawFundingData,
    RawFundingDataResourceAdmin,
)
admin.site.register(SupervisorLocation, SupervisorLocationResourceAdmin)

admin.site.register(SalesRepAgencyDumpData, SalesRepAgencyDumpDataResourceAdmin)
admin.site.register(SupervisorAgencyDumpData, SupervisorAgencyDumpDataResourceAdmin)
admin.site.register(SuperAgentAgencyDumpData, SuperAgentAgencyDumpDataResourceAdmin)
admin.site.register(RemunerationChargeRemittance, AgentCashBackResourceAdmin)
admin.site.register(WinwiseEmployeeSalary, WinwiseEmployeeSalaryResourceAdmin)
admin.site.register(AgentNoPreFunding, AgentNoPreFundingResourceAdmin)
admin.site.register(LottoSuperAgentCommissionAllocation, LottoSuperAgentCommissionAllocationResourceAdmin)
admin.site.register(PromotionGames, PromotionGamesResourceAdmin)
admin.site.register(LottoAgentSalesActivity, LottoAgentSalesActivityResourceAdmin)
admin.site.register(LottoAgentDailySalesActivity, LottoAgentDailySalesActivityResourceAdmin)
admin.site.register(DailyInactiveAgents, DailyInactiveAgentsResourceAdmin)
admin.site.register(GamesDailyActivities, GamesDailyActivitiesResourceAdmin)
admin.site.register(LottoAgentFailedTransactionLog, LottoAgentFailedTransactionLogResourceAdmin)
admin.site.register(TerminalIdUnAssignmentRequestLogs, TerminalIdUnAssignmentRequestLogsResourceAdmin)
admin.site.register(GeneralRetailLottoGames, GeneralRetailLottoGamesResourceAdmin)
admin.site.register(AgencyBankingToken, AgencyBankingTokenResourceAdmin)
admin.site.register(AgencyBankingLoginAttempt, AgencyBankingLoginAttemptResourceAdmin)
