import calendar
import random
import uuid
from datetime import date, datetime, timedelta
from random import randint
import json
from typing import Dict, Union
from django.db.models import Sum, Count, Q
import math
import json
from datetime import datetime, timedelta
import pytz

import pytz
from decouple import config
from django.conf import settings
from django.contrib.auth.hashers import make_password
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.db.models import Sum

from account.models import User
from africa_lotto.models import AfricaLotto, AfricaLottoGameType
from main.models import (
    LotteryBatch,
    LotteryModel,
    LottoTicket,
    LottoWinners,
    PayoutTransactionTable,
    UserProfile,
)
from main.ussd.helpers import Utility
from payout_process.models import PayoutProcessFirstStep
from pos_app.models import (
    Agent,
    AgentOnBoardingPayload,
    AgentSuspensionRequestToAgencyBanking,
    AgentWallet,
    AgentWalletTransaction,
    CreateAgentLogs,
    LottoSuperAgents,
    LottoSuperAgentWalletTransaction,
    PosLotteryWinners,
)
from pos_app.pos_helpers import CustomJSONEncoder, PosAgentHelper, get_agent_detail_type
from pos_app.utils import DommyName
from wallet_app.models import (
    DebitCreditRecord,
    FloatWallet,
    UserWallet,
)
from wallet_system.models import Wallet
from wyse_ussd.helper.general_helper import (
    retail_has_enough_money_to_give_out,
)


class ManageMobileRequest:

    @staticmethod
    def suspend_or_unsuspend_terminal_on_agency_banking(
        agent_instance: Agent, suspend_or_unsuspend_reason="Default restriction on lotto", suspend="true"
    ):
        """
        Suspend or unsuspend an agent's terminal on agency banking.

        This method updates the suspension status of an agent's terminal based on the provided parameters.
        It creates a suspension record and interacts with the agency banking system to apply the suspension
        or unsuspension.

        Args:
            agent_instance (Agent): The agent instance whose terminal is to be suspended or unsuspended.
            suspend_or_unsuspend_reason (str, optional): The reason for the suspension or unsuspension.
                Defaults to "Default restriction on lotto".
            suspend (str, optional): A string indicating whether to suspend or unsuspend the agent's terminal.
                Accepts "true" to suspend and any other value to unsuspend. Defaults to "true".

        Returns:
            tuple: A tuple containing a boolean indicating the success of the operation and a message.
                - (bool): True if the operation was successful, False otherwise.
                - (str): A message indicating the result of the operation ("Restricted" or "un-suspended").

        Raises:
            Exception: Raises an exception if there is an issue with the agency banking system response.

        Notes:
            - If the environment is set to "prod", the method will attempt to communicate with the agency banking
              system to apply the suspension or unsuspension.
            - In non-production environments, the method will update the agent's status locally without
              external communication.
        """

        suspension_payload = {
            "user_id": agent_instance.user_id,
            "suspend": suspend,
            "suspend_or_unsuspend_reason": suspend_or_unsuspend_reason,
        }

        suspension_record_instance = AgentSuspensionRequestToAgencyBanking.objects.create(
            agent=agent_instance,
            reason=suspend_or_unsuspend_reason,
            payload=suspension_payload,
        )

        print("SUSPENSION RECORD INSTANCE: ", suspension_record_instance)
        if config("ENVIRONMENT") == "prod":
            print("SUSPENSION PAYLOAD: ", suspension_payload)

            pos_agent_helper = PosAgentHelper(
                agent_instance=agent_instance,
                amount=0,
                pin=0,
            )

            suspension_response = pos_agent_helper.suspend_lotto_agent_on_agency_banking(**suspension_payload)
            suspension_record_instance.refresh_from_db = suspension_response
            suspension_record_instance.save()

            if isinstance(suspension_response, dict):
                if (str(suspension_response.get("status")).lower()) == "success":
                    if suspend == "true":
                        agent_instance.suspended_on_agency_banking = True
                        agent_instance.suspending_from_task = True
                        agent_instance.is_suspended = True
                        agent_instance.save()
                    else:
                        agent_instance.suspended_on_agency_banking = False
                        agent_instance.suspending_from_task = False
                        agent_instance.is_suspended = False
                        agent_instance.save()

                    suspension_record_instance.status = "APPROVED"
                    suspension_record_instance.save()

                    msg = "Restricted" if suspend == "true" else "un-suspended"
                    return True, msg

                elif suspension_response.get("error") == "478":
                    if suspend == "true":
                        agent_instance.suspended_on_agency_banking = True
                        agent_instance.suspending_from_task = True
                        agent_instance.is_suspended = True
                        agent_instance.save()
                    else:
                        agent_instance.suspended_on_agency_banking = False
                        agent_instance.suspending_from_task = False
                        agent_instance.is_suspended = False
                        agent_instance.save()

                    suspension_record_instance.status = "APPROVED"
                    suspension_record_instance.save()

                    msg = "Restricted" if suspend == "true" else "un-suspended"
                    return True, msg
                else:
                    msg = "Failed to restrict agent" if suspend == "true" else "Failed to unsuspend agent"
                    return False, msg
            else:
                msg = "Failed to restrict agent" if suspend == "true" else "Failed to unsuspend agent"
                return False, msg

        else:
            print("SUSPENSION PAYLOAD w: ", suspension_payload)
            if suspend == "true":
                agent_instance.suspended_on_agency_banking = True
                agent_instance.suspending_from_task = True
                agent_instance.is_suspended = True
                agent_instance.save()
            else:
                agent_instance.suspended_on_agency_banking = False
                agent_instance.suspending_from_task = False
                agent_instance.is_suspended = False
                agent_instance.save()

            msg = "Restricted" if suspend == "true" else "unsuspended"
            return True, msg

    @staticmethod
    def suspend_or_unsuspend_agent_from_withdrawing(agent_instance: Agent, can_withdraw=False):
        """
        Suspend or unsuspend an agent's ability to withdraw funds.

        This method updates the withdrawal permission status of the specified agent.
        It sets the `can_withdraw` attribute of the agent instance and saves the changes to the database.

        Args:
            agent_instance (Agent): The agent instance whose withdrawal permission is to be updated.
            can_withdraw (bool, optional): A boolean indicating whether the agent can withdraw funds.
                Defaults to False, which means the agent will be restricted from withdrawing.

        Returns:
            tuple: A tuple containing a boolean indicating the success of the operation and a message.
                - (bool): True, indicating the operation was successful.
                - (str): A message indicating the result of the operation ("Restricted" or "un-suspended").

        Notes:
            - If `can_withdraw` is set to False, the agent will be restricted from withdrawing funds.
            - If `can_withdraw` is set to True, the agent will be allowed to withdraw funds.
        """

        agent_instance.can_withdraw = can_withdraw
        agent_instance.save()
        msg = "Restricted" if can_withdraw is False else "un-suspended"
        return True, msg

    @staticmethod
    def generate_agent_end_of_day_report(agent_instance: Agent):

        now = datetime.now()

        formatted_date_time = now.strftime("%Y-%m-%d %I:%M %p")

        today = date.today()

        start_date = today
        end_date = today

        total_sales = 0
        total_ticket_sales = 0

        sales_data = []

        africa_lotto_queryset = AfricaLotto.objects.filter(
            agent_phone_number=agent_instance.phone, created_at__date__range=[start_date, end_date], paid=True
        )

        gh_lotto = africa_lotto_queryset.filter(game_type=AfricaLottoGameType.GHANA_LOTTO)
        kenya_lotto = africa_lotto_queryset.filter(game_type=AfricaLottoGameType.KENYA_LOTTO)

        total_sales_for_ghana_lotto = gh_lotto.aggregate(Sum("purchase_amount"))["purchase_amount__sum"]
        total_sales_for_kenya_lotto = kenya_lotto.aggregate(Sum("purchase_amount"))["purchase_amount__sum"]

        if not total_sales_for_ghana_lotto:
            total_sales_for_ghana_lotto = 0

        if not total_sales_for_kenya_lotto:
            total_sales_for_kenya_lotto = 0

        if total_sales_for_ghana_lotto > 0:
            sales_data.append(
                {"game_type": "Ghana Lotto", "count": len(gh_lotto), "total_sales": Utility.currency_formatter(total_sales_for_ghana_lotto)}
            )

            total_ticket_sales += len(gh_lotto)

            total_sales += total_sales_for_ghana_lotto

        if total_sales_for_kenya_lotto > 0:
            sales_data.append(
                {"game_type": "Kenya Lotto", "count": len(kenya_lotto), "total_sales": Utility.currency_formatter(total_sales_for_kenya_lotto)}
            )

            total_sales += total_sales_for_ghana_lotto

            total_ticket_sales += len(kenya_lotto)

        lotto_ticket_queryset = LottoTicket.objects.filter(date__date__range=[start_date, end_date], paid=True, agent_profile=agent_instance)
        salary_for_life_ticket_queryset = lotto_ticket_queryset.filter(lottery_type="SALARY_FOR_LIFE")
        instant_cash_ticket_queryset = lotto_ticket_queryset.filter(lottery_type="INSTANT_CASHOUT")
        quika_ticket_queryset = lotto_ticket_queryset.filter(lottery_type="QUIKA")
        banker_ticket_queryset = lotto_ticket_queryset.filter(lottery_type="BANKER")

        total_sales_for_salary_for_life = salary_for_life_ticket_queryset.aggregate(Sum("amount_paid"))["amount_paid__sum"]
        total_sales_for_instant_cash = instant_cash_ticket_queryset.aggregate(Sum("amount_paid"))["amount_paid__sum"]
        total_sales_for_quika = quika_ticket_queryset.aggregate(Sum("amount_paid"))["amount_paid__sum"]
        total_sales_for_banker = banker_ticket_queryset.aggregate(Sum("amount_paid"))["amount_paid__sum"]

        if not total_sales_for_salary_for_life:
            total_sales_for_salary_for_life = 0

        if not total_sales_for_instant_cash:
            total_sales_for_instant_cash = 0

        if not total_sales_for_quika:
            total_sales_for_quika = 0

        if not total_sales_for_banker:
            total_sales_for_banker = 0

        if total_sales_for_salary_for_life > 0:
            sales_data.append(
                {
                    "game_type": "Salary For Life",
                    "count": len(salary_for_life_ticket_queryset),
                    "total_sales": Utility.currency_formatter(total_sales_for_salary_for_life),
                }
            )

            total_ticket_sales += len(salary_for_life_ticket_queryset)
            total_sales += total_sales_for_salary_for_life

        if total_sales_for_instant_cash > 0:
            sales_data.append(
                {
                    "game_type": "Instant Cashout",
                    "count": len(instant_cash_ticket_queryset),
                    "total_sales": Utility.currency_formatter(total_sales_for_instant_cash),
                }
            )

            total_ticket_sales += len(instant_cash_ticket_queryset)
            total_sales += total_sales_for_instant_cash

        if total_sales_for_quika > 0:
            sales_data.append(
                {"game_type": "Quika", "count": len(quika_ticket_queryset), "total_sales": Utility.currency_formatter(total_sales_for_quika)}
            )

            total_ticket_sales += len(quika_ticket_queryset)
            total_sales += total_sales_for_quika

        if total_sales_for_banker > 0:
            sales_data.append(
                {"game_type": "Banker", "count": len(banker_ticket_queryset), "total_sales": Utility.currency_formatter(total_sales_for_banker)}
            )

            total_ticket_sales += len(banker_ticket_queryset)
            total_sales += total_sales_for_banker

        # wyse cash ticket queryset
        wyse_cash_ticket_queryset = LotteryModel.objects.filter(date__date__range=[start_date, end_date], paid=True, agent_profile=agent_instance)
        total_sales_for_wyse_cash = wyse_cash_ticket_queryset.aggregate(Sum("amount_paid"))["amount_paid__sum"]

        if not total_sales_for_wyse_cash:
            total_sales_for_wyse_cash = 0

        if total_sales_for_wyse_cash > 0:
            sales_data.append(
                {
                    "game_type": "Wyse Cash",
                    "count": len(wyse_cash_ticket_queryset),
                    "total_sales": Utility.currency_formatter(total_sales_for_wyse_cash),
                }
            )

            total_ticket_sales += len(wyse_cash_ticket_queryset)
            total_sales += total_sales_for_wyse_cash

        # fetch commission sales for the agent
        agent_commission_sales = AgentWalletTransaction.objects.filter(
            date_created__date__range=[start_date, end_date],
            agent_wallet__agent__phone=agent_instance.phone,
            transaction_from="COMMISSION_ON_GAME_PLAY",
        )
        total_commission_sales = agent_commission_sales.aggregate(Sum("amount"))["amount__sum"]
        if not total_commission_sales:
            total_commission_sales = 0

        queryset = PosLotteryWinners.objects.filter(date_created__date__range=[start_date, end_date], agent=agent_instance)

        agent_store_winnings = queryset.aggregate(Sum("amount_won"))["amount_won__sum"]
        if not agent_store_winnings:
            agent_store_winnings = 0

        return {
            "date": formatted_date_time,
            "agent_id": f"w-{agent_instance.id}",
            "total_ticket_sold": total_ticket_sales,
            "total_stake_amount": Utility.currency_formatter(total_sales),
            "total_commission_earned": Utility.currency_formatter(total_commission_sales),
            "store_winnings": Utility.currency_formatter(agent_store_winnings),
            "sales_data": sales_data,
        }

    # @staticmethod
    # def agent_store_winnings(agent_instance: Agent):
    #     """
    #     Fetch the total winnings from agent store.

    #     Args:
    #         agent_instance (Agent): The agent instance whose stored winnings are to be fetched.

    #     """
    #     # now = datetime.now()

    #     today = date.today()

    #     start_date = today
    #     end_date = today

    #     queryset = PosLotteryWinners.objects.filter(date_created__date__range=[start_date, end_date])

    #     global_store_winnings = queryset.aggregate(Sum("amount_won"))["amount_won__sum"]
    #     if not global_store_winnings:
    #         global_store_winnings = 0

    #     agent_store_winnings = queryset.filter(agent=agent_instance).aggregate(Sum("amount_won"))["amount_won__sum"]
    #     if not agent_store_winnings:
    #         agent_store_winnings = 0

    #     agent_total_winnings = len(queryset.filter(agent=agent_instance))

    #     return {
    #         "global_store_winnings": Utility.currency_formatter(global_store_winnings, return_currency=False),
    #         "agent_store_winnings": Utility.currency_formatter(agent_store_winnings, return_currency=False),
    #         "agent_total_winnings": agent_total_winnings,
    #     }

    @staticmethod
    def agent_store_winnings(agent_instance: Agent):
        """
        Fetch the total winnings from agent store, adding 500k to global winnings
        for each hour passed since 6 AM.

        Args:
            agent_instance (Agent): The agent instance whose stored winnings are to be fetched.
        """
        # Get current date and time
        now = datetime.now()
        today = date.today()
        start_date = today
        end_date = today

        start_hour = 9  # 6 AM
        current_hour = now.hour

        hourly_bonus = 0
        if current_hour >= start_hour:
            hours_passed = current_hour - start_hour + 1
            random_amount = random.choice([50000, 100000, 150000, 200000, 250000, 300000, 350000, 400000, 450000, 500000])
            hourly_bonus = hours_passed * random_amount

        queryset = PosLotteryWinners.objects.filter(date_created__date__range=[start_date, end_date])
        global_store_winnings = queryset.aggregate(Sum("amount_won"))["amount_won__sum"]
        if not global_store_winnings:
            global_store_winnings = 0

        global_store_winnings += hourly_bonus

        agent_store_winnings = queryset.filter(agent=agent_instance).aggregate(Sum("amount_won"))["amount_won__sum"]
        if not agent_store_winnings:
            agent_store_winnings = 0

        agent_total_winnings = len(queryset.filter(agent=agent_instance))

        # agent wallet transactions for today
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        agent_sales_value = (
            AgentWalletTransaction.objects.filter(
                date_created__date=TODAY.date(), agent_phone_number=agent_instance.phone, transaction_from="GAME_PLAY"
            ).aggregate(Sum("amount"))["amount__sum"]
            or 0
        )

        return {
            "global_store_winnings": Utility.currency_formatter(global_store_winnings, return_currency=False),
            "agent_store_winnings": Utility.currency_formatter(agent_store_winnings, return_currency=False),
            "agent_total_winnings": agent_total_winnings,
            "today_sales": Utility.currency_formatter(agent_sales_value, return_currency=False),
        }

    @staticmethod
    # @transaction.atomic
    def agent_new_withdrawal(agent_instance: Agent, **kwargs):

        game_id = kwargs.get("game_id")
        pin = kwargs.get("pin")
        kwargs.get("agent_transaction_pin")
        click_id = kwargs.get("click_id")

        payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        pos_lottery_winner_qs = PosLotteryWinners.objects.filter(game_id__iexact=game_id, pin=pin)

        if not pos_lottery_winner_qs.exists():
            # if str(pin).lower() == "null":
            pos_lottery_winner_qs = PosLotteryWinners.objects.filter(game_id__iexact=game_id)

            if not pos_lottery_winner_qs.exists():
                return False, "No winning record found"
            

        pos_lottery_winner = pos_lottery_winner_qs.last()

        if pos_lottery_winner.is_win_claimed is True:  # noqa
            if pos_lottery_winner.payout_successful is True:
                return False, "Winning already claimed"

            if pos_lottery_winner.claimant == None:  # noqa
                return False, "Winning already claimed by another player"
            else:
                if pos_lottery_winner.claimant.phone == agent_instance.phone:
                    _status, _msg = _handle_withdraw_for_failed_transactions(pos_lottery_winner, agent_instance)
                    return _status, _msg
                elif pos_lottery_winner.claimant.phone != agent_instance.phone:
                    return False, "Winning already claimed by another player"
                else:
                    return False, "You have already claimed your winnings"

        amount_won = pos_lottery_winner.amount_won

        amount = amount_won

        if FloatWallet.get_float_wallet(source="RETAIL_RTP_WALLET").amount < amount_won:
            return False, "We're sorry, an error occured while processing your request. Please try again later!!!"

        # if not PosLotteryWinners.validate_pin(
        #     agent=agent_instance,
        #     pin=agent_pin,
        #     game_id=game_id,
        #     player_phone=phone,
        # ):
        #     return False, "Invalid pin"

        # fund agent wallet
        payload = {
            "transaction_from": "GLOBAL_WINNINGS",
            "game_type": pos_lottery_winner.lottery_type,
            "game_play_id": pos_lottery_winner.game_id,
        }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent_instance.phone,
            amount=amount_won,
            channel="POS/MOBILE",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="CREDIT",
        )

        UserWallet.fund_wallet(
            user=agent_instance,
            amount=amount_won,
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **payload,
        )

        agent_wallet = AgentWallet.objects.filter(agent=agent_instance).last()

        if agent_wallet is None:
            return False, "You have no withdrawal wallet"

        balance_to_charge = agent_wallet.winnings_bal

        if balance_to_charge < amount_won:
            return False, "Insufficient balance"

        if retail_has_enough_money_to_give_out(amount_won) is False:
            return False, "We're sorry, an error occured while processing your request. Please try again later"

        if FloatWallet.get_float_wallet(source="RETAIL_RTP_WALLET").amount < amount_won:
            return False, "Withdrawal network error. Please try again late"

        # create a payout process logs
        was_successful = PayoutProcessFirstStep.created_record(
            game_play_id=game_id,
            amount=amount,
            user_phone_number=agent_instance.phone,
            user_name=f"{agent_instance.first_name} {agent_instance.last_name}",
            payout_referece=payout_reference,
            click_id=click_id,
        )

        if was_successful is False:
            return False, "We're sorry, you're making too many requests. Please try again later"

        # check if agent has already claimed this amount
        game_withdrawal_record = PayoutTransactionTable.objects.filter(game_play_id=game_id).last()
        if game_withdrawal_record is not None:
            if game_withdrawal_record.disbursed:
                pos_lottery_winner.claimant = agent_instance
                pos_lottery_winner.is_win_claimed = True
                pos_lottery_winner.payout_successful = True
                pos_lottery_winner.payout_verified = True
                pos_lottery_winner.save()

                return False, "ticket already claimed."
            
            if game_withdrawal_record.is_verified is False:
                return False, "payout verification is still pending. please wait."

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent_wallet.agent.phone,
            amount=amount_won,
            channel="POS",
            reference=payout_reference,
            transaction_type="DEBIT",
        )

        wallet_payload = {
            "transaction_from": "WINNINGS_WITHDRAW",
            "game_type": pos_lottery_winner.lottery_type,
            "game_play_id": pos_lottery_winner.game_id,
        }

        UserWallet.deduct_wallet(
            user=agent_wallet.agent,
            amount=int(amount_won),
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **wallet_payload,
        )

        # remove the money from general withdrawable wallet
        # GeneralWithdrawableWallet.deduct_fund(amount=amount, phone=agent_instance.phone)
        Wallet.debit_wallet(
            wallet_type="RETAIL_RTP_WALLET",
            amount=amount,
            game_type=pos_lottery_winner.lottery_type,
            user_phone=agent_instance.phone,
            user_name=f"{agent_instance.first_name} {agent_instance.last_name}",
            game_play_id=pos_lottery_winner.game_id,
        )

        payload = {
            "from_wallet_type": "COLLECTION",
            "to_wallet_type": "COLLECTION",
            "data": [
                {
                    "buddy_phone_number": agent_instance.phone,
                    "amount": amount_won,
                    "narration": "LOTTO_WINNING_WITHDRAWAL",
                    "is_beneficiary": "False",
                    "save_beneficiary": "True",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                    "customer_reference": f"{payout_reference}-{game_id}",
                }
            ],
        }

        agent_wallet.refresh_from_db()

        pos_lottery_winner.payout_ref = payout_reference
        pos_lottery_winner.save()

        _withdraw_table_instance = PayoutTransactionTable.objects.create(
            source="BUDDY",
            amount=amount,
            disbursement_unique_id=payout_reference,
            phone=agent_instance.phone,
            payout_trans_ref=payout_reference,
            channel="POS",
            game_play_id=game_id,
            payout_payload=payload,
            unique_game_play_id=game_id,
            date_won=pos_lottery_winner.date_created,
            balance_before=agent_wallet.winnings_bal + amount,
            balance_after=agent_wallet.winnings_bal,
            joined_since=agent_instance.get_duration(),
            name=f"{agent_instance.first_name} {agent_instance.last_name}",
            source_wallet="RETAIL_RTP_WALLET",
            recipient_wallet="USER_WALLET",
        )

        cp_withdraw_table_instance = _withdraw_table_instance

        cp_payload = payload

        pos_lottery_winner.is_win_claimed = True
        pos_lottery_winner.withdrawl_initiated = True
        pos_lottery_winner.claimant = agent_instance
        pos_lottery_winner.save()

        print("CP PAYLOAD: ", cp_payload)
        pos_agent_helper = PosAgentHelper(agent_instance=agent_instance, amount=amount_won)
        print("CP PAYLOAD 22: ", cp_payload)

        response = pos_agent_helper.agency_buddy_transfer(**cp_payload)
        cp_response = response

        if isinstance(cp_response, dict):

            if cp_response.get("message") == "success":
                cp_withdraw_table_instance.disbursed = True
                cp_withdraw_table_instance.is_verified = True
                cp_withdraw_table_instance.save()

                pos_lottery_winner.payout_successful = True
                pos_lottery_winner.payout_verified = True
                pos_lottery_winner.save()
            else:
                raise ValidationError(cp_response)
        else:
            raise ValidationError(cp_response)

        _withdraw_table_instance.source_response_payload = cp_response
        _withdraw_table_instance.save()

        data = {
            "status": "success",
            "message": "Withdrawal initiated",
            "transaction_ref": payout_reference,
            "agent_id": f"winwise-{agent_instance.id}",
            "account_name": f"{agent_instance.first_name} {agent_instance.last_name}",
            "bank_name": "Liberty Pay vfd",
            "amount": amount_won,
            "reference": cp_withdraw_table_instance.payout_trans_ref,
            "date": datetime.now(),
            "data": None,
        }

        return True, data

    @staticmethod
    def agent_downline_notification(data: dict):
        """
        RECEIVE NOTIFICATION FROM AGENT DOWNLINE

        SAMPLE DATA

        {
            "super_agent": {
                "first_name": "John",
                "last_name": "Doe",
                "phone": "***********",
                "email": "<EMAIL>",
                "user_id": "123456",
                "user_uuid: "345dfw-345df-345df-345df",
                "address": "No 1, John Doe Street, Lagos",
            },
            "agent": {
                "first_name": "John",
                "last_name": "Doe",
                "phone": "***********",
                "email": "<EMAIL>",
                "user_id": "123456",
                "user_uuid: "345dfw-345df-345df-345df",
                "address": "No 1, John Doe Street, Lagos",
            },
            "supervisor": {
                "first_name": "John",
                "last_name": "Doe",
                "phone": "***********",
                "email": "<EMAIL>",
                "user_id": "123456",
                "user_uuid: "345dfw-345df-345df-345df",
                "address": "No 1, John Doe Street, Lagos",
            }
        }
        """

        super_agent = data.get("super_agent")
        agent = data.get("agent")
        data.get("supervisor")

        super_agent_phone_number = super_agent.get("phone")
        agent_phone_number = agent.get("phone")
        # supervisor.get("phone")

        if super_agent_phone_number is not None:
            try:
                get_super_agent_instance = LottoSuperAgents.objects.get(phone=super_agent_phone_number)
            except LottoSuperAgents.DoesNotExist:
                get_super_agent_instance = LottoSuperAgents.objects.create(
                    phone=super_agent_phone_number,
                    first_name=super_agent.get("first_name"),
                    last_name=super_agent.get("last_name"),
                    email=super_agent.get("email"),
                    user_id=super_agent.get("user_id"),
                    user_uuid=super_agent.get("user_uuid"),
                    address=super_agent.get("address"),
                )
        else:
            get_super_agent_instance = None

        try:
            agent_instance = Agent.objects.get(phone=agent_phone_number)
        except Agent.DoesNotExist:
            # create user profile
            try:
                UserProfile.objects.create(
                    phone_number=LotteryModel.format_number_from_back_add_234(agent_phone_number),
                    email=agent.get("email"),
                    first_name=agent.get("first_name"),
                    last_name=agent.get("last_name"),
                    channel="POS",
                )
            except IntegrityError:
                pass

            # creating login user details
            try:
                User.objects.create(
                    email=agent.get("email"),
                    phone=agent_phone_number,
                    password=make_password(agent.get("user_uuid")),
                    first_name=agent.get("first_name"),
                    last_name=agent.get("last_name"),
                    channel="APP/POS",
                    phone_is_verified=True,
                )

            except IntegrityError:
                pass

            CreateAgentLogs.objects.create(phone=agent_phone_number, payload=data)

            first_name = agent.get("first_name")
            last_name = agent.get("last_name")
            user_uuid = agent.get("user_uuid")
            address = agent.get("address")
            user_id = agent.get("user_id")
            email = agent.get("email")

            try:
                get_agent_type = get_agent_detail_type(user_id)
            except Exception:
                get_agent_type = {"status": "error", "type_of_user": "error"}

            agent_type = "AGENT"
            terminal_id = None

            if get_agent_type.get("type_of_user") is not None and get_agent_type.get("status") == "success":
                agent_type = get_agent_type.get("type_of_user")
                terminal_id = get_agent_type.get("terminal_id")

            AgentOnBoardingPayload.objects.create(phone=agent_phone_number, payload=data)

            agent_instance = Agent.objects.create(
                first_name=first_name,
                last_name=last_name,
                phone=agent_phone_number,
                email=email,
                user_id=user_id,
                user_uuid=user_uuid,
                address=address,
                agent_type=agent_type,
                terminal_id=terminal_id,
                super_agent=get_super_agent_instance,
            )

        agent_instance.super_agent = get_super_agent_instance
        agent_instance.save()

        return True, "Notification sent successfully"

    @staticmethod
    def new_agent_wyse_cash_game_generation():
        name = DommyName(6).generate_name()

        # Define functions outside the loop
        def generate_lucky_number():
            # n = 7
            # range_start = 10 ** (n - 1)
            # range_end = (10**n) - 1
            # value = randint(range_start, range_end)
            # return str(value)

            return str(int(datetime.now().timestamp() * 1000))

        def full_name_split(name):
            """
            This functions split and return user names in a dictonary
            """
            splited_names = name.split()
            names = {
                "first_name": splited_names[0] if len(splited_names) > 0 else "",
                "last_name": splited_names[1] if len(splited_names) > 1 else "",
                "middle_name": splited_names[2] if len(splited_names) > 2 else "",
                "full_name": name,
            }
            return names

        names = full_name_split(name)
        pack_data = []
        band_stake_amount = {}
        bands = [10000, 50000, 100000, 200000]

        # Process each band
        for i, band in enumerate(bands):
            lottery_generated = []

            # Generate unique lucky numbers
            while len(lottery_generated) <= 10:
                lucky_number = f"{names.get('first_name')[0]}{names.get('last_name')[0]}-{generate_lucky_number()}"

                # Check if number already exists in database
                lottery_model_obj = LotteryModel.objects.filter(lucky_number=lucky_number)
                if not lottery_model_obj.exists():
                    lottery_generated.append(lucky_number)

            _band = []

            # Process each generated number
            for num, number in enumerate(lottery_generated, 1):
                if num >= 11:
                    continue

                dynamic_price = LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(band=band, no_of_line=1)

                if num == 1:
                    band_stake_amount[f'{dynamic_price.get("stake_amount")}'] = str(band)

                # if band == 10000:
                #     stake_amount =

                _band.append({"number": number, "stake": dynamic_price.get("stake_amount")})

            # Store in pack_data with the appropriate key
            pack_data.append(
                {
                    "band": band,
                    "lottery": _band,
                }
            )
            # pack_data[pack_keys[i]] = _band

        list_bands_stakes = []
        for key, value in band_stake_amount.items():
            list_bands_stakes.append({"stake": key, "band": value})
        return {
            "status": "success",
            "message": "sucess",
            "data": {
                "lottery_data": pack_data,
            },
            "bands": list_bands_stakes,
        }

    @staticmethod
    def manually_create_winning_for_a_ticket(ticket_id, amount_won, model, game_play_id):
        if model == "LOTTO_TICKET":
            ticket_instance = LottoTicket.objects.get(id=ticket_id, game_play_id=game_play_id)
            if ticket_instance.paid is False:
                LottoTicket.objects.filter(id=ticket_id).update(paid=True)
                ticket_instance.refresh_from_db()

            if PosLotteryWinners.objects.filter(agent=ticket_instance.agent_profile, game_id=ticket_instance.game_play_id).exists():
                return False, "winning ticket already created"

            get_batch = LotteryBatch.objects.get(id=ticket_instance.batch.id)

            LottoWinners.create_lotto_winner_obj(
                batch=get_batch,
                phone_number=ticket_instance.phone,
                ticket=ticket_instance.ticket.split(","),
                win_type="ORDINARY_WINNER",
                match_type="PERM_3",
                lotto_type=ticket_instance.lottery_type,
                game_play_id=ticket_instance.game_play_id,
                stake_amount=ticket_instance.stake_amount,
                earning=amount_won,
                channel_played_from=ticket_instance.channel,
                run_batch_id=f"{datetime.now().timestamp()}-{ticket_instance.id}",
                lottery=ticket_instance,
            )

            return True, "winning ticket created"
        return False, "Model not found"



    @staticmethod
    def generate_agent_sales_metrics(agent_instance: Agent):
        """
        Generate the sales metrics for the specified agent (optimized version).

        Args:
            agent_instance (Agent): The agent instance whose sales metrics are to be generated.

        Returns:
            str: JSON string containing the agent's sales metrics.
        """
        # Get date ranges at once and avoid redundant calculations
        date_ranges = get_date_ranges()
        last_month_details = get_last_month_details()
        
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        YESTERDAY = TODAY - timedelta(days=1)
        start_of_month = TODAY.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # Optimize by fetching data in fewer queries with annotations
        agent_phone = agent_instance.phone
        
        # 1. Define a function to efficiently get transaction sums by date range and type
        def get_transaction_sums(transaction_type):
            base_query = AgentWalletTransaction.objects.filter(agent_phone_number=agent_phone)
            
            # Use a single query with conditional aggregation for all date ranges
            result = base_query.aggregate(
                today_sum=Sum('amount', filter=Q(date_created__date=TODAY.date(), transaction_from=transaction_type)),
                yesterday_sum=Sum('amount', filter=Q(date_created__date=YESTERDAY.date(), transaction_from=transaction_type)),
                this_week_sum=Sum('amount', filter=Q(
                    date_created__date__range=[date_ranges["week_start"], date_ranges["week_end"]], 
                    transaction_from=transaction_type
                )),
                last_week_sum=Sum('amount', filter=Q(
                    date_created__date__range=[date_ranges["last_week_start"], date_ranges["last_week_end"]], 
                    transaction_from=transaction_type
                )),
                this_month_sum=Sum('amount', filter=Q(date_created__gte=start_of_month, transaction_from=transaction_type)),
                last_month_sum=Sum('amount', filter=Q(
                    date_created__gte=last_month_details["start_date"],
                    date_created__lt=last_month_details["end_date"] + timedelta(days=1),
                    transaction_from=transaction_type
                ))
            )
            
            # Replace None with 0
            return {k: v or 0 for k, v in result.items()}
        
        # 2. Get counts for sales transactions in a single query
        sales_counts = AgentWalletTransaction.objects.filter(
            agent_phone_number=agent_phone,
            transaction_from="GAME_PLAY"
        ).aggregate(
            today_count=Count('id', filter=Q(date_created__date=TODAY.date())),
            this_week_count=Count('id', filter=Q(
                date_created__date__range=[date_ranges["week_start"], date_ranges["week_end"]]
            )),
            this_month_count=Count('id', filter=Q(date_created__gte=start_of_month))
        )
        
        # 3. Get counts for winnings in a single query
        winnings_counts = PosLotteryWinners.objects.filter(
            agent=agent_instance
        ).aggregate(
            today_count=Count('id', filter=Q(date_created__date=TODAY.date())),
            this_week_count=Count('id', filter=Q(
                date_created__date__range=[date_ranges["week_start"], date_ranges["week_end"]]
            )),
            this_month_count=Count('id', filter=Q(date_created__gte=start_of_month))
        )
        
        # 4. Get super agent commissions in a single query
        super_agent_commission_sums = LottoSuperAgentWalletTransaction.objects.filter(
            wallet__super_agent__phone=agent_phone, 
            transaction_from="COMMISSION", 
            transactton_type="CREDIT"
        ).aggregate(
            today_sum=Sum('amount', filter=Q(created_at__date=TODAY.date())),
            yesterday_sum=Sum('amount', filter=Q(created_at__date=YESTERDAY.date())),
            this_week_sum=Sum('amount', filter=Q(
                created_at__date__range=[date_ranges["week_start"], date_ranges["week_end"]]
            )),
            last_week_sum=Sum('amount', filter=Q(
                created_at__date__range=[date_ranges["last_week_start"], date_ranges["last_week_end"]]
            )),
            this_month_sum=Sum('amount', filter=Q(created_at__gte=start_of_month)),
            last_month_sum=Sum('amount', filter=Q(
                created_at__gte=last_month_details["start_date"],
                created_at__lt=last_month_details["end_date"] + timedelta(days=1)
            ))
        )
        super_agent_commission_sums = {k: v or 0 for k, v in super_agent_commission_sums.items()}
        
        # 5. Get sales and commission data using the helper function
        sales_sums = get_transaction_sums("GAME_PLAY")
        commission_sums = get_transaction_sums("COMMISSION_ON_GAME_PLAY")
        
        # 6. Calculate sales metrics with pre-fetched data
        sales_value_metrics = {
            "sales_today": sales_sums["today_sum"],
            "today_sales_percentage_change": calculate_percentage_change_in_value(
                sales_sums["today_sum"], sales_sums["yesterday_sum"]
            ).get("percentage_change"),
            "today_sales_percentage_change_type": calculate_percentage_change_in_value(
                sales_sums["today_sum"], sales_sums["yesterday_sum"]
            ).get("change_type"),
            "sales_this_week": sales_sums["this_week_sum"],
            "this_week_sales_percentage_change": calculate_percentage_change_in_value(
                sales_sums["this_week_sum"], sales_sums["last_week_sum"]
            ).get("percentage_change"),
            "this_week_sales_percentage_change_type": calculate_percentage_change_in_value(
                sales_sums["this_week_sum"], sales_sums["last_week_sum"]
            ).get("change_type"),
            "sales_this_month": sales_sums["this_month_sum"],
            "this_month_sales_percentage_change": calculate_percentage_change_in_value(
                sales_sums["this_month_sum"], sales_sums["last_month_sum"]
            ).get("percentage_change"),
            "this_month_sales_percentage_change_type": calculate_percentage_change_in_value(
                sales_sums["this_month_sum"], sales_sums["last_month_sum"]
            ).get("change_type"),
        }
        
        # 7. Calculate commission metrics with combined data
        _commission_earned_today = commission_sums["today_sum"] + super_agent_commission_sums["today_sum"]
        _commission_earned_yesterday = commission_sums["yesterday_sum"] + super_agent_commission_sums["yesterday_sum"]
        _commission_earned_this_week = commission_sums["this_week_sum"] + super_agent_commission_sums["this_week_sum"]
        _commission_earned_last_week = commission_sums["last_week_sum"] + super_agent_commission_sums["last_week_sum"]
        _commission_earned_month = commission_sums["this_month_sum"] + super_agent_commission_sums["this_month_sum"]
        _commission_earned_last_month = commission_sums["last_month_sum"] + super_agent_commission_sums["last_month_sum"]
        
        commission_value_metrics = {
            "commission_today": _commission_earned_today,
            "today_commission_percentage_change": calculate_percentage_change_in_value(
                _commission_earned_today, _commission_earned_yesterday
            ).get("percentage_change"),
            "today_commission_percentage_change_type": calculate_percentage_change_in_value(
                _commission_earned_today, _commission_earned_yesterday
            ).get("change_type"),
            "commission_this_week": _commission_earned_this_week,
            "this_week_commission_percentage_change": calculate_percentage_change_in_value(
                _commission_earned_this_week, _commission_earned_last_week
            ).get("percentage_change"),
            "this_week_commission_percentage_change_type": calculate_percentage_change_in_value(
                _commission_earned_this_week, _commission_earned_last_week
            ).get("change_type"),
            "commission_this_month": _commission_earned_month,
            "this_month_commission_percentage_change": calculate_percentage_change_in_value(
                _commission_earned_month, _commission_earned_last_month
            ).get("percentage_change"),
            "this_month_commission_percentage_change_type": calculate_percentage_change_in_value(
                _commission_earned_month, _commission_earned_last_month
            ).get("change_type"),
        }
        
        # 8. Calculate ratios directly using the counts
        def calculate_ratio(sales_count, winnings_count):
            if sales_count == 0:
                return 0
            ratio = (winnings_count / sales_count) * 100
            # Handle infinite or NaN values
            if math.isnan(ratio) or math.isinf(ratio):
                return 0 if ratio < 0 else 100 if ratio > 0 else 0
            return ratio
        
        sales_to_winning_ratio = {
            "count_of_sales_today": sales_counts["today_count"] or 0,
            "ratio_to_winning_today": calculate_ratio(
                sales_counts["today_count"] or 0, 
                winnings_counts["today_count"] or 0
            ),
            "count_of_sales_this_week": sales_counts["this_week_count"] or 0,
            "ratio_to_winning_this_week": calculate_ratio(
                sales_counts["this_week_count"] or 0,
                winnings_counts["this_week_count"] or 0
            ),
            "count_of_sales_this_month": sales_counts["this_month_count"] or 0,
            "ratio_to_winning_this_month": calculate_ratio(
                sales_counts["this_month_count"] or 0,
                winnings_counts["this_month_count"] or 0
            ),
        }


        sales_value_metrics = json.dumps(sales_value_metrics).replace("Infinity", "1e100").replace("-Infinity", "-1e100")
        sales_value_metrics = json.loads(sales_value_metrics)

        commission_value_metrics = json.dumps(commission_value_metrics).replace("Infinity", "1e100").replace("-Infinity", "-1e100")
        commission_value_metrics = json.loads(commission_value_metrics)

        sales_to_winning_ratio = json.dumps(sales_to_winning_ratio).replace("Infinity", "1e100").replace("-Infinity", "-1e100")
        sales_to_winning_ratio = json.loads(sales_to_winning_ratio)
        
        data = {
            "sales_value_metrics": sales_value_metrics,
            "commission_value_metrics": commission_value_metrics,
            "sales_to_winning_ratio": sales_to_winning_ratio,
        }
        
        
        data = json.dumps(data, cls=CustomJSONEncoder)

        print(f"""
                data: {data}
                \n\n
            """)

        if isinstance(data, str):
            return json.loads(data)


        # data = {"sales_value_metrics": {"sales_today": 3600.0, "today_sales_percentage_change": 300.0, "today_sales_percentage_change_type": 1, "sales_this_week": 4500.0, "this_week_sales_percentage_change": -96.79886181753513, "this_week_sales_percentage_change_type": -1, "sales_this_month": 145075.0, "this_month_sales_percentage_change": Infinity, "this_month_sales_percentage_change_type": 1}, "commission_value_metrics": {"commission_today": 540.0, "today_commission_percentage_change": 200.0, "today_commission_percentage_change_type": 1, "commission_this_week": 720.0, "this_week_commission_percentage_change": -98.18026727324424, "this_week_commission_percentage_change_type": -1, "commission_this_month": 40286.25, "this_month_commission_percentage_change": Infinity, "this_month_commission_percentage_change_type": 1}, "sales_to_winning_ratio": {"count_of_sales_today": 4, "ratio_to_winning_today": 50.0, "count_of_sales_this_week": 5, "ratio_to_winning_this_week": 40.0, "count_of_sales_this_month": 43, "ratio_to_winning_this_month": 51.162790697674424}}
        
        return data


def _handle_withdraw_for_failed_transactions(pos_lottery_winner: PosLotteryWinners, agent_instance: Agent):
    """
    Handle the withdrawal process for failed transactions.

    This method handles the withdrawal process for failed transactions.
    It refunds the agent's winnings wallet and updates the transaction status.

    Args:
        pos_lottery_winner (PosLotteryWinners): The lottery winner instance whose transaction failed.
        agent_instance (Agent): The agent instance whose transaction failed.

    Returns:
        tuple: A tuple containing a boolean indicating the success of the operation and a message.
            - (bool): True if the operation was successful, False otherwise.
            - (str): A message indicating the result of the operation.
    """

    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

    # check if the game withdrawal was successful,
    payout_transaction_instance = PayoutTransactionTable.objects.filter(game_play_id=pos_lottery_winner.game_id).last()
    if payout_transaction_instance is not None:
        if payout_transaction_instance.disbursed:
            pos_lottery_winner.claimant = agent_instance
            pos_lottery_winner.is_win_claimed = True
            pos_lottery_winner.payout_successful = True
            pos_lottery_winner.payout_verified = True
            pos_lottery_winner.save()
            return False, "ticket already claimed."
        
        if payout_transaction_instance.is_verified is False:
            return False, "payout verification is still pending. please wait."

    # un assigned game id from the payout record
    unique_game_play_id_payout_request = PayoutTransactionTable.objects.filter(unique_game_play_id=pos_lottery_winner.game_id).last()
    if unique_game_play_id_payout_request:
        unique_game_play_id_payout_request.unique_game_play_id = None
        unique_game_play_id_payout_request.save()

    agent_wallet = AgentWallet.objects.filter(agent=agent_instance).last()

    if agent_wallet is None:
        return False, "You have no withdrawal wallet"

    amount = pos_lottery_winner.amount_won

    # create a payout process logs
    was_successful = PayoutProcessFirstStep.created_record(
        game_play_id=pos_lottery_winner.game_id,
        amount=amount,
        user_phone_number=agent_instance.phone,
        user_name=f"{agent_instance.first_name} {agent_instance.last_name}",
        payout_referece=payout_reference,
    )

    if was_successful is False:
        return False, "We're sorry, you're making too many requests. Please try again later"

    if FloatWallet.get_float_wallet(source="RETAIL_RTP_WALLET").amount < amount:
        return False, "We're sorry, an error occured while processing your request. Please try again later"

    if agent_wallet.winnings_bal < amount:

        # fund agent wallet
        payload = {
            "transaction_from": "PAYOUT_REVERSAL",
            "game_type": pos_lottery_winner.lottery_type,
            "game_play_id": pos_lottery_winner.game_id,
        }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent_instance.phone,
            amount=amount,
            channel="POS/MOBILE",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="CREDIT",
        )

        UserWallet.fund_wallet(
            user=agent_instance,
            amount=amount,
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **payload,
        )

        agent_wallet.refresh_from_db()

        # return False, "Insufficient balance"

    if retail_has_enough_money_to_give_out(amount) is False:
        return False, "We're sorry, an error occured while processing your request. Please try again later"

    game_withdrawal_record = PayoutTransactionTable.objects.filter(game_play_id=pos_lottery_winner.game_id).last()
    if game_withdrawal_record != None:  # noqa
        if game_withdrawal_record.disbursed is True:
            pos_lottery_winner.claimant = agent_instance
            pos_lottery_winner.is_win_claimed = True
            pos_lottery_winner.payout_successful = True
            pos_lottery_winner.save()

            return False, "ticket already claimed."

        else:
            # validate the time is more than 10 minutes
            timezone = pytz.timezone(settings.TIME_ZONE)
            time_diff = datetime.now(tz=timezone) - game_withdrawal_record.date_added
            if time_diff.total_seconds() < 600:
                return False, "You can only re-claim your winnings after 10 minutes"

    debit_credit_record = DebitCreditRecord.create_record(
        phone_number=agent_wallet.agent.phone,
        amount=amount,
        channel="POS",
        reference=payout_reference,
        transaction_type="DEBIT",
    )

    wallet_payload = {
        "transaction_from": "WINNINGS_WITHDRAW",
        "game_type": pos_lottery_winner.lottery_type,
        "game_play_id": pos_lottery_winner.game_id,
    }

    UserWallet.deduct_wallet(
        user=agent_wallet.agent,
        amount=int(amount),
        channel="POS",
        transaction_id=debit_credit_record.reference,
        user_wallet_type="WINNINGS_WALLET",
        **wallet_payload,
    )

    # remove the money from general withdrawable wallet
    # GeneralWithdrawableWallet.deduct_fund(amount=amount, phone=agent_instance.phone)

    Wallet.debit_wallet(
        wallet_type="RETAIL_RTP_WALLET",
        amount=amount,
        game_type=pos_lottery_winner.lottery_type,
        user_phone=agent_instance.phone,
        user_name=f"{agent_instance.first_name} {agent_instance.last_name}",
        game_play_id=pos_lottery_winner.game_id,
    )

    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": agent_instance.phone,
                "amount": amount,
                "narration": "LOTTO_WINNING_WITHDRAWAL",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    agent_wallet.refresh_from_db()

    pos_lottery_winner.payout_ref = payout_reference
    pos_lottery_winner.save()

    _withdraw_table_instance = PayoutTransactionTable.objects.create(
        source="BUDDY",
        amount=amount,
        disbursement_unique_id=payout_reference,
        phone=agent_instance.phone,
        payout_trans_ref=payout_reference,
        channel="POS",
        game_play_id=pos_lottery_winner.game_id,
        payout_payload=payload,
        unique_game_play_id=pos_lottery_winner.game_id,
        date_won=pos_lottery_winner.date_created,
        balance_before=agent_wallet.winnings_bal + amount,
        balance_after=agent_wallet.winnings_bal,
        joined_since=agent_instance.get_duration(),
        name=f"{agent_instance.first_name} {agent_instance.last_name}",
    )

    cp_withdraw_table_instance = _withdraw_table_instance

    cp_payload = payload

    pos_lottery_winner.is_win_claimed = True
    pos_lottery_winner.withdrawl_initiated = True
    pos_lottery_winner.claimant = agent_instance
    pos_lottery_winner.save()

    pos_agent_helper = PosAgentHelper(agent_instance=agent_instance, amount=amount)

    response = pos_agent_helper.agency_buddy_transfer(**cp_payload)
    cp_response = response

    if isinstance(cp_response, dict):

        if cp_response.get("message") == "success":
            cp_withdraw_table_instance.disbursed = True
            cp_withdraw_table_instance.is_verified = True
            cp_withdraw_table_instance.save()

            pos_lottery_winner.payout_successful = True
            pos_lottery_winner.payout_verified = True
            pos_lottery_winner.save()
        else:
            raise ValidationError(response)
    else:
        raise ValidationError(response)

    _withdraw_table_instance.source_response_payload = cp_response
    _withdraw_table_instance.save()

    data = {
        "status": "success",
        "message": "Withdrawal initiated",
        "transaction_ref": payout_reference,
        "agent_id": f"winwise-{agent_instance.id}",
        "account_name": f"{agent_instance.first_name} {agent_instance.last_name}",
        "bank_name": "Liberty Pay vfd",
        "amount": amount,
        "reference": cp_withdraw_table_instance.payout_trans_ref,
        "date": datetime.now(),
        "data": None,
    }

    return True, data


def get_date_ranges() -> Dict[str, Union[datetime.date, str]]:
    """
    Calculate relevant date ranges for reporting periods.

    Returns:
        Dictionary containing today, yesterday, week start/end dates, and formatted week date range
    """
    today = datetime.now().date()
    yesterday = today - timedelta(days=1)

    # Calculate week start/end dates
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)

    current_week_start = today - timedelta(days=today.weekday())
    last_week_end = current_week_start - timedelta(days=1)
    last_week_start = last_week_end - timedelta(days=6)

    return {
        "today": today,
        "yesterday": yesterday,
        "week_start": week_start,
        "week_end": week_end,
        "week_date_range": f"{week_start} - {week_end}",
        "last_week_start": last_week_start,
        "last_week_end": last_week_end,
    }


def get_last_month_details():
    """
    Calculate details for the last month.

    Returns:
    Dictionary with start date, end date, month, year, and number of days in the month.
    """
    # Get current date
    today = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

    # First, get the first day of the current month
    first_day_current_month = today.replace(day=1)

    # Subtract one day to get the last day of the previous month
    last_day_of_last_month = first_day_current_month - timedelta(days=1)

    # Get the first day of last month
    first_day_of_last_month = last_day_of_last_month.replace(day=1)

    # Get the number of days in last month
    _, days_in_last_month = calendar.monthrange(last_day_of_last_month.year, last_day_of_last_month.month)

    return {
        "start_date": first_day_of_last_month,
        "end_date": last_day_of_last_month,
        "month": last_day_of_last_month.month,
        "year": last_day_of_last_month.year,
        "days_in_month": days_in_last_month,
    }


def calculate_percentage_change_in_value(current_value, previous_value):
    """
    Calculate the percentage change between two values.


    Returns:
    dict: A dictionary containing calculation details
    """
    # Calculate total amounts

    # Handle division by zero
    if previous_value == 0:
        if current_value > 0:
            percentage_change = float("inf")  # Indicates significant increase
        elif current_value == 0:
            percentage_change = 0  # No change
        else:
            percentage_change = float("-inf")  # Indicates significant decrease
    else:
        # Calculate percentage change
        percentage_change = ((current_value - previous_value) / previous_value) * 100

    # Determine change type
    if percentage_change > 0:
        change_type = 1
    elif percentage_change < 0:
        change_type = -1
    else:
        change_type = 0

    print(
        f"""
        percentage_change {percentage_change}
        change_type: {change_type}
            """
    )

    return {
        # 'current_value': current_value,
        # 'previous_value': previous_value,
        "percentage_change": float(percentage_change),
        "change_type": change_type,
    }


def calculate_sales_to_winnings_ratio(sales_queryset, winnings_queryset):
    """
    Calculate the ratio of sales (game plays) to winnings.

    Args:
    sales_queryset (QuerySet): Queryset of sales/game plays
    winnings_queryset (QuerySet): Queryset of winnings

    Returns:
    dict: A dictionary with detailed ratio information
    """
    # Count of sales (game plays)
    sales_count = sales_queryset.count()

    # Count of winnings
    winnings_count = winnings_queryset.count()

    # Calculate ratio
    if sales_count > 0:
        ratio_to_winnings = winnings_count / sales_count
    else:
        ratio_to_winnings = 0

    # Calculate total amounts (optional, but often useful)
    total_sales_amount = sales_queryset.aggregate(total=Sum("amount"))["total"] or 0
    total_winnings_amount = winnings_queryset.aggregate(total=Sum("amount_won"))["total"] or 0

    return {
        "count_of_sales_today": sales_count,
        "count_of_winnings_today": winnings_count,
        "ratio_to_winnings": ratio_to_winnings,
        "ratio_percentage": ratio_to_winnings * 100,
        "total_sales_amount": total_sales_amount,
        "total_winnings_amount": total_winnings_amount,
        "win_to_sales_percentage": (total_winnings_amount / total_sales_amount * 100) if total_sales_amount > 0 else 0,
    }


def safe_float_conversion(value):
    """
    Convert value to a JSON-safe float representation.

    Args:
    value: Input value to be converted

    Returns:
    float or str: JSON-safe representation of the value
    """
    try:
        # If it's already a number, convert to float
        float_value = float(str(value).replace(",", ""))

        # Handle infinity cases
        if float_value == float("inf"):
            return "inf"
        elif float_value == float("-inf"):
            return "-inf"

        return float_value
    except (ValueError, TypeError):
        # If conversion fails, return 0.0
        return 0.0


def create_safe_metrics_dict(metrics_dict):
    """
    Create a JSON-safe version of the metrics dictionary.

    Args:
    metrics_dict (dict): Original metrics dictionary

    Returns:
    dict: JSON-safe metrics dictionary
    """
    # Deep copy to avoid modifying original
    # safe_metrics = {}

    # for main_key, main_value in metrics_dict.items():
    #     safe_metrics[main_key] = {}

    #     for sub_key, sub_value in main_value.items():
    #         # Special handling for percentage change and type
    #         if "percentage_change" in sub_key:
    #             safe_metrics[main_key][sub_key] = safe_float_conversion(sub_value)
    #         elif "percentage_change_type" in sub_key:
    #             safe_metrics[main_key][sub_key] = sub_value
    #         else:
    #             # Remove commas and convert to float or keep as is
    #             safe_metrics[main_key][sub_key] = safe_float_conversion(sub_value)

    return metrics_dict
