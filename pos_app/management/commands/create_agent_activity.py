from django.core.management.base import BaseCommand

from pos_app.models import Agent, DailyInactiveAgents, LottoAgentSalesActivity
from pos_app.pos_helpers import get_week_info
from datetime import datetime
import pytz
from django.conf import settings



class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        agents = Agent.objects.filter(terminal_retrieved=False, terminal_id__isnull=False)
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        
        for agent in agents:
            week_num, formatted_week_range = get_week_info(TODAY)

            week_date_range = f"Week {week_num}: {formatted_week_range}"

            try:
                LottoAgentSalesActivity.objects.get(agent_phone_number=agent.phone, week_date_range=week_date_range)
            except LottoAgentSalesActivity.DoesNotExist: 
                LottoAgentSalesActivity.objects.create(
                    agent_name=f"{agent.first_name} - {agent.last_name}",
                    agent_email=agent.email,
                    agent_phone_number=agent.phone,
                    sales_for_the_week=0,
                    winnings=0,
                    week_date_range=week_date_range,
                    agent_terminal_id=agent.terminal_id,
                    activity_status = "ABSENT"
                )
            except LottoAgentSalesActivity.MultipleObjectsReturned:
                pass
            except Exception:
                pass



            try:
                DailyInactiveAgents.objects.get(
                    agent_phone_number = agent.phone,
                    created_at__date = TODAY.date()
                )
            except:
                DailyInactiveAgents.objects.create(
                    agent_name = f"{agent.first_name} {agent.last_name}",
                    agent_email = agent.email,
                    agent_phone_number = agent.phone,
                    agent_terminal_id = agent.terminal_id,
                )
        

                

            










        