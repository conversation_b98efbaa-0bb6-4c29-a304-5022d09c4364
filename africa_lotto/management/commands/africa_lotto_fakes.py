from django.core.management.base import BaseCommand

from account.models import User
from django.contrib.auth.hashers import make_password
from datetime import datetime, timedelta

from africa_lotto.helpers import get_valid_until_date_for_kenya_30_games, scrape_ghana_draw_numbers
from africa_lotto.models import AfricaLotteryType, AfricaLotto, AfricaLottoBatch, AfricaLottoDrawNumbers
from africa_lotto.socket_utils import live_k_now_draw_number_socket_update
from africa_lotto.tasks import celery_africa_lotto_kenya_draw, celery_filter_africa_lotto_game_winners
import random


def create_fake_africa_lotto_ticket():
    """
    Create fake Africa Lotto tickets for testing purposes.
    """
    
    game_types = ["KENYA_LOTTO", "KENYA_30_LOTTO", "K_NOW"]

    for game in game_types:
        # create 5 tickets for each game type
        for i in range(5):
            batch_name = None

            if game == "KENYA_LOTTO":
                batch_name = random.choices(["Tokyo","Ultimate", "Angelina Special", "Supreme Max", "Barnabas"])
                batch_name = batch_name[0]


            batch = AfricaLottoBatch.objects.create(
                game_type = game,
                batch_name = batch_name,
                batch_status = False
            )

            # create 5 random numbers
            ticket = random.sample(range(1, 91), 5)
            ticket = ",".join([str(num) for num in ticket])

            machine_number = random.sample(range(1, 91), 5)
            machine_number = ",".join([str(num) for num in machine_number])

            AfricaLottoDrawNumbers.objects.create(
                draw_number = ticket,
                game_type = game,
                machine_number = machine_number,
                batch_id = batch.batch_uuid,
            )
        

class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # scrape_ghana_draw_numbers()

        # res = get_valid_until_date_for_kenya_30_games()
        # # res = res.strftime("%I:%M %p")
        # print(res)


        # celery_africa_lotto_kenya_draw(create_new_batch=False, batch_db_id = 571)

        # draw_numbers_queryset = AfricaLottoDrawNumbers.objects.all()
        # for instance in draw_numbers_queryset:
        #     batch_id = ""
        #     # randome 5 numbers
        #     draw_numbers = []
        #     while len(draw_numbers) < 5:
        #         num = random.randint(1, 90)
        #         if num not in draw_numbers:
        #             draw_numbers.append(num)
        #             batch_id = str(num) + batch_id
            


            
        #     # random 2 numbers
            

        #     draw_numbers = ",".join([str(num) for num in draw_numbers])
        #     instance.draw_number = draw_numbers
        #     instance.batch_id = batch_id
        #     instance.machine_number = draw_numbers
        #     instance.save()

        # data = {
        #     "draw_number": "35,11,44,5,10",
        #     "machine_number": "35,11,44,5,10",
        #     "batch_number": "KW78883455"
        # }

        # live_k_now_draw_number_socket_update("KW78883455", data)

        create_fake_africa_lotto_ticket()




