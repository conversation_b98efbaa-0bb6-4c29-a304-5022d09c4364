from django.core.management.base import BaseCommand
from django.core.management.base import BaseCommand
from africa_lotto.tasks import celery_africa_lotto_kenya_draw
from datetime import datetime
import pytz
from django.conf import settings


class Command(BaseCommand):
    help = "Check if main draw time has reached and run the draw"
    
    def handle(self, *args, **kwargs):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_time = datetime.now(tz=timezone).strftime("%I:%M %p")
        draw_times = ["09:01 AM", "12:01 PM", "03:01 PM", "06:01 PM", "09:01 PM"]
        
        if current_time in draw_times:
            # Run the main draw logic here
            celery_africa_lotto_kenya_draw()
            self.stdout.write(self.style.SUCCESS(f"Running main draw for {current_time}"))
        else:
            self.stdout.write(self.style.WARNING("Not a valid draw time."))