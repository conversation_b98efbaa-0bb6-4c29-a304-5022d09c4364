from django.contrib import admin, messages
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from africa_lotto.models import (
    AfricaLotto,
    AfricaLottoBatch,
    AfricaLottoConstants,
    AfricaLottoDrawData,
    AfricaLottoDrawDataWinners,
    AfricaLottoDrawNumbers,
    AfricaLottoGameType,
    AfricaLottoGames,
)
from africa_lotto.tasks import filter_africa_lotto_winnders_data, celery_africa_lotto_kenya_draw, process_winner_data
from main.models import UserProfile
from pos_app.models import Agent, PosLotteryWinners
import json 
import ast

# Register your models here.
class AfricaLottoResource(resources.ModelResource):
    class Meta:
        model = AfricaLotto


class AfricaLottoBatchResource(resources.ModelResource):
    class Meta:
        model = AfricaLottoBatch


class AfricaLottoConstantsResource(resources.ModelResource):
    class Meta:
        model = AfricaLottoConstants


class AfricaLottoDrawNumbersResource(resources.ModelResource):
    class Meta:
        model = AfricaLottoDrawNumbers


class AfricaLottoDrawDataResource(resources.ModelResource):
    class Meta:
        model = AfricaLottoDrawData


class AfricaLottoDrawDataWinnersResource(resources.ModelResource):
    class Meta:
        model = AfricaLottoDrawDataWinners

class AfricaLottoGamesResource(resources.ModelResource):
    class Meta:
        model = AfricaLottoGames


class AfricaLottoResourceAdmin(ImportExportModelAdmin):
    resource_class = AfricaLottoResource
    search_fields = [
        "batch__batch_uuid",
        "game_play_id",
    ]
    list_filter = ["created_at", "won", "exempted_from_draw", "lottery_type", "game_type", "paid", "double_chance", "channel"]
    date_hierarchy = "created_at"
    raw_id_fields = [
        "batch",
    ]
    autocomplete_fields = ["batch"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AfricaLottoBatchResourceAdmin(ImportExportModelAdmin):
    resource_class = AfricaLottoBatchResource
    search_fields = [
        "batch_uuid",
        "batch_name",
        "game_type"
    ]
    list_filter = ["game_type", "batch_status", "created_at", "draw_date"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    actions = ["manuallyrundraw"]

    @admin.action(description="MANUALLY RUN DRAW")
    def manuallyrundraw(modeladmin, request, queryset):
        for obj in queryset:
            if obj.game_type == AfricaLottoGameType.GHANA_LOTTO:
                messages.error(request, f"This game type is not supported for manual draw! - {obj.batch_uuid}")
                continue

            if obj.batch_status is True:
                messages.error(request, f"This batch is still active! - {obj.batch_uuid}")
                continue

            # check if this batch already have draw numbers
            if AfricaLottoDrawNumbers.objects.filter(batch_id=obj.batch_uuid).exists():
                messages.error(request, f"This batch already has draw numbers! - {obj.batch_uuid}")
                continue

            celery_africa_lotto_kenya_draw(game_type=obj.game_type, batch_db_id=obj.id, create_new_batch=False)
            
            day_of_week = obj.created_at.strftime("%A")
            draw_date = f"{day_of_week} {obj.created_at.strftime('%Y-%m-%d')}{obj.next_draw_time}"

            AfricaLottoDrawNumbers.objects.filter(batch_id=obj.batch_uuid).update(
                draw_date=draw_date,
            )

            obj.draw_status = True
            obj.batch_status = False
            obj.save()
            messages.success(request, f"Successfully drawn ghana lotto! - {obj.batch_uuid}")



class AfricaLottoConstantsResourceAdmin(ImportExportModelAdmin):
    resource_class = AfricaLottoConstantsResource

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AfricaLottoDrawNumbersResourceAdmin(ImportExportModelAdmin):
    resource_class = AfricaLottoDrawNumbersResource

    autocomplete_fields = ["game_batch"]

    list_filter = ["game_type", "batch_drawn", "created_at"]
    
    search_fields = ["batch_id",]

    date_hierarchy = "created_at"

    raw_id_fields = ["game_batch"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    actions = [
        "filterwinners"
    ]



    @admin.action(description="FILTER WINNERS")
    def filterwinners(modeladmin, request, queryset):
        for obj in queryset:

            if obj.manually_filtered_winnings is True:
                messages.error(request, f"This batch has already been filtered! - {obj.game_batch.batch_uuid}")
                continue

            

            if not obj.game_batch:
                messages.error(request, f"This batch has no batch id! - {obj.game_batch.batch_uuid}")
                continue


            obj.manually_filtered_winnings = True
            obj.save()

            filter_africa_lotto_winnders_data(
                draw_db_id=obj.id,
                batch_id=obj.game_batch.id,
                game_type = obj.game_type,
            )

    

class AfricaLottoDrawDataResourceAdmin(ImportExportModelAdmin):
    resource_class = AfricaLottoDrawDataResource

    date_hierarchy = "created_at"
    list_filter = ["game_type", "created_at"]

    search_fields = ["batch"]

    def get_list_display(self, request):
        datas = [field.name for field in self.model._meta.concrete_fields]
        datas.remove("game_plays")
        return datas
    

    actions = [
        "processwinners",
    ]

    @admin.action(description="MANUALLY PROCESS WINNERS DATA")
    def processwinners(modeladmin, request, queryset):
        for obj in queryset:
            if obj.manually_processed_winners is True:
                messages.error(request, f"This batch has already been processed! - {obj.batch.batch_uuid}")
                continue

            obj.manually_processed_winners = True
            obj.save()
            
            if obj.game_type in ["KENYA_LOTTO", "KENYA_30_LOTTO"]:
                try:
                    serialized_data = json.loads(obj.draw_response)
                except json.JSONDecodeError as e:
                    try:
                        serialized_data = ast.literal_eval(obj.draw_response)
                    except (SyntaxError, ValueError) as e:
                        messages.error(request, f"Error decoding JSON data: {e}")
                        continue

                if isinstance(serialized_data, dict):
                    process_winner_data(serialized_data["winners"], draw_data_model_instance = obj)
            else:
                messages.error(request, f"Game type {obj.game_type} is not supported for winner processing.")
                continue


class AfricaLottoDrawDataWinnersResourceAdmin(ImportExportModelAdmin):
    resource_class = AfricaLottoDrawDataWinnersResource

    date_hierarchy = "created_at"

    search_fields = ["user_phone_number", "agent_phone_number", "batch", "game_play_id"]

    list_filter = ["created_at", "channel", "lottery_type", "game_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        
        # Check if user is a superuser
        if not request.user.is_superuser:
            import datetime
            today = datetime.date.today()
            first_day_of_month = today.replace(day=1)
            
            queryset = queryset.filter(created_at__gte=first_day_of_month)
            
        return queryset
    

    actions = [
        "manuallycreatewinningrecord",
    ]

    @admin.action(description="MANUALLY CREATE WINNING RECORD ON POS LOTTERY TABLE")
    def manuallycreatewinningrecord(modeladmin, request, queryset):
        for obj in queryset:
            try:
                agent_profile_instance = Agent.objects.get(phone=obj.agent_phone_number)
                user_profile = UserProfile.objects.get(phone_number=obj.agent_phone_number)

                game_play_id = str(obj.game_play_id).split("&")[0]
                pin = str(obj.game_play_id).split("&")[1]

                existing_instance = PosLotteryWinners.objects.filter(
                    agent=agent_profile_instance,
                    player=user_profile,
                    game_id=game_play_id,
                    lottery_type=obj.game_type,
                    pin=pin,
                ).first()
                if existing_instance:
                    messages.success(request, "record already exists")
                    return

                # Create a new winner record

                

                PosLotteryWinners().create_winners(
                    agent=agent_profile_instance,
                    player=user_profile,
                    game_id=game_play_id,
                    amount_won=obj.amount_won,
                    win_flavour="WHITE",
                    lottery_type=obj.game_type,
                    pin=pin,
                )

                messages.success(request, "Successfully created winning record on POS lottery table")

            except (Agent.DoesNotExist, UserProfile.DoesNotExist) as e:
                messages.success(request, f"Error handling POS agent winners: {e}")
                


class AfricaLottoGamesResourceAdmin(ImportExportModelAdmin):
    resource_class = AfricaLottoGamesResource

    date_hierarchy = "created_at"


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    


admin.site.register(AfricaLotto, AfricaLottoResourceAdmin)
admin.site.register(AfricaLottoBatch, AfricaLottoBatchResourceAdmin)
admin.site.register(AfricaLottoConstants, AfricaLottoConstantsResourceAdmin)
admin.site.register(AfricaLottoDrawNumbers, AfricaLottoDrawNumbersResourceAdmin)
admin.site.register(AfricaLottoDrawData, AfricaLottoDrawDataResourceAdmin)
admin.site.register(AfricaLottoDrawDataWinners, AfricaLottoDrawDataWinnersResourceAdmin)
admin.site.register(AfricaLottoGames, AfricaLottoGamesResourceAdmin)
