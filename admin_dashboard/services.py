import calendar
from datetime import datetime
from math import floor
import datetime as dt
from django.db.models import Avg, Count, F, Sum, Q, Q, Case, When, Value, FloatField, IntegerField, ExpressionWrapper
from django.db.models.functions import Coalesce

from admin_dashboard.helpers.helpers import (
    Paginator,
    date_utility,
    get_percentage_change,
)
from admin_dashboard.models import ConstantTable
from awoof_app.models import AwoofDrawTable, AwoofGameTable
from main.models import (
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    PaymentTransaction,
    PayoutTransactionTable,
)
from pos_app.models import (
    Agent,
    AgentFundingTable,
    AgentWallet,
    AgentWalletTransaction,
    FailedRemittanceAgencyWalletCharge,
    LottoAgentRemittanceTable,
    PosLotteryWinners,
    Supervisor,
    SupervisorLocation,
)
from sms_campaign.models import SmsChargeWallet
from sport_app.models import <PERSON><PERSON>ashWinner
from wyse_ussd.models import SoccerPrediction


class DateUtility:
    def __init__(self):
        # Date utilities
        filter_date = date_utility(datetime=datetime)
        self.previous_year_current_month_start = filter_date.get("previous_year_current_month_start")
        self.previous_year_current_month_end = filter_date.get("previous_year_current_month_end")
        self.previous_year_current_previous_month = filter_date.get("previous_year_current_previous_month")
        self.previous_year_current_following_month = filter_date.get("previous_year_current_following_month")
        self.today = filter_date.get("today")
        self.previous_day = filter_date.get("previous_day")
        self.week_start = filter_date.get("week_start")
        self.month_start = filter_date.get("month_start")
        self.year_start = filter_date.get("year_start")
        self.previous_year_start = filter_date.get("previous_year_start")
        self.previous_year_end = filter_date.get("previous_year_end")
        self.previous_month_start = filter_date.get("previous_month_start")
        self.previous_month_end = filter_date.get("previous_month_end")
        self.date_today = filter_date.get("date_today")
        self.date_today_date = filter_date.get("date_today_date")
        self.start_of_all_transactions = filter_date.get("start_of_all_transactions")
        self.previous_week_start = filter_date.get("previous_week_start")
        self.previous_week_end = filter_date.get("previous_week_end")

        # Create an array of the month keys
        self.months_list1 = list(
            range(
                self.previous_year_current_month_start.month,
                (self.previous_year_current_month_start.month + 13 - self.previous_year_current_month_start.month),
            )
        )

        self.months_list2 = list(range(1, 12 - (12 - self.previous_year_current_month_start.month) + 1))
        self.months_list_names = [f"{calendar.month_name[month][:3]} {self.previous_year_current_month_start.year}" for month in self.months_list1]
        self.months_list2_names = [f"{calendar.month_name[month][:3]} {self.today.year}" for month in self.months_list2]
        self.months_list_names = self.months_list_names + self.months_list2_names
        self.months_list = self.months_list1 + self.months_list2
        self.year_months_tuple_list = [(self.previous_year_current_month_start.year, month) for month in self.months_list1] + [
            (self.today.year, month) for month in self.months_list2
        ]

    def get_filter(self, request):
        filter_value = request.GET.get("filter")

        if filter_value is not None:
            if filter_value == "today":
                self.date_filter = {"date_created__date": self.date_today_date}
                self.date_filter_two = {"date__date": self.date_today_date}
                self.date_filter_three = {"created_at__date": self.date_today_date}
                self.date_filter_four = {"created_date__date": self.date_today_date}
                self.date_filter_five = {"date_won__date": self.date_today_date}

            elif filter_value == "this_week":
                self.date_filter = {"date_created__gte": self.week_start}
                self.date_filter_two = {"date__gte": self.week_start}
                self.date_filter_three = {"created_at__gte": self.week_start}
                self.date_filter_four = {"created_date__gte": self.week_start}
                self.date_filter_five = {"date_won__gte": self.week_start}

            elif filter_value == "last_week":
                self.date_filter = {"date_created__gte": self.previous_week_start, "date_created__lte": self.previous_week_end}
                self.date_filter_two = {"date__gte": self.previous_week_start, "date__lte": self.previous_week_end}
                self.date_filter_three = {"created_at__gte": self.previous_week_start, "created_at__lte": self.previous_week_end}
                self.date_filter_four = {"created_date__gte": self.previous_week_start, "created_date__lte": self.previous_week_end}
                self.date_filter_five = {"date_won__gte": self.previous_week_start, "date_won__lte": self.previous_week_end}

            elif filter_value == "this_month":
                self.date_filter = {"date_created__gte": self.month_start}
                self.date_filter_two = {"date__gte": self.month_start}
                self.date_filter_three = {"created_at__gte": self.month_start}
                self.date_filter_four = {"created_date__gte": self.month_start}
                self.date_filter_five = {"date_won__gte": self.month_start}

            elif filter_value == "last_month":
                self.date_filter = {"date_created__gte": self.previous_month_start, "date_created__lte": self.previous_month_end}
                self.date_filter_two = {"date__gte": self.previous_month_start, "date__lte": self.previous_month_end}
                self.date_filter_three = {"created_at__gte": self.previous_month_start, "created_at__lte": self.previous_month_end}
                self.date_filter_four = {"created_date__gte": self.previous_month_start, "created_date__lte": self.previous_month_end}
                self.date_filter_five = {"date_won__gte": self.previous_month_start, "date_won__lte": self.previous_month_end}

            elif filter_value == "this_year":
                self.date_filter = {"date_created__gte": self.year_start}
                self.date_filter_two = {"date__gte": self.year_start}
                self.date_filter_three = {"created_at__gte": self.year_start}
                self.date_filter_four = {"created_date__gte": self.year_start}
                self.date_filter_five = {"date_won__gte": self.year_start}

            elif filter_value == "last_year":
                self.date_filter = {"date_created__gte": self.previous_year_start, "date_created__lte": self.previous_year_end}
                self.date_filter_two = {"date__gte": self.previous_year_start, "date__lte": self.previous_year_end}
                self.date_filter_three = {"created_at__gte": self.previous_year_start, "created_at__lte": self.previous_year_end}
                self.date_filter_four = {"created_date__gte": self.previous_year_start, "created_date__lte": self.previous_year_end}
                self.date_filter_five = {"date_won__gte": self.previous_year_start, "date_won__lte": self.previous_year_end}

            else:
                self.date_filter = {"date_created__gte": self.start_of_all_transactions}
                self.date_filter_two = {"date__gte": self.start_of_all_transactions}
                self.date_filter_three = {"created_at__gte": self.start_of_all_transactions}
                self.date_filter_four = {"created_date__gte": self.start_of_all_transactions}
                self.date_filter_five = {"date_won__gte": self.start_of_all_transactions}

        else:
            self.date_filter = {"date_created__gte": self.start_of_all_transactions}
            self.date_filter_two = {"date__gte": self.start_of_all_transactions}
            self.date_filter_three = {"created_at__gte": self.start_of_all_transactions}
            self.date_filter_four = {"created_date__gte": self.start_of_all_transactions}
            self.date_filter_five = {"date_won__gte": self.start_of_all_transactions}

        return {
            "date_filter": self.date_filter,
            "date_filter_two": self.date_filter_two,
            "date_filter_three": self.date_filter_three,
            "date_filter_four": self.date_filter_four,
            "date_filter_five": self.date_filter_five,
        }


class MainDashboard:
    def __init__(self, request):
        self.date_filter = DateUtility().get_filter(request).get("date_filter")
        self.date_filter_two = DateUtility().get_filter(request).get("date_filter_two")
        self.date_filter_three = DateUtility().get_filter(request).get("date_filter_three")
        self.date_filter_four = DateUtility().get_filter(request).get("date_filter_four")
        self.date_filter_five = DateUtility().get_filter(request).get("date_filter_five")

        date_today_date = DateUtility().date_today_date
        week_start = DateUtility().week_start
        month_start = DateUtility().month_start
        previous_month_start = DateUtility().previous_month_start
        previous_month_end = DateUtility().previous_month_end
        year_start = DateUtility().year_start
        previous_year_start = DateUtility().previous_year_start
        previous_year_end = DateUtility().previous_year_end

        agents_remittance_queryset = LottoAgentRemittanceTable.objects.all()
        agent_wallet_qs = AgentWallet.objects.all()

        aggregates = agent_wallet_qs.aggregate(
            total_game_play=Coalesce(Sum("game_play_bal"), Value(0.00), output_field=FloatField()),
            total_winnings=Coalesce(Sum("winnings_bal"), Value(0.00), output_field=FloatField()),
            winwise_game=Coalesce(Sum("game_play_bal", filter=Q(agent__is_winwise_staff_agent=True)), Value(0.00), output_field=FloatField()),
            winwise_winnings=Coalesce(Sum("winnings_bal", filter=Q(agent__is_winwise_staff_agent=True)), Value(0.00), output_field=FloatField()),
            other_game=Coalesce(Sum("game_play_bal", filter=Q(agent__is_winwise_staff_agent=False)), Value(0.00), output_field=FloatField()),
            other_winnings=Coalesce(Sum("winnings_bal", filter=Q(agent__is_winwise_staff_agent=False)), Value(0.00), output_field=FloatField()),
        )

        # Aggregate for overall wallet balance
        # self.overall_game_play_wallet_balance = list(agent_wallet_qs.aggregate(Sum("game_play_bal")).values())[0]
        self.overall_game_play_wallet_balance = aggregates["total_game_play"]
        # self.overall_winnings_wallet_balance = list(agent_wallet_qs.aggregate(Sum("winnings_bal")).values())[0]
        self.overall_winnings_wallet_balance = aggregates["total_winnings"]
        self.overall_wallet_balance = (self.overall_game_play_wallet_balance) + (self.overall_winnings_wallet_balance)
        # Aggregate of wallet balance for WinWise agents
        # self.winwise_agents_game_play_wallet_balance = list(
        #     agent_wallet_qs.filter(agent__full_name__icontains="Wise").aggregate(Sum("game_play_bal")).values()
        # )[0]
        self.winwise_agents_game_play_wallet_balance = aggregates["winwise_game"]
        # self.winwise_agents_winnings_wallet_balance = list(
        #     # agent_wallet_qs.filter(agent__full_name__icontains="Wise").aggregate(Sum("winnings_bal")).values()
        # )[0]
        self.winwise_agents_winnings_wallet_balance = aggregates["winwise_winnings"]

        self.winwise_agents_overall_wallet_balance = self.winwise_agents_game_play_wallet_balance + self.winwise_agents_winnings_wallet_balance

        # Aggregate of wallet balance for Other lotto agents
        # self.other_lotto_agents_winnings_wallet_balance = list(
        #     # agent_wallet_qs.exclude(agent__full_name__icontains="Wise").aggregate(Sum("winnings_bal")).values()
        #     agent_wallet_qs.exclude(agent__is_winwise_staff_agent=True).aggregate(Sum("winnings_bal")).values()
        # )[0]
        self.other_lotto_agents_winnings_wallet_balance = aggregates["other_winnings"]

        # self.other_lotto_agents_game_play_wallet_balance = list(
        #     # agent_wallet_qs.exclude(agent__full_name__icontains="Wise").aggregate(Sum("game_play_bal")).values()
        #     agent_wallet_qs.exclude(agent__is_winwise_staff_agent=True).aggregate(Sum("game_play_bal")).values()
        # )[0]
        self.other_lotto_agents_game_play_wallet_balance = aggregates["other_game"]

        self.other_lotto_agents_overall_wallet_balance = self.other_lotto_agents_game_play_wallet_balance + self.other_lotto_agents_winnings_wallet_balance

        # Wallet balance percentages
        self.winwise_agents_percentage = (
            (self.winwise_agents_overall_wallet_balance or 0)
            / (self.overall_wallet_balance or 1) * 100
        )

        self.other_lotto_agents_percentage = (
            (self.other_lotto_agents_overall_wallet_balance or 0)
            / (self.overall_wallet_balance or 1) * 100
        )

        # Game Play Balance Percentages
        self.winwise_agents_game_play_percentage = (
            (self.winwise_agents_game_play_wallet_balance or 0)
            / (self.overall_game_play_wallet_balance or 1)
            * 100
        )

        self.other_lotto_agents_game_play_percentage = (
            (self.other_lotto_agents_game_play_wallet_balance or 0)
            / (self.other_lotto_agents_game_play_wallet_balance or 1)
            * 100
        )

        # Winnings Balance Percentages
        self.winwise_agents_winnings_percentage = (
            (self.winwise_agents_winnings_wallet_balance or 0)
            / (self.overall_winnings_wallet_balance or 1)
            * 100
        )

        self.other_lotto_agents_winnings_percentage = (
            (self.other_lotto_agents_winnings_wallet_balance or 0)
            / (self.overall_winnings_wallet_balance or 1)
            * 100
        )

        # Payouts ----> pending payouts (is_disbursed)
        # Winnings Withdraw
        payouts_qs = PayoutTransactionTable.objects.all().filter(
            payment_initiated=True, disbursed=True, is_verified=True
        )  # sometimes status remains as pending even when successful/add pending --- pos/online

        payouts_qs_aggreg = payouts_qs.aggregate(
            today=Coalesce(Sum("amount", filter=Q(date_added__date=date_today_date)), Value(0.00), output_field=FloatField()),
            this_week=Coalesce(Sum("amount", filter=Q(date_added__gte=week_start)), Value(0.00), output_field=FloatField()),
            this_month=Coalesce(Sum("amount", filter=Q(date_added__gte=month_start)), Value(0.00), output_field=FloatField()),
            last_month=Coalesce(Sum("amount", filter=Q(date_added__gte=previous_month_start, date_added__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            this_year=Coalesce(Sum("amount", filter=Q(date_added__gte=year_start)), Value(0.00), output_field=FloatField()),
            last_year=Coalesce(Sum("amount", filter=Q(date_added__gte=previous_year_start, date_added__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
        )

        # self.payouts_amount_today = list(payouts_qs.filter(date_added__date=date_today_date).aggregate(Sum("amount")).values())[0]
        self.payouts_amount_today = payouts_qs_aggreg["today"]
        # self.payouts_amount_week = list(payouts_qs.filter(date_added__gte=week_start).aggregate(Sum("amount")).values())[0]
        self.payouts_amount_week = payouts_qs_aggreg["this_week"]
        # self.payouts_amount_month = list(payouts_qs.filter(date_added__gte=month_start).aggregate(Sum("amount")).values())[0]
        self.payouts_amount_month = payouts_qs_aggreg["this_month"]
        # self.payouts_amount_last_month = list(
        #     payouts_qs.filter(date_added__gte=previous_month_start, date_added__lte=previous_month_end).aggregate(Sum("amount")).values()
        # )[0]
        self.payouts_amount_last_month = payouts_qs_aggreg["last_month"]
        # self.payouts_amount_year = list(payouts_qs.filter(date_added__gte=year_start).aggregate(Sum("amount")).values())[0]
        self.payouts_amount_year = payouts_qs_aggreg["this_year"]
        # self.payouts_amount_last_year = list(
        #     payouts_qs.filter(date_added__gte=previous_year_start, date_added__lte=previous_year_end).aggregate(Sum("amount")).values()
        # )[0]
        self.payouts_amount_last_year = payouts_qs_aggreg["last_year"]

        # POS Payout
        payouts_pos_qs = payouts_qs.filter(channel="POS")
        payouts_pos_aggreg = payouts_pos_qs.aggregate(
            today=Coalesce(Sum("amount", filter=Q(date_added__date=date_today_date)), Value(0.00), output_field=FloatField()),
            this_week=Coalesce(Sum("amount", filter=Q(date_added__gte=week_start)), Value(0.00), output_field=FloatField()),
            this_month=Coalesce(Sum("amount", filter=Q(date_added__gte=month_start)), Value(0.00), output_field=FloatField()),
            last_month=Coalesce(Sum("amount", filter=Q(date_added__gte=previous_month_start, date_added__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            this_year=Coalesce(Sum("amount", filter=Q(date_added__gte=year_start)), Value(0.00), output_field=FloatField()),
            last_year=Coalesce(Sum("amount", filter=Q(date_added__gte=previous_year_start, date_added__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
        )
        
        # self.payouts_pos_amount_today = list(payouts_pos_qs.filter(date_added__date=date_today_date).aggregate(Sum("amount")).values())[0]
        self.payouts_pos_amount_today = payouts_pos_aggreg["today"]
        # self.payouts_pos_amount_week = list(payouts_pos_qs.filter(date_added__gte=week_start).aggregate(Sum("amount")).values())[0]
        self.payouts_pos_amount_week = payouts_pos_aggreg["this_week"]
        # self.payouts_pos_amount_month = list(payouts_pos_qs.filter(date_added__gte=month_start).aggregate(Sum("amount")).values())[0]
        self.payouts_pos_amount_month = payouts_pos_aggreg["this_month"]
        # self.payouts_pos_amount_last_month = list(
        #     payouts_pos_qs.filter(date_added__gte=previous_month_start, date_added__lte=previous_month_end).aggregate(Sum("amount")).values()
        # )[0]
        self.payouts_pos_amount_last_month = payouts_pos_aggreg["last_month"]
        # self.payouts_pos_amount_year = list(payouts_pos_qs.filter(date_added__gte=year_start).aggregate(Sum("amount")).values())[0]
        self.payouts_pos_amount_year = payouts_pos_aggreg["this_year"]
        # self.payouts_pos_amount_last_year = list(
        #     payouts_pos_qs.filter(date_added__gte=previous_year_start, date_added__lte=previous_year_end).aggregate(Sum("amount")).values()
        # )[0]
        self.payouts_pos_amount_last_year = payouts_pos_aggreg["last_year"]

        # WEB Payout
        payouts_web_qs = payouts_qs.filter(channel="WEB")
        payouts_web_aggreg = payouts_web_qs.aggregate(
            today=Coalesce(Sum("amount", filter=Q(date_added__date=date_today_date)), Value(0.00), output_field=FloatField()),
            this_week=Coalesce(Sum("amount", filter=Q(date_added__gte=week_start)), Value(0.00), output_field=FloatField()),
            this_month=Coalesce(Sum("amount", filter=Q(date_added__gte=month_start)), Value(0.00), output_field=FloatField()),
            last_month=Coalesce(Sum("amount", filter=Q(date_added__gte=previous_month_start, date_added__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            this_year=Coalesce(Sum("amount", filter=Q(date_added__gte=year_start)), Value(0.00), output_field=FloatField()),
            last_year=Coalesce(Sum("amount", filter=Q(date_added__gte=previous_year_start, date_added__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
        )


        # self.payouts_web_amount_today = list(payouts_web_qs.filter(date_added__date=date_today_date).aggregate(Sum("amount")).values())[0]
        self.payouts_web_amount_today = payouts_web_aggreg["today"]
        # self.payouts_web_amount_week = list(payouts_web_qs.filter(date_added__gte=week_start).aggregate(Sum("amount")).values())[0]
        self.payouts_web_amount_week = payouts_web_aggreg["this_week"]
        # self.payouts_web_amount_month = list(payouts_web_qs.filter(date_added__gte=month_start).aggregate(Sum("amount")).values())[0]
        self.payouts_web_amount_month = payouts_web_aggreg["this_month"]
        # self.payouts_web_amount_last_month = list(
        #     payouts_web_qs.filter(date_added__gte=previous_month_start, date_added__lte=previous_month_end).aggregate(Sum("amount")).values()
        # )[0]
        self.payouts_web_amount_last_month = payouts_web_aggreg["last_month"]
        # self.payouts_web_amount_year = list(payouts_web_qs.filter(date_added__gte=year_start).aggregate(Sum("amount")).values())[0]
        self.payouts_web_amount_year = payouts_web_aggreg["this_year"]
        # self.payouts_web_amount_last_year = list(
        #     payouts_web_qs.filter(date_added__gte=previous_year_start, date_added__lte=previous_year_end).aggregate(Sum("amount")).values()
        # )[0]
        self.payouts_web_amount_last_year = payouts_web_aggreg["last_year"]


        # USSD Payout
        payouts_ussd_qs = payouts_qs.filter(channel="USSD")
        payouts_ussd_aggreg = payouts_ussd_qs.aggregate(
            today=Coalesce(Sum("amount", filter=Q(date_added__date=date_today_date)), Value(0.00), output_field=FloatField()),
            this_week=Coalesce(Sum("amount", filter=Q(date_added__gte=week_start)), Value(0.00), output_field=FloatField()),
            this_month=Coalesce(Sum("amount", filter=Q(date_added__gte=month_start)), Value(0.00), output_field=FloatField()),
            last_month=Coalesce(Sum("amount", filter=Q(date_added__gte=previous_month_start, date_added__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            this_year=Coalesce(Sum("amount", filter=Q(date_added__gte=year_start)), Value(0.00), output_field=FloatField()),
            last_year=Coalesce(Sum("amount", filter=Q(date_added__gte=previous_year_start, date_added__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
        )

        # self.payouts_ussd_amount_today = list(payouts_ussd_qs.filter(date_added__date=date_today_date).aggregate(Sum("amount")).values())[0]
        self.payouts_ussd_amount_today = payouts_ussd_aggreg["today"]
        # self.payouts_ussd_amount_week = list(payouts_ussd_qs.filter(date_added__gte=week_start).aggregate(Sum("amount")).values())[0]
        self.payouts_ussd_amount_week = payouts_ussd_aggreg["this_week"]
        # self.payouts_ussd_amount_month = list(payouts_ussd_qs.filter(date_added__gte=month_start).aggregate(Sum("amount")).values())[0]
        self.payouts_ussd_amount_month = payouts_ussd_aggreg["this_month"]
        # self.payouts_ussd_amount_last_month = list(
        #     payouts_ussd_qs.filter(date_added__gte=previous_month_start, date_added__lte=previous_month_end).aggregate(Sum("amount")).values()
        # )[0]
        self.payouts_ussd_amount_last_month = payouts_ussd_aggreg["last_month"]
        # self.payouts_ussd_amount_year = list(payouts_ussd_qs.filter(date_added__gte=year_start).aggregate(Sum("amount")).values())[0]
        self.payouts_ussd_amount_year = payouts_ussd_aggreg["this_year"]
        # self.payouts_ussd_amount_last_year = list(
        #     payouts_ussd_qs.filter(date_added__gte=previous_year_start, date_added__lte=previous_year_end).aggregate(Sum("amount")).values()
        # )[0]
        self.payouts_ussd_amount_last_year = payouts_ussd_aggreg["last_year"]

        # Terminals
        terminals_qs = Agent.objects.all().filter(agent_type="LOTTO_AGENT", terminal_id__isnull=False, terminal_retrieved=False)
        # that have terminal_id

        counts_aggregate = terminals_qs.aggregate(
            total = Count("id"),
            previous_month_total_terminals_count = Count("id", filter=Q(created_date__lte=previous_month_end)),
            winwise_agents_terminals_count = Count("id", filter=Q(is_winwise_staff_agent=True)),
            other_lotto_agents_terminals_count = Count("id", filter=Q(is_winwise_staff_agent=False)),
        ) 
        
        # self.total_terminals_count = terminals_qs.count()
        self.total_terminals_count = counts_aggregate["total"]
        # self.previous_month_total_terminals_count = terminals_qs.filter(created_date__lte=previous_month_end).count()
        self.previous_month_total_terminals_count = counts_aggregate["previous_month_total_terminals_count"]
        # self.winwise_agents_terminals_count = terminals_qs.filter(is_winwise_staff_agent=True).count()
        self.winwise_agents_terminals_count = counts_aggregate["winwise_agents_terminals_count"]
        # self.other_lotto_agents_terminals_count = terminals_qs.exclude(is_winwise_staff_agent=False).count()
        self.other_lotto_agents_terminals_count = counts_aggregate["other_lotto_agents_terminals_count"]

        # Terminals Percentage
        self.winwise_agents_terminals_count_percentage = (
            self.winwise_agents_terminals_count / (self.total_terminals_count or 1) * 100
        )

        self.other_lotto_agents_terminals_percentage = (
            self.other_lotto_agents_terminals_count / (self.total_terminals_count or 1) * 100
        )

        # Tickets Sold ===> POS
        tickets_sold_qs = AgentWalletTransaction.objects.filter(status="SUCCESSFUL", transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"], agent_wallet__agent__supervisor__user_id=request.user.id)

        # Tickets Sold Amount ====> POS
        
        tickets_sold_amount_aggregates = tickets_sold_qs.aggregate(
            today=Coalesce(Sum("amount", filter=Q(date_created__date=date_today_date)), Value(0.00), output_field=FloatField()),
            this_week=Coalesce(Sum("amount", filter=Q(date_created__gte=week_start)), Value(0.00), output_field=FloatField()),
            this_month=Coalesce(Sum("amount", filter=Q(date_created__gte=month_start)), Value(0.00), output_field=FloatField()),
            last_month=Coalesce(Sum("amount", filter=Q(date_created__gte=previous_month_start, date_created__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            this_year=Coalesce(Sum("amount", filter=Q(date_created__gte=year_start)), Value(0.00), output_field=FloatField()),
            last_year=Coalesce(Sum("amount", filter=Q(date_created__gte=previous_year_start, date_created__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
        ) 

        # self.ticket_sold_amount_today = list(tickets_sold_qs.filter(date_created__date=date_today_date).aggregate(Sum("amount")).values())[0]
        self.ticket_sold_amount_today = tickets_sold_amount_aggregates["today"]
        # self.ticket_sold_amount_week = list(tickets_sold_qs.filter(date_created__gte=week_start).aggregate(Sum("amount")).values())[0]
        self.ticket_sold_amount_week = tickets_sold_amount_aggregates["this_week"]
        # self.ticket_sold_amount_month = list(tickets_sold_qs.filter(date_created__gte=month_start).aggregate(Sum("amount")).values())[0]
        self.ticket_sold_amount_month = tickets_sold_amount_aggregates["this_month"]
        # self.ticket_sold_amount_year = list(tickets_sold_qs.filter(date_created__gte=year_start).aggregate(Sum("amount")).values())[0]
        self.ticket_sold_amount_year = tickets_sold_amount_aggregates["this_year"]
        # self.ticket_sold_amount_last_year = list(
        #     tickets_sold_qs.filter(date_created__gte=previous_year_start, date_created__lte=previous_year_end).aggregate(Sum("amount")).values()
        # )[0]
        self.ticket_sold_amount_last_year = tickets_sold_amount_aggregates["last_year"]
        # self.ticket_sold_amount_last_month = list(
        #     tickets_sold_qs.filter(date_created__gte=previous_month_start, date_created__lte=previous_month_end).aggregate(Sum("amount")).values()
        # )[0]
        self.ticket_sold_amount_last_month = tickets_sold_amount_aggregates["last_month"]

        # Tickets Sold Count ==> POS
        pos_aggregates = tickets_sold_qs.aggregate(
            today=Coalesce(Count("amount", filter=Q(date_created__date=date_today_date)), Value(0.00), output_field=FloatField()),
            this_week=Coalesce(Count("amount", filter=Q(date_created__gte=week_start)), Value(0.00), output_field=FloatField()),
            this_month=Coalesce(Count("amount", filter=Q(date_created__gte=month_start)), Value(0.00), output_field=FloatField()),
            last_month=Coalesce(Count("amount", filter=Q(date_created__gte=previous_month_start, date_created__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            this_year=Coalesce(Count("amount", filter=Q(date_created__gte=year_start)), Value(0.00), output_field=FloatField()),
            last_year=Coalesce(Count("amount", filter=Q(date_created__gte=previous_year_start, date_created=previous_year_end)), Value(0.00), output_field=FloatField()),
        )


        
        # self.ticket_sold_count_today = tickets_sold_qs.filter(date_created__date=date_today_date).count()
        self.ticket_sold_count_today = pos_aggregates["today"]
        # self.ticket_sold_count_week = tickets_sold_qs.filter(date_created__gte=week_start).count()
        self.ticket_sold_count_week = pos_aggregates["this_week"]
        # self.ticket_sold_count_month = tickets_sold_qs.filter(date_created__gte=month_start).count()
        self.ticket_sold_count_month = pos_aggregates["this_month"]
        # self.ticket_sold_count_year = tickets_sold_qs.filter(date_created__gte=year_start).count()
        self.ticket_sold_count_year = pos_aggregates["this_year"]
        # self.ticket_sold_count_last_year = tickets_sold_qs.filter(date_created__gte=previous_year_start, date_created__lte=previous_year_end).count()
        self.ticket_sold_count_last_year = pos_aggregates["last_year"]
        # self.ticket_sold_count_last_month = tickets_sold_qs.filter(
        #     date_created__gte=previous_month_start, date_created__lte=previous_month_end
        # ).count()
        self.ticket_sold_count_last_month = pos_aggregates["last_month"]
        # self.ticket_sold_count_this_month = tickets_sold_qs.filter(date_created__gte=month_start).count()
        self.ticket_sold_count_this_month = pos_aggregates["this_month"]

        # Tickets Sold Web Qs
        game_play_tickets_web_qs = LottoTicket.objects.all().filter(paid=True).filter(channel="WEB")
        wyse_cash_game_play_tickets_web_qs = LotteryModel.objects.all().filter(paid=True).filter(channel="WEB")
        soccer_cash_game_play_web_qs = SoccerPrediction.objects.all().filter(paid=True).filter(channel="WEB")

        salary_4_life_instant_cashout_web_qs = game_play_tickets_web_qs.filter(lottery_type__in=["SALARY_FOR_LIFE", "INSTANT_CASHOUT"])
        wysecash_web_qs = wyse_cash_game_play_tickets_web_qs.filter(lottery_type="WYSE_CASH")
        soccercash_web_qs = soccer_cash_game_play_web_qs.filter(lottery_type="SOCCER_CASH")
        banker_game_play_web_qs = game_play_tickets_web_qs.filter(lottery_type="BANKER")
        quika_game_play_web_qs = game_play_tickets_web_qs.filter(lottery_type="QUIKA")

        quika_game_play_web_qs_aggreg = quika_game_play_web_qs.aggregate(
            quika_web_ticket_sold_amount = Coalesce(Sum("amount_paid"), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_amount_today = Coalesce(Sum("amount_paid", filter=Q(date__gte=date_today_date)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_amount_week = Coalesce(Sum("amount_paid", filter=Q(date__gte=week_start)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_amount_month = Coalesce(Sum("amount_paid", filter=Q(date__gte=month_start)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_amount_last_month = Coalesce(Sum("amount_paid", filter=Q(date__gte=previous_month_start, date__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_amount_year = Coalesce(Sum("amount_paid", filter=Q(date__gte=year_start)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_amount_last_year = Coalesce(Sum("amount_paid", filter=Q(date__gte=previous_year_start, date__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_count = Coalesce(Count("amount_paid"), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_count_today = Coalesce(Count("amount_paid", filter=Q(date__gte=date_today_date)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_count_week = Coalesce(Count("amount_paid", filter=Q(date__gte=week_start)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_count_month = Coalesce(Count("amount_paid", filter=Q(date__gte=month_start)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_count_last_month = Coalesce(Count("amount_paid", filter=Q(date__gte=previous_month_start, date__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_count_year = Coalesce(Count("amount_paid", filter=Q(date__gte=year_start)), Value(0.00), output_field=FloatField()),
            quika_web_ticket_sold_count_last_year = Coalesce(Count("amount_paid", filter=Q(date__gte=previous_year_start, date__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
        )


        banker_game_play_web_qs_aggreg = banker_game_play_web_qs.aggregate(
            banker_web_ticket_sold_amount = Coalesce(Sum("amount_paid"), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_amount_today = Coalesce(Sum("amount_paid", filter=Q(date__gte=date_today_date)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_amount_week = Coalesce(Sum("amount_paid", filter=Q(date__gte=week_start)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_amount_month = Coalesce(Sum("amount_paid", filter=Q(date__gte=month_start)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_amount_last_month = Coalesce(Sum("amount_paid", filter=Q(date__gte=previous_month_start, date__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_amount_year = Coalesce(Sum("amount_paid", filter=Q(date__gte=year_start)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_amount_last_year = Coalesce(Sum("amount_paid", filter=Q(date__gte=previous_year_start, date__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_count = Coalesce(Count("amount_paid"), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_count_today = Coalesce(Count("amount_paid", filter=Q(date__gte=date_today_date)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_count_week = Coalesce(Count("amount_paid", filter=Q(date__gte=week_start)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_count_month = Coalesce(Count("amount_paid", filter=Q(date__gte=month_start)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_count_last_month = Coalesce(Count("amount_paid", filter=Q(date__gte=previous_month_start, date__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_count_year = Coalesce(Count("amount_paid", filter=Q(date__gte=year_start)), Value(0.00), output_field=FloatField()),
            banker_web_ticket_sold_count_last_year = Coalesce(Count("amount_paid", filter=Q(date__gte=previous_year_start, date__lte=previous_year_start)), Value(0.00), output_field=FloatField()),
        )

        
        # banker_game_play_web_qs = quika_game_play_web_qs_aggreg["banker_game_play_web_qs"]
        # quika_game_play_web_qs = game_play_tickets_web_qs.filter(lottery_type="QUIKA")
        # quika_game_play_web_qs = quika_game_play_web_qs_aggreg["quika_game_play_web_qs"]

        # Tickets Sold USSD Qs
        game_play_tickets_ussd_qs = LottoTicket.objects.all().filter(paid=True).filter(channel="USSD")
        wyse_cash_game_play_tickets_ussd_qs = LotteryModel.objects.all().filter(paid=True).filter(channel="USSD")
        soccer_cash_game_play_ussd_qs = SoccerPrediction.objects.all().filter(paid=True).filter(channel="USSD")

        salary_4_life_instant_cashout_ussd_qs = game_play_tickets_ussd_qs.filter(lottery_type__in=["SALARY_FOR_LIFE", "INSTANT_CASHOUT"])
        wysecash_ussd_qs = wyse_cash_game_play_tickets_ussd_qs.filter(lottery_type="WYSE_CASH")
        soccercash_ussd_qs = soccer_cash_game_play_ussd_qs.filter(lottery_type="SOCCER_CASH")
        banker_game_play_ussd_qs = game_play_tickets_ussd_qs.filter(lottery_type="BANKER")
        quika_game_play_ussd_qs = game_play_tickets_ussd_qs.filter(lottery_type="QUIKA")

        banker_ussd_ticket_aggregate = banker_game_play_ussd_qs.aggregate(
            banker_ussd_ticket_sold_count = Coalesce(Count("amount_paid"), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_count_today = Coalesce(Count("amount_paid", filter=Q(date__gte=date_today_date)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_count_week = Coalesce(Count("amount_paid", filter=Q(date__gte=week_start)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_count_month = Coalesce(Count("amount_paid", filter=Q(date__gte=month_start)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_count_last_month = Coalesce(Count("amount_paid", filter=Q(date__gte=previous_month_start, date__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_count_year = Coalesce(Count("amount_paid", filter=Q(date__gte=year_start)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_count_last_year = Coalesce(Count("amount_paid", filter=Q(date__gte=previous_year_start, date__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_amount = Coalesce(Sum("amount_paid"), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_amount_today = Coalesce(Sum("amount_paid", filter=Q(date__gte=date_today_date)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_amount_week = Coalesce(Sum("amount_paid", filter=Q(date__gte=week_start)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_amount_month = Coalesce(Sum("amount_paid", filter=Q(date__gte=month_start)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_amount_last_month = Coalesce(Sum("amount_paid", filter=Q(date__gte=previous_month_start, date__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_amount_year = Coalesce(Sum("amount_paid", filter=Q(date__gte=year_start)), Value(0.00), output_field=FloatField()),
            banker_ussd_ticket_sold_amount_last_year = Coalesce(Sum("amount_paid", filter=Q(date__gte=previous_year_start, date__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
        )

        quika_ussd_ticket_aggregate = quika_game_play_ussd_qs.aggregate(
            quika_ussd_ticket_sold_count = Coalesce(Count("amount_paid"), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_count_today = Coalesce(Count("amount_paid", filter=Q(date__gte=date_today_date)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_count_week = Coalesce(Count("amount_paid", filter=Q(date__gte=week_start)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_count_month = Coalesce(Count("amount_paid", filter=Q(date__gte=month_start)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_count_last_month = Coalesce(Count("amount_paid", filter=Q(date__gte=previous_month_start, date__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_count_year = Coalesce(Count("amount_paid", filter=Q(date__gte=year_start)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_count_last_year = Coalesce(Count("amount_paid", filter=Q(date__gte=previous_year_start, date__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_amount = Coalesce(Sum("amount_paid"), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_amount_today = Coalesce(Sum("amount_paid", filter=Q(date__gte=date_today_date)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_amount_week = Coalesce(Sum("amount_paid", filter=Q(date__gte=week_start)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_amount_month = Coalesce(Sum("amount_paid", filter=Q(date__gte=month_start)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_amount_last_month = Coalesce(Sum("amount_paid", filter=Q(date__gte=previous_month_start, date__lte=previous_month_end)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_amount_year = Coalesce(Sum("amount_paid", filter=Q(date__gte=year_start)), Value(0.00), output_field=FloatField()),
            quika_ussd_ticket_sold_amount_last_year = Coalesce(Sum("amount_paid", filter=Q(date__gte=previous_year_start, date__lte=previous_year_end)), Value(0.00), output_field=FloatField()),
        )

        # banker_game_play_ussd_qs = game_play_tickets_ussd_qs.filter(lottery_type="BANKER")
        # banker_game_play_ussd_qs = banker_aggregate["banker_game_play_ussd_qs"]
        # quika_game_play_ussd_qs = game_play_tickets_ussd_qs.filter(lottery_type="QUIKA")
        # quika_game_play_ussd_qs = banker_aggregate["quika_game_play_ussd_qs"]

        # Tickets Sold Amount ====> WEB
        self.salary_4_life_instant_cashout_web_ticket_sold_amount = list(salary_4_life_instant_cashout_web_qs.aggregate(Sum("amount_paid")).values())[
            0
        ]
        self.wyse_cash_web_ticket_sold_amount = list(wysecash_web_qs.aggregate(Sum("amount_paid")).values())[0]
        self.soccercash_web_ticket_sold_amount = list(soccercash_web_qs.aggregate(Sum("amount_paid")).values())[0]
        # self.banker_web_ticket_sold_amount = list(banker_game_play_web_qs.aggregate(Sum("amount_paid")).values())[0]
        self.banker_web_ticket_sold_amount = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_amount"]
        # self.quika_web_ticket_sold_amount = list(quika_game_play_web_qs.aggregate(Sum("amount_paid")).values())[0]
        self.quika_web_ticket_sold_amount = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_amount"]

        # WEB
        self.total_tickets_sold_web_amount = (
            (self.salary_4_life_instant_cashout_web_ticket_sold_amount or 0.00) +
            (self.wyse_cash_web_ticket_sold_amount or 0.00) +
            (self.soccercash_web_ticket_sold_amount or 0.00) +
            (self.banker_web_ticket_sold_amount or 0.00) +
            (self.quika_web_ticket_sold_amount or 0.00)
        )

        # Today
        self.salary_4_life_instant_cashout_web_ticket_sold_amount_today = list(
            salary_4_life_instant_cashout_web_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values()
        )[0]
        self.wyse_cash_web_ticket_sold_amount_today = list(wysecash_web_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values())[
            0
        ]
        self.soccercash_web_ticket_sold_amount_today = list(
            soccercash_web_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values()
        )[0]
        # self.banker_web_ticket_sold_amount_today = list(
        #     banker_game_play_web_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.banker_web_ticket_sold_amount_today = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_amount_today"]
        # self.quika_web_ticket_sold_amount_today = list(
        #     quika_game_play_web_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.quika_web_ticket_sold_amount_today = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_amount_today"]

        self.total_tickets_sold_web_amount_today = (
            (self.salary_4_life_instant_cashout_web_ticket_sold_amount_today or 0.00) +
            (self.wyse_cash_web_ticket_sold_amount_today or 0.00) +
            (self.soccercash_web_ticket_sold_amount_today or 0.00) +
            (self.banker_web_ticket_sold_amount_today or 0.00) +
            (self.quika_web_ticket_sold_amount_today or 0.00)
        )

        # This Week
        self.salary_4_life_instant_cashout_web_ticket_sold_amount_week = list(
            salary_4_life_instant_cashout_web_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values()
        )[0]
        self.wyse_cash_web_ticket_sold_amount_week = list(wysecash_web_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values())[0]
        self.soccercash_web_ticket_sold_amount_week = list(soccercash_web_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values())[0]
        # self.banker_web_ticket_sold_amount_week = list(banker_game_play_web_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values())[0]
        self.banker_web_ticket_sold_amount_week = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_amount_week"]
        # self.quika_web_ticket_sold_amount_week = list(quika_game_play_web_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values())[0]
        self.quika_web_ticket_sold_amount_week = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_amount_week"]

        self.total_tickets_sold_web_amount_week = (
            (self.salary_4_life_instant_cashout_web_ticket_sold_amount_week or 0.00) +
            (self.wyse_cash_web_ticket_sold_amount_week or 0.00) +
            (self.soccercash_web_ticket_sold_amount_week or 0.00) +
            (self.banker_web_ticket_sold_amount_week or 0.00) +
            (self.quika_web_ticket_sold_amount_week or 0.00)
        )

        # This Month
        self.salary_4_life_instant_cashout_web_ticket_sold_amount_month = list(
            salary_4_life_instant_cashout_web_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values()
        )[0]
        self.wyse_cash_web_ticket_sold_amount_month = list(wysecash_web_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values())[0]
        self.soccercash_web_ticket_sold_amount_month = list(soccercash_web_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values())[0]
        # self.banker_web_ticket_sold_amount_month = list(banker_game_play_web_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values())[
        #     0
        # ]
        self.banker_web_ticket_sold_amount_month = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_amount_month"]
        # self.quika_web_ticket_sold_amount_month = list(quika_game_play_web_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values())[0]
        self.quika_web_ticket_sold_amount_month = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_amount_month"]

        self.total_tickets_sold_web_amount_month = (
            (self.salary_4_life_instant_cashout_web_ticket_sold_amount_month or 0.00) +
            (self.wyse_cash_web_ticket_sold_amount_month or 0.00) +
            (self.soccercash_web_ticket_sold_amount_month or 0.00) +
            (self.banker_web_ticket_sold_amount_month or 0.00) +
            (self.quika_web_ticket_sold_amount_month or 0.00)
        )

        # Last Month
        self.salary_4_life_instant_cashout_web_ticket_sold_amount_last_month = list(
            salary_4_life_instant_cashout_web_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end)
            .aggregate(Sum("amount_paid"))
            .values()
        )[0]
        self.wyse_cash_web_ticket_sold_amount_last_month = list(
            wysecash_web_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end).aggregate(Sum("amount_paid")).values()
        )[0]
        self.soccercash_web_ticket_sold_amount_last_month = list(
            soccercash_web_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end).aggregate(Sum("amount_paid")).values()
        )[0]
        # self.banker_web_ticket_sold_amount_last_month = list(
        #     banker_game_play_web_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.banker_web_ticket_sold_amount_last_month = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_amount_last_month"]
        # self.quika_web_ticket_sold_amount_last_month = list(
        #     quika_game_play_web_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.quika_web_ticket_sold_amount_last_month = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_amount_last_month"]

        self.total_tickets_sold_web_amount_last_month = (
            (self.salary_4_life_instant_cashout_web_ticket_sold_amount_last_month or 0.00) +
            (self.wyse_cash_web_ticket_sold_amount_last_month or 0.00) +
            (self.soccercash_web_ticket_sold_amount_last_month or 0.00) +
            (self.banker_web_ticket_sold_amount_last_month or 0.00) +
            (self.quika_web_ticket_sold_amount_last_month or 0.00)
        )

        # THIS Year
        self.salary_4_life_instant_cashout_web_ticket_sold_amount_year = list(
            salary_4_life_instant_cashout_web_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values()
        )[0]
        self.wyse_cash_web_ticket_sold_amount_year = list(wysecash_web_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values())[0]
        self.soccercash_web_ticket_sold_amount_year = list(soccercash_web_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values())[0]
        # self.banker_web_ticket_sold_amount_year = list(banker_game_play_web_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values())[0]
        self.banker_web_ticket_sold_amount_year = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_amount_year"]
        # self.quika_web_ticket_sold_amount_year = list(quika_game_play_web_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values())[0]
        self.quika_web_ticket_sold_amount_year = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_amount_year"]

        self.total_tickets_sold_web_amount_year = (
            (self.salary_4_life_instant_cashout_web_ticket_sold_amount_year or 0.00) +
            (self.soccercash_web_ticket_sold_amount_year or 0.00) +
            (self.banker_web_ticket_sold_amount_year or 0.00) +
            (self.quika_web_ticket_sold_amount_year or 0.00)
        )

        # Previous Year
        self.salary_4_life_instant_cashout_web_ticket_sold_amount_last_year = list(
            salary_4_life_instant_cashout_web_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start)
            .aggregate(Sum("amount_paid"))
            .values()
        )[0]
        self.wyse_cash_web_ticket_sold_amount_last_year = list(
            wysecash_web_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start).aggregate(Sum("amount_paid")).values()
        )[0]
        self.soccercash_web_ticket_sold_amount_last_year = list(
            soccercash_web_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start).aggregate(Sum("amount_paid")).values()
        )[0]
        # self.banker_web_ticket_sold_amount_last_year = list(
        #     banker_game_play_web_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.banker_web_ticket_sold_amount_last_year =  banker_game_play_web_qs_aggreg["banker_web_ticket_sold_amount_last_year"]
        # self.quika_web_ticket_sold_amount_last_year = list(
        #     quika_game_play_web_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.quika_web_ticket_sold_amount_last_year = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_amount_last_year"]

        self.total_tickets_sold_web_amount_last_year = (
            (self.salary_4_life_instant_cashout_web_ticket_sold_amount_last_year or 0.00) +
            (self.wyse_cash_web_ticket_sold_amount_last_year or 0.00) +
            (self.soccercash_web_ticket_sold_amount_last_year or 0.00) +
            (self.banker_web_ticket_sold_amount_last_year or 0.00) +
            (self.quika_web_ticket_sold_amount_last_year or 0.00)
        )

        # Total Tickets Sold Count ===> WEB
        self.salary_4_life_instant_cashout_web_ticket_sold_count = salary_4_life_instant_cashout_web_qs.count()
        self.wyse_cash_web_ticket_sold_count = wysecash_web_qs.count()
        self.soccercash_web_ticket_sold_count = soccercash_web_qs.count()
        # self.banker_web_ticket_sold_count = banker_game_play_web_qs.count()
        self.banker_web_ticket_sold_count = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_count"]
        # self.quika_web_ticket_sold_count = quika_game_play_web_qs.count()
        self.quika_web_ticket_sold_count = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_count"]

        self.total_tickets_sold_web_count = (
            self.salary_4_life_instant_cashout_web_ticket_sold_count
            + self.wyse_cash_web_ticket_sold_count
            + self.soccercash_web_ticket_sold_count
            + self.banker_web_ticket_sold_count
            + self.quika_web_ticket_sold_count
        )

        # Today
        self.salary_4_life_instant_cashout_web_ticket_sold_count_today = salary_4_life_instant_cashout_web_qs.filter(
            date__gte=date_today_date
        ).count()
        self.wyse_cash_web_ticket_sold_count_today = wysecash_web_qs.filter(date__gte=date_today_date).count()
        self.soccercash_web_ticket_sold_count_today = soccercash_web_qs.filter(date__gte=date_today_date).count()
        # self.banker_web_ticket_sold_count_today = banker_game_play_web_qs.filter(date__gte=date_today_date).count()
        self.banker_web_ticket_sold_count_today = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_count_today"]
        # self.quika_web_ticket_sold_count_today = quika_game_play_web_qs.filter(date__gte=date_today_date).count()
        self.quika_web_ticket_sold_count_today = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_count_today"]

        self.total_tickets_sold_web_count_today = (
            self.salary_4_life_instant_cashout_web_ticket_sold_count_today
            + self.wyse_cash_web_ticket_sold_count_today
            + self.soccercash_web_ticket_sold_count_today
            + self.banker_web_ticket_sold_count_today
            + self.quika_web_ticket_sold_count_today
        )

        # This Week
        self.salary_4_life_instant_cashout_web_ticket_sold_count_week = salary_4_life_instant_cashout_web_qs.filter(date__gte=week_start).count()
        self.wyse_cash_web_ticket_sold_count_week = wysecash_web_qs.filter(date__gte=week_start).count()
        self.soccercash_web_ticket_sold_count_week = soccercash_web_qs.filter(date__gte=week_start).count()
        # self.banker_web_ticket_sold_count_week = banker_game_play_web_qs.filter(date__gte=week_start).count()
        self.banker_web_ticket_sold_count_week = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_count_week"]
        # self.quika_web_ticket_sold_count_week = quika_game_play_web_qs.filter(date__gte=week_start).count()
        self.quika_web_ticket_sold_count_week = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_count_week"]

        self.total_tickets_sold_web_count_week = (
            self.salary_4_life_instant_cashout_web_ticket_sold_count_week
            + self.wyse_cash_web_ticket_sold_count_week
            + self.soccercash_web_ticket_sold_count_week
            + self.banker_web_ticket_sold_count_week
            + self.quika_web_ticket_sold_count_week
        )

        # This Month
        self.salary_4_life_instant_cashout_web_ticket_sold_count_month = salary_4_life_instant_cashout_web_qs.filter(date__gte=month_start).count()
        self.wyse_cash_web_ticket_sold_count_month = wysecash_web_qs.filter(date__gte=month_start).count()
        self.soccercash_web_ticket_sold_count_month = soccercash_web_qs.filter(date__gte=month_start).count()
        # self.banker_web_ticket_sold_count_month = banker_game_play_web_qs.filter(date__gte=month_start).count()
        self.banker_web_ticket_sold_count_month = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_count_month"]
        # self.quika_web_ticket_sold_count_month = quika_game_play_web_qs.filter(date__gte=month_start).count()
        self.quika_web_ticket_sold_count_month = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_count_month"]

        self.total_tickets_sold_web_count_month = (
            self.salary_4_life_instant_cashout_web_ticket_sold_count_month
            + self.wyse_cash_web_ticket_sold_count_month
            + self.soccercash_web_ticket_sold_count_month
            + self.banker_web_ticket_sold_count_month
            + self.quika_web_ticket_sold_count_month
        )

        # Last Month
        self.salary_4_life_instant_cashout_web_ticket_sold_count_last_month = salary_4_life_instant_cashout_web_qs.filter(
            date__gte=previous_month_start, date__lte=previous_month_end
        ).count()
        self.wyse_cash_web_ticket_sold_count_last_month = wysecash_web_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end).count()
        self.soccercash_web_ticket_sold_count_last_month = soccercash_web_qs.filter(
            date__gte=previous_month_start, date__lte=previous_month_end
        ).count()
        # self.banker_web_ticket_sold_count_last_month = banker_game_play_web_qs.filter(
        #     date__gte=previous_month_start, date__lte=previous_month_end
        # ).count()
        self.banker_web_ticket_sold_count_last_month = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_count_last_month"]
        # self.quika_web_ticket_sold_count_last_month = quika_game_play_web_qs.filter(
        #     date__gte=previous_month_start, date__lte=previous_month_end
        # ).count()
        self.quika_web_ticket_sold_count_last_month = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_count_last_month"]

        self.total_tickets_sold_web_count_last_month = (
            self.salary_4_life_instant_cashout_web_ticket_sold_count_last_month
            + self.wyse_cash_web_ticket_sold_count_last_month
            + self.soccercash_web_ticket_sold_count_last_month
            + self.banker_web_ticket_sold_count_last_month
            + self.quika_web_ticket_sold_count_last_month
        )

        # THIS Year
        self.salary_4_life_instant_cashout_web_ticket_sold_count_year = salary_4_life_instant_cashout_web_qs.filter(date__gte=year_start).count()
        self.wyse_cash_web_ticket_sold_count_year = wysecash_web_qs.filter(date__gte=year_start).count()
        self.soccercash_web_ticket_sold_count_year = soccercash_web_qs.filter(date__gte=year_start).count()
        # self.banker_web_ticket_sold_count_year = banker_game_play_web_qs.filter(date__gte=year_start).count()
        self.banker_web_ticket_sold_count_year = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_count_year"]
        # self.quika_web_ticket_sold_count_year = quika_game_play_web_qs.filter(date__gte=year_start).count()
        self.quika_web_ticket_sold_count_year = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_count_year"]

        self.total_tickets_sold_web_count_year = (
            self.salary_4_life_instant_cashout_web_ticket_sold_count_year
            + self.wyse_cash_web_ticket_sold_count_year
            + self.soccercash_web_ticket_sold_count_year
            + self.banker_web_ticket_sold_count_year
            + self.quika_web_ticket_sold_count_year
        )

        # Previous Year
        self.salary_4_life_instant_cashout_web_ticket_sold_count_last_year = salary_4_life_instant_cashout_web_qs.filter(
            date__gte=previous_year_start, date__lte=previous_year_start
        ).count()

        self.wyse_cash_web_ticket_sold_count_last_year = wysecash_web_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start).count()

        self.soccercash_web_ticket_sold_count_last_year = soccercash_web_qs.filter(
            date__gte=previous_year_start, date__lte=previous_year_start
        ).count()
        # self.banker_web_ticket_sold_count_last_year = banker_game_play_web_qs.filter(
        #     date__gte=previous_year_start, date__lte=previous_year_start
        # ).count()
        self.banker_web_ticket_sold_count_last_year = banker_game_play_web_qs_aggreg["banker_web_ticket_sold_count_last_year"]
        # self.quika_web_ticket_sold_count_last_year = quika_game_play_web_qs.filter(
        #     date__gte=previous_year_start, date__lte=previous_year_start
        # ).count()
        self.quika_web_ticket_sold_count_last_year = quika_game_play_web_qs_aggreg["quika_web_ticket_sold_count_last_year"]

        self.total_tickets_sold_web_count_last_year = (
            self.salary_4_life_instant_cashout_web_ticket_sold_count_last_year
            + self.wyse_cash_web_ticket_sold_count_last_year
            + self.soccercash_web_ticket_sold_count_last_year
            + self.banker_web_ticket_sold_count_last_year
            + self.quika_web_ticket_sold_count_last_year
        )

        # Total Tickets Sold Count ===> USSD
        self.salary_4_life_instant_cashout_ussd_ticket_sold_count = salary_4_life_instant_cashout_ussd_qs.count()
        self.wyse_cash_ussd_ticket_sold_count = wysecash_ussd_qs.count()
        self.soccercash_ussd_ticket_sold_count = soccercash_ussd_qs.count()
        # self.banker_ussd_ticket_sold_count = banker_game_play_ussd_qs.count()
        self.banker_ussd_ticket_sold_count = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_count"]
        # self.quika_ussd_ticket_sold_count = quika_game_play_ussd_qs.count()
        self.quika_ussd_ticket_sold_count = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_count"]

        self.total_tickets_sold_ussd_count = (
            self.salary_4_life_instant_cashout_ussd_ticket_sold_count
            + self.wyse_cash_ussd_ticket_sold_count
            + self.soccercash_ussd_ticket_sold_count
            + self.banker_ussd_ticket_sold_count
            + self.quika_ussd_ticket_sold_count
        )

        # Today
        self.salary_4_life_instant_cashout_ussd_ticket_sold_count_today = salary_4_life_instant_cashout_ussd_qs.filter(
            date__gte=date_today_date
        ).count()
        self.wyse_cash_ussd_ticket_sold_count_today = wysecash_ussd_qs.filter(date__gte=date_today_date).count()
        self.soccercash_ussd_ticket_sold_count_today = soccercash_ussd_qs.filter(date__gte=date_today_date).count()
        # self.banker_ussd_ticket_sold_count_today = banker_game_play_ussd_qs.filter(date__gte=date_today_date).count()
        self.banker_ussd_ticket_sold_count_today = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_count_today"]
        # self.quika_ussd_ticket_sold_count_today = quika_game_play_ussd_qs.filter(date__gte=date_today_date).count()
        self.quika_ussd_ticket_sold_count_today = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_count_today"]

        self.total_tickets_sold_ussd_count_today = (
            self.salary_4_life_instant_cashout_ussd_ticket_sold_count_today
            + self.wyse_cash_ussd_ticket_sold_count_today
            + self.soccercash_ussd_ticket_sold_count_today
            + self.banker_ussd_ticket_sold_count_today
            + self.quika_ussd_ticket_sold_count_today
        )

        # This Week
        self.salary_4_life_instant_cashout_ussd_ticket_sold_count_week = salary_4_life_instant_cashout_ussd_qs.filter(date__gte=week_start).count()
        self.wyse_cash_ussd_ticket_sold_count_week = wysecash_ussd_qs.filter(date__gte=week_start).count()
        self.soccercash_ussd_ticket_sold_count_week = soccercash_ussd_qs.filter(date__gte=week_start).count()
        # self.banker_ussd_ticket_sold_count_week = banker_game_play_ussd_qs.filter(date__gte=week_start).count()
        self.banker_ussd_ticket_sold_count_week = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_count_week"]
        # self.quika_ussd_ticket_sold_count_week = quika_game_play_ussd_qs.filter(date__gte=week_start).count()
        self.quika_ussd_ticket_sold_count_week = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_count_week"]

        self.total_tickets_sold_ussd_count_week = (
            self.salary_4_life_instant_cashout_ussd_ticket_sold_count_week
            + self.wyse_cash_ussd_ticket_sold_count_week
            + self.soccercash_ussd_ticket_sold_count_week
            + self.banker_ussd_ticket_sold_count_week
            + self.quika_ussd_ticket_sold_count_week
        )

        # This Month
        self.salary_4_life_instant_cashout_ussd_ticket_sold_count_month = salary_4_life_instant_cashout_ussd_qs.filter(date__gte=month_start).count()
        self.wyse_cash_ussd_ticket_sold_count_month = wysecash_ussd_qs.filter(date__gte=month_start).count()
        self.soccercash_ussd_ticket_sold_count_month = soccercash_ussd_qs.filter(date__gte=month_start).count()
        # self.banker_ussd_ticket_sold_count_month = banker_game_play_ussd_qs.filter(date__gte=month_start).count()
        self.banker_ussd_ticket_sold_count_month = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_count_month"]
        # self.quika_ussd_ticket_sold_count_month = quika_game_play_ussd_qs.filter(date__gte=month_start).count()
        self.quika_ussd_ticket_sold_count_month = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_count_month"]

        self.total_tickets_sold_ussd_count_month = (
            self.salary_4_life_instant_cashout_ussd_ticket_sold_count_month
            + self.wyse_cash_ussd_ticket_sold_count_month
            + self.soccercash_ussd_ticket_sold_count_month
            + self.banker_ussd_ticket_sold_count_month
            + self.quika_ussd_ticket_sold_count_month
        )

        # Last Month
        self.salary_4_life_instant_cashout_ussd_ticket_sold_count_last_month = salary_4_life_instant_cashout_ussd_qs.filter(
            date__gte=previous_month_start, date__lte=previous_month_end
        ).count()
        self.wyse_cash_ussd_ticket_sold_count_last_month = wysecash_ussd_qs.filter(
            date__gte=previous_month_start, date__lte=previous_month_end
        ).count()
        self.soccercash_ussd_ticket_sold_count_last_month = soccercash_ussd_qs.filter(
            date__gte=previous_month_start, date__lte=previous_month_end
        ).count()
        # self.banker_ussd_ticket_sold_count_last_month = banker_game_play_ussd_qs.filter(
        #     date__gte=previous_month_start, date__lte=previous_month_end
        # ).count()
        self.banker_ussd_ticket_sold_count_last_month = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_count_last_month"]
        # self.quika_ussd_ticket_sold_count_last_month = quika_game_play_ussd_qs.filter(
        #     date__gte=previous_month_start, date__lte=previous_month_end
        # ).count()
        self.quika_ussd_ticket_sold_count_last_month = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_count_last_month"]

        self.total_tickets_sold_ussd_count_last_month = (
            self.salary_4_life_instant_cashout_ussd_ticket_sold_count_last_month
            + self.wyse_cash_ussd_ticket_sold_count_last_month
            + self.soccercash_ussd_ticket_sold_count_last_month
            + self.banker_ussd_ticket_sold_count_last_month
            + self.quika_ussd_ticket_sold_count_last_month
        )

        # THIS Year
        self.salary_4_life_instant_cashout_ussd_ticket_sold_count_year = salary_4_life_instant_cashout_ussd_qs.filter(date__gte=year_start).count()
        self.wyse_cash_ussd_ticket_sold_count_year = wysecash_ussd_qs.filter(date__gte=year_start).count()
        self.soccercash_ussd_ticket_sold_count_year = soccercash_ussd_qs.filter(date__gte=year_start).count()
        # self.banker_ussd_ticket_sold_count_year = banker_game_play_ussd_qs.filter(date__gte=year_start).count()
        self.banker_ussd_ticket_sold_count_year = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_count_year"]
        # self.quika_ussd_ticket_sold_count_year = quika_game_play_ussd_qs.filter(date__gte=year_start).count()
        self.quika_ussd_ticket_sold_count_year = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_count_year"]

        self.total_tickets_sold_ussd_count_year = (
            self.salary_4_life_instant_cashout_ussd_ticket_sold_count_year
            + self.wyse_cash_ussd_ticket_sold_count_year
            + self.soccercash_ussd_ticket_sold_count_year
            + self.banker_ussd_ticket_sold_count_year
            + self.quika_ussd_ticket_sold_count_year
        )

        # Previous Year
        self.salary_4_life_instant_cashout_ussd_ticket_sold_count_last_year = salary_4_life_instant_cashout_ussd_qs.filter(
            date__gte=previous_year_start, date__lte=previous_year_start
        ).count()
        self.wyse_cash_ussd_ticket_sold_count_last_year = wysecash_ussd_qs.filter(
            date__gte=previous_year_start, date__lte=previous_year_start
        ).count()
        self.soccercash_ussd_ticket_sold_count_last_year = soccercash_ussd_qs.filter(
            date__gte=previous_year_start, date__lte=previous_year_start
        ).count()
        # self.banker_ussd_ticket_sold_count_last_year = banker_game_play_ussd_qs.filter(
        #     date__gte=previous_year_start, date__lte=previous_year_start
        # ).count()
        self.banker_ussd_ticket_sold_count_last_year = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_count_last_year"]
        # self.quika_ussd_ticket_sold_count_last_year = quika_game_play_ussd_qs.filter(
        #     date__gte=previous_year_start, date__lte=previous_year_start
        # ).count()
        self.quika_ussd_ticket_sold_count_last_year = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_count_last_year"]

        self.total_tickets_sold_ussd_count_last_year = (
            self.salary_4_life_instant_cashout_ussd_ticket_sold_count_last_year
            + self.wyse_cash_ussd_ticket_sold_count_last_year
            + self.soccercash_ussd_ticket_sold_count_last_year
            + self.banker_ussd_ticket_sold_count_last_year
            + self.quika_ussd_ticket_sold_count_last_year
        )

        # Tickets Sold Amount USSD
        # Tickets Sold Amount ====> USSD
        self.salary_4_life_instant_cashout_ussd_ticket_sold_amount = list(
            salary_4_life_instant_cashout_ussd_qs.aggregate(Sum("amount_paid")).values()
        )[0]
        self.wyse_cash_ussd_ticket_sold_amount = list(wysecash_ussd_qs.aggregate(Sum("amount_paid")).values())[0]
        self.soccercash_ussd_ticket_sold_amount = list(soccercash_ussd_qs.aggregate(Sum("amount_paid")).values())[0]
        # self.banker_ussd_ticket_sold_amount = list(banker_game_play_ussd_qs.aggregate(Sum("amount_paid")).values())[0]
        self.banker_ussd_ticket_sold_amount = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_amount"]
        # self.quika_ussd_ticket_sold_amount = list(quika_game_play_ussd_qs.aggregate(Sum("amount_paid")).values())[0]
        self.quika_ussd_ticket_sold_amount = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_amount"]

        self.total_tickets_sold_ussd_amount = (
            (self.salary_4_life_instant_cashout_ussd_ticket_sold_amount or 0.00) +
            (self.wyse_cash_ussd_ticket_sold_amount or 0.00) +
            (self.soccercash_ussd_ticket_sold_amount or 0.00) +
            (self.banker_ussd_ticket_sold_amount or 0.00) +
            (self.quika_ussd_ticket_sold_amount or 0.00)
        )

        # Today
        self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_today = list(
            salary_4_life_instant_cashout_ussd_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values()
        )[0]
        self.wyse_cash_ussd_ticket_sold_amount_today = list(
            wysecash_ussd_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values()
        )[0]
        self.soccercash_ussd_ticket_sold_amount_today = list(
            soccercash_ussd_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values()
        )[0]
        # self.banker_ussd_ticket_sold_amount_today = list(
        #     banker_game_play_ussd_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.banker_ussd_ticket_sold_amount_today = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_amount_today"]
        # self.quika_ussd_ticket_sold_amount_today = list(
        #     quika_game_play_ussd_qs.filter(date__gte=date_today_date).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.quika_ussd_ticket_sold_amount_today = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_amount_today"]

        self.total_tickets_sold_ussd_amount_today = (
            (self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_today or 0.00) +
            (self.wyse_cash_ussd_ticket_sold_amount_today or 0.00) +
            (self.soccercash_ussd_ticket_sold_amount_today or 0.00) +
            (self.banker_ussd_ticket_sold_amount_today or 0.00) +
            (self.quika_ussd_ticket_sold_amount_today or 0.00)
        )

        # This Week
        self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_week = list(
            salary_4_life_instant_cashout_ussd_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values()
        )[0]
        self.wyse_cash_ussd_ticket_sold_amount_week = list(wysecash_ussd_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values())[0]
        self.soccercash_ussd_ticket_sold_amount_week = list(soccercash_ussd_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values())[0]
        # self.banker_ussd_ticket_sold_amount_week = list(banker_game_play_ussd_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values())[0]
        self.banker_ussd_ticket_sold_amount_week = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_amount_week"]
        # self.quika_ussd_ticket_sold_amount_week = list(quika_game_play_ussd_qs.filter(date__gte=week_start).aggregate(Sum("amount_paid")).values())[0]
        self.quika_ussd_ticket_sold_amount_week = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_amount_week"]

        self.total_tickets_sold_ussd_amount_week = (
            (self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_week or 0.00) +
            (self.wyse_cash_ussd_ticket_sold_amount_week or 0.00) +
            (self.soccercash_ussd_ticket_sold_amount_week or 0.00) +
            (self.banker_ussd_ticket_sold_amount_week or 0.00) +
            (self.quika_ussd_ticket_sold_amount_week or 0.00)
        )

        # This Month
        self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_month = list(
            salary_4_life_instant_cashout_ussd_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values()
        )[0]
        self.wyse_cash_ussd_ticket_sold_amount_month = list(wysecash_ussd_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values())[0]
        self.soccercash_ussd_ticket_sold_amount_month = list(soccercash_ussd_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values())[
            0
        ]
        # self.banker_ussd_ticket_sold_amount_month = list(
        #     banker_game_play_ussd_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.banker_ussd_ticket_sold_amount_month = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_amount_month"]
        # self.quika_ussd_ticket_sold_amount_month = list(quika_game_play_ussd_qs.filter(date__gte=month_start).aggregate(Sum("amount_paid")).values())[0]
        self.quika_ussd_ticket_sold_amount_month = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_amount_month"]

        self.total_tickets_sold_ussd_amount_month = (
            (self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_month or 0.00) +
            (self.wyse_cash_ussd_ticket_sold_amount_month or 0.00) +
            (self.soccercash_ussd_ticket_sold_amount_month or 0.00) +
            (self.banker_ussd_ticket_sold_amount_month or 0.00) +
            (self.quika_ussd_ticket_sold_amount_month or 0.00)
        )

        # Last Month
        self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_last_month = list(
            salary_4_life_instant_cashout_ussd_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end)
            .aggregate(Sum("amount_paid"))
            .values()
        )[0]
        self.wyse_cash_ussd_ticket_sold_amount_last_month = list(
            wysecash_ussd_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end).aggregate(Sum("amount_paid")).values()
        )[0]
        self.soccercash_ussd_ticket_sold_amount_last_month = list(
            soccercash_ussd_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end).aggregate(Sum("amount_paid")).values()
        )[0]
        # self.banker_ussd_ticket_sold_amount_last_month = list(
        #     banker_game_play_ussd_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.banker_ussd_ticket_sold_amount_last_month = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_amount_last_month"]
        # self.quika_ussd_ticket_sold_amount_last_month = list(
        #     quika_game_play_ussd_qs.filter(date__gte=previous_month_start, date__lte=previous_month_end).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.quika_ussd_ticket_sold_amount_last_month = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_amount_last_month"]

        self.total_tickets_sold_ussd_amount_last_month = (
            (self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_last_month or 0.00) +
            (self.wyse_cash_ussd_ticket_sold_amount_last_month or 0.00) +
            (self.soccercash_ussd_ticket_sold_amount_last_month or 0.00) +
            (self.banker_ussd_ticket_sold_amount_last_month or 0.00) +
            (self.quika_ussd_ticket_sold_amount_last_month or 0.00)
        )

        # THIS Year
        self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_year = list(
            salary_4_life_instant_cashout_ussd_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values()
        )[0]
        self.wyse_cash_ussd_ticket_sold_amount_year = list(wysecash_ussd_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values())[0]
        self.soccercash_ussd_ticket_sold_amount_year = list(soccercash_ussd_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values())[0]
        # self.banker_ussd_ticket_sold_amount_year = list(banker_game_play_ussd_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values())[0]
        self.banker_ussd_ticket_sold_amount_year = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_amount_year"]
        # self.quika_ussd_ticket_sold_amount_year = list(quika_game_play_ussd_qs.filter(date__gte=year_start).aggregate(Sum("amount_paid")).values())[0]
        self.quika_ussd_ticket_sold_amount_year = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_amount_year"]

        self.total_tickets_sold_ussd_amount_year = (
            (self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_year or 0.00) +
            (self.wyse_cash_ussd_ticket_sold_amount_year or 0.00) +
            (self.soccercash_ussd_ticket_sold_amount_year or 0.00) +
            (self.banker_ussd_ticket_sold_amount_year or 0.00) +
            (self.quika_ussd_ticket_sold_amount_year or 0.00)
        )

        # Previous Year
        self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_last_year = list(
            salary_4_life_instant_cashout_ussd_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start)
            .aggregate(Sum("amount_paid"))
            .values()
        )[0]
        self.wyse_cash_ussd_ticket_sold_amount_last_year = list(
            wysecash_ussd_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start).aggregate(Sum("amount_paid")).values()
        )[0]
        self.soccercash_ussd_ticket_sold_amount_last_year = list(
            soccercash_ussd_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start).aggregate(Sum("amount_paid")).values()
        )[0]
        # self.banker_ussd_ticket_sold_amount_last_year = list(
        #     banker_game_play_ussd_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.banker_ussd_ticket_sold_amount_last_year = banker_ussd_ticket_aggregate["banker_ussd_ticket_sold_amount_last_year"]
        # self.quika_ussd_ticket_sold_amount_last_year = list(
        #     quika_game_play_ussd_qs.filter(date__gte=previous_year_start, date__lte=previous_year_start).aggregate(Sum("amount_paid")).values()
        # )[0]
        self.quika_ussd_ticket_sold_amount_last_year = quika_ussd_ticket_aggregate["quika_ussd_ticket_sold_amount_last_year"]

        self.total_tickets_sold_ussd_amount_last_year = (
            (self.salary_4_life_instant_cashout_ussd_ticket_sold_amount_last_year or 0.00) +
            (self.wyse_cash_ussd_ticket_sold_amount_last_year or 0.00) +
            (self.soccercash_ussd_ticket_sold_amount_last_year or 0.00) +
            (self.banker_ussd_ticket_sold_amount_last_year or 0.00) +
            (self.quika_ussd_ticket_sold_amount_last_year or 0.00)
        )

        # Total Tickets Sold Amount ===> WEB & POS
        self.total_tickets_sold_web_pos_amount_today = (
            (self.ticket_sold_amount_today or 0.00)
            + (self.total_tickets_sold_web_amount_today or 0.00)
            + (self.total_tickets_sold_ussd_amount_today or 0.00)
        )

        self.total_tickets_sold_web_pos_amount_week = (
            (self.ticket_sold_amount_week or 0.00)
            + (self.total_tickets_sold_web_amount_week or 0.00)
            + (self.total_tickets_sold_ussd_amount_week or 0.00)
        )
        self.total_tickets_sold_web_pos_amount_month = (
            (self.ticket_sold_amount_month or 0.00)
            + (self.total_tickets_sold_web_amount_month or 0.00)
            + (self.total_tickets_sold_ussd_amount_month or 0.00)
        )
        self.total_tickets_sold_web_pos_amount_last_month = (
            (self.ticket_sold_amount_last_month or 0.00)
            + (self.total_tickets_sold_web_amount_last_month or 0.00)
            + (self.total_tickets_sold_ussd_amount_last_month or 0.00)
        )
        self.total_tickets_sold_web_pos_amount_year = (
            (self.ticket_sold_amount_year or 0.00)
            + (self.total_tickets_sold_web_amount_year or 0.00)
            + (self.total_tickets_sold_ussd_amount_year or 0.00)
        )
        self.total_tickets_sold_web_pos_amount_last_year = (
            (self.ticket_sold_amount_last_year or 0.00)
            + (self.total_tickets_sold_web_amount_last_year or 0.00)
            + (self.total_tickets_sold_ussd_amount_last_year or 0.00)
        )

        # Total Tickets Sold Count ===> WEB & POS
        self.total_tickets_sold_web_pos_count_today = (
            self.ticket_sold_count_today + self.total_tickets_sold_web_count_today + self.total_tickets_sold_ussd_count_today
        )

        self.total_tickets_sold_web_pos_count_week = (
            self.ticket_sold_count_week + self.total_tickets_sold_web_count_week + self.total_tickets_sold_ussd_count_week
        )

        self.total_tickets_sold_web_pos_count_month = (
            self.ticket_sold_count_month + self.total_tickets_sold_web_count_month + self.total_tickets_sold_ussd_count_month
        )

        self.total_tickets_sold_web_pos_count_last_month = (
            self.ticket_sold_count_last_month + self.total_tickets_sold_web_count_last_month + self.total_tickets_sold_ussd_count_last_month
        )

        self.total_tickets_sold_web_pos_count_year = (
            self.ticket_sold_count_year + self.total_tickets_sold_web_count_year + self.total_tickets_sold_ussd_count_year
        )

        self.total_tickets_sold_web_pos_count_last_year = (
            self.ticket_sold_count_last_year + self.total_tickets_sold_web_count_last_year + self.total_tickets_sold_ussd_count_last_year
        )

        # Total Terminals Selling Based on Duration
        total_terminals_date_qs = (
            tickets_sold_qs.filter(agent_wallet__agent__terminal_id__isnull=False)
            .values("agent_wallet__agent__terminal_id")
            .distinct("agent_wallet__agent__terminal_id")
        )
        self.total_terminals_count_today = total_terminals_date_qs.filter(date_created__date=date_today_date).count()
        self.total_terminals_count_week = total_terminals_date_qs.filter(date_created__gte=week_start).count()
        self.total_terminals_count_month = total_terminals_date_qs.filter(date_created__gte=month_start).count()
        self.total_terminals_count_year = total_terminals_date_qs.filter(date_created__gte=year_start).count()
        self.total_terminals_count_last_year = total_terminals_date_qs.filter(
            date_created__gte=previous_year_start, date_created__lte=previous_year_end
        ).count()
        self.total_terminals_count_last_month = total_terminals_date_qs.filter(
            date_created__gte=previous_month_start, date_created__lte=previous_month_end
        ).count()

        # Transactions
        transactions_qs = AgentWalletTransaction.objects.all().filter(
            Q(status="SUCCESSFUL", transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"]) 
            & Q(agent_wallet__agent__supervisor__user_id=request.user.id) 
        )  # --> only game_play and retail
        all_transactions_qs = AgentWalletTransaction.objects.all().filter(status="SUCCESSFUL").filter(agent_wallet__agent__supervisor__user_id=request.user.id)
        remittance_qs = agents_remittance_queryset.filter(remitted=True)

        # winwise agent contain wise in name and have termianl id
        # Transactions Amount
        self.total_transactions_amount = list(all_transactions_qs.aggregate(Sum("amount")).values())[0]
        self.ticket_sales_amount = list(tickets_sold_qs.aggregate(Sum("amount")).values())[0]
        self.remittance_amount = list(remittance_qs.aggregate(Sum("amount")).values())[0]
        self.previous_month_total_transaction_amount = list(
            all_transactions_qs.filter(date_created__lte=previous_month_end).aggregate(Sum("amount")).values()
        )[0]

        # This month Total tRANSACTION AMOUNT
        self.total_transactions_amount_this_month = list(all_transactions_qs.filter(date_created__gte=month_start).aggregate(Sum("amount")).values())[
            0
        ]

        self.ticket_sales_amount_percentage = (
            (self.ticket_sales_amount if self.ticket_sales_amount else 0)
            / (self.total_transactions_amount if self.total_transactions_amount else 1)
            * 100
        )
        self.remittance_amount_percentage = (
            (self.remittance_amount if self.remittance_amount else 0)
            / (self.total_transactions_amount if self.total_transactions_amount else 1)
            * 100
        )

        # Transaction comparatives
        game_play_qs = transactions_qs.filter(transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"], transaction_type="DEBIT")
        winnings_qs = AgentWalletTransaction.objects.all().filter(transaction_from="WINNINGS", transaction_type="CREDIT", status="SUCCESSFUL")

        self.game_play_amount = list(game_play_qs.aggregate(Sum("amount")).values())[0]
        self.winnings_amount = list(winnings_qs.aggregate(Sum("amount")).values())[0]

        self.game_play_amount_percentage = (
            (self.game_play_amount if self.game_play_amount else 0) / (self.total_transactions_amount if self.total_transactions_amount else 1) * 100
        )
        self.winnings_amount_percentage = (
            (self.winnings_amount if self.winnings_amount else 0) / (self.total_transactions_amount if self.total_transactions_amount else 1) * 100
        )

        # Transactions Count
        self.total_transactions_count = all_transactions_qs.count()
        self.ticket_sales_count = tickets_sold_qs.count()
        self.remittance_count = remittance_qs.count()
        self.game_play_transactions_count = game_play_qs.count()
        self.winnings_transactions_count = winnings_qs.count()
        self.cashout_transactions_count = transactions_qs.filter(transaction_from__in=["WINNINGS_WITHDRAW", "GAME_PLAY", "WINNINGS"]).count()
        self.total_transactions_count_this_month = all_transactions_qs.filter(date_created__gte=month_start).count()

        # cashout -> winnings_withdraw
        # game_play and winnings

        # Failed Transactions
        failed_transactions_qs = AgentWalletTransaction.objects.all().filter(status="FAILED")
        failed_cashout_transactions = failed_transactions_qs.filter(transaction_from__in=["WINNINGS_WITHDRAW", "GAME_PLAY", "WINNINGS"])
        failed_game_play_transactions = failed_transactions_qs.filter(ticket_instance="GAME_PLAY", transaction_type="DEBIT")

        self.failed_cashout_transactions_amount = list(failed_cashout_transactions.aggregate(Sum("amount")).values())[0]
        self.failed_game_play_transactions_amount = list(failed_game_play_transactions.aggregate(Sum("amount")).values())[0]
        self.failed_game_play_transactions_amount_year = list(
            failed_game_play_transactions.filter(date_created__gte=year_start).aggregate(Sum("amount")).values()
        )[0]

        self.failed_cashout_transactions_count = failed_cashout_transactions.count()
        self.failed_game_play_transactions_count = failed_game_play_transactions.count()
        self.failed_game_play_transactions_count_year = failed_game_play_transactions.filter(date_created__gte=year_start).count()

        # Total Sales All-time | Agent Performance status per month based on Active days (30%) and Sales volume of
        self.overall_sales_amount = self.ticket_sales_amount
        self.other_lotto_agents_sales_amount = list(
            # tickets_sold_qs.exclude(agent_wallet__agent__full_name__icontains="Wise").aggregate(Sum("amount")).values()
            tickets_sold_qs.exclude(agent_wallet__agent__is_winwise_staff_agent=True).aggregate(Sum("amount")).values()
        )[0]
        self.winwise_lotto_agents_sales_amount = list(
            # tickets_sold_qs.filter(agent_wallet__agent__full_name__icontains="Wise").aggregate(Sum("amount")).values()
            tickets_sold_qs.filter(agent_wallet__agent__is_winwise_staff_agent=True).aggregate(Sum("amount")).values()
        )[0]
        # self.sales_amount_this_month = list(tickets_sold_qs.filter(agent_wallet__agent__first_name__icontains="wise").aggregate(Sum("amount")).values())[0] # noqa

        # Average Sales Amount
        self.average_sales_today_amount = (self.ticket_sold_amount_today or 0) / (self.total_terminals_count_today or 1)

        # Total terminals selling
        self.average_sales_this_week_amount = (self.ticket_sold_amount_week or 0) / (self.total_terminals_count_week or 1)
        self.average_sales_this_month_amount = (self.ticket_sold_amount_month or 0) / (self.total_terminals_count_month or 1)
        self.average_sales_last_month_amount = (self.ticket_sold_amount_last_month or 0) / (self.total_terminals_count_last_month or 1)
        self.average_sales_year_amount = (self.ticket_sold_amount_year or 0) / (self.total_terminals_count_year or 1)
        self.average_sales_previous_year_amount = (self.ticket_sold_amount_last_year or 0) / (self.total_terminals_count_last_year or 1)

        self.overall_sales_count = tickets_sold_qs.count()

        self.previous_month_overall_sales_amount = list(
            tickets_sold_qs.filter(date_created__lte=previous_month_end).aggregate(Sum("amount")).values()
        )[0]
        self.previous_month_overall_sales_count = tickets_sold_qs.filter(date_created__lte=previous_month_end).count()

        # Average Sales Count
        self.avereage_ticket_sold_count_today = self.ticket_sold_count_today / (self.total_terminals_count_today or 1)
        self.avereage_ticket_sold_count_week = self.ticket_sold_count_week / (self.total_terminals_count_week or 1)
        self.avereage_ticket_sold_count_month = self.ticket_sold_count_month / (self.total_terminals_count_month or 1)
        self.avereage_ticket_sold_count_year = self.ticket_sold_count_year / (self.total_terminals_count_year or 1)
        self.avereage_ticket_sold_count_last_year = self.ticket_sold_count_last_year / (self.total_terminals_count_last_year or 1)
        self.avereage_ticket_sold_count_last_month = self.ticket_sold_count_last_month / (self.total_terminals_count_last_month or 1)
        self.avereage_ticket_sold_count_this_month = self.ticket_sold_count_this_month / (self.total_terminals_count_month or 1)

        # Percentage Change
        self.overall_overall_sales_amount_percentage = get_percentage_change(self.previous_month_overall_sales_amount, self.overall_sales_amount)
        self.overall_sales_count_percentage = get_percentage_change(self.previous_month_overall_sales_count, self.overall_sales_count)

        # Comparative
        self.other_lotto_agents_sales_percentage = (
            (self.other_lotto_agents_sales_amount or 0)
            / (self.overall_sales_amount or 1)
            * 100
        )
        self.winwise_lotto_agents_sales_percentage = (
            (self.winwise_lotto_agents_sales_amount if self.winwise_lotto_agents_sales_amount else 0)
            / (self.overall_sales_amount or 1)
            * 100
        )

        # Average Agents Transactions
        self.average_agents_transactions_amount = (self.total_transactions_amount if self.total_transactions_amount else 0) / (
            self.total_terminals_count if self.total_terminals_count else 1
        )
        self.previous_average_agents_transactions_amount = (
            self.previous_month_total_transaction_amount if self.previous_month_total_transaction_amount else 0
        ) / (self.previous_month_total_terminals_count if self.previous_month_total_terminals_count else 1)
        self.average_agents_transactions_amount_percentage_change = get_percentage_change(
            self.previous_average_agents_transactions_amount, self.average_agents_transactions_amount
        )

        # This Month Average Agent Transaction Amount
        self.average_agents_transactions_amount_this_month = (
            self.total_transactions_amount_this_month if self.total_transactions_amount_this_month else 0
        ) / (self.total_terminals_count if self.total_terminals_count else 1)

        # Total Players
        players_qs = transactions_qs.filter().distinct("phone_number")
        self.total_players_count = players_qs.count()
        self.previous_month_total_players_count = players_qs.filter(date_created__lte=previous_month_end).count()
        self.total_players_change = get_percentage_change(self.previous_month_total_players_count, self.total_players_count)

        # New Players
        self.new_players_count = players_qs.filter(date_created__gte=month_start).count()  # new players count
        self.previous_month_new_players_count = players_qs.filter(
            date_created__gte=previous_month_start, date_created__lte=previous_month_end
        ).count()
        self.new_players_change = get_percentage_change(self.previous_month_new_players_count, self.new_players_count)

        # Total Commissions
        commission_qs = AgentWallet.objects.all().filter()  # commission_on_game_play
        self.total_commission_amount = list(
            commission_qs.annotate(commissions_amount=F("commission_bal") + F("commission_rewarded"))
            .values()
            .aggregate(Sum("commissions_amount"))
            .values()
        )[0]
        self.previous_month_total_commission_amount = list(
            commission_qs.annotate(commissions_amount=F("commission_bal") + F("commission_rewarded"))
            .values()
            .aggregate(Sum("commissions_amount"))
            .values()
        )[0]
        self.total_commission_amount_this_month = list(
            commission_qs.annotate(commissions_amount=F("commission_bal") + F("commission_rewarded"))
            .values()
            .aggregate(Sum("commissions_amount"))
            .values()
        )[0]

        # Paid/Retained Commission
        self.paid_commission_amount = list(commission_qs.aggregate(Sum("commission_rewarded")).values())[0]
        self.paid_commission_amount_this_month = list(
            commission_qs.filter(created_at__gte=month_start).aggregate(Sum("commission_rewarded")).values()
        )[0]

        self.retained_commission_amount = list(commission_qs.filter().aggregate(Sum("commission_bal")).values())[0]
        self.retained_commission_amount_this_month = list(
            commission_qs.filter().filter(created_at__gte=month_start).aggregate(Sum("commission_bal")).values()
        )[0]

        # Commission Amount Change
        self.total_commission_amount_change = get_percentage_change(self.previous_month_total_commission_amount, self.total_commission_amount)

        # Agents Salary
        try:
            salary_amount = ConstantTable.objects.last().winwise_agents_salary_amount
        except Exception:
            salary_amount = 30000

        self.agent_salary_amount = self.winwise_agents_terminals_count * salary_amount
        self.agent_salary_amount_this_month = self.total_terminals_count_month * salary_amount

        # SMS Cost
        sms_charge_qs = SmsChargeWallet.objects.all()
        self.sms_cost_amount = list(sms_charge_qs.aggregate(Sum("amount")).values())[0]
        self.previous_month_sms_cost_amount = list(sms_charge_qs.filter(created_at__lte=previous_month_end).aggregate(Sum("amount")).values())[0]

        # SMS Cost change
        self.sms_charge_change = get_percentage_change(self.previous_month_sms_cost_amount, self.sms_cost_amount)

        # Average sales amount
        self.average_agent_sales_amount = (self.overall_sales_amount or 0) / (
            self.total_terminals_count if self.total_terminals_count else 1
        )
        self.average_agent_sales_amount_previous_month = (
            self.previous_month_overall_sales_amount if self.previous_month_overall_sales_amount else 0
        ) / (self.previous_month_total_terminals_count if self.previous_month_total_terminals_count else 1)
        # self.average_agent_sales_amount_this_month = (self.sales_amount_this_month if self.sales_amount_this_month else 0) / (self.total_terminals_count if self.total_terminals_count else 1) # noqa

        # Average sales count
        self.tickets_sales_count = tickets_sold_qs.count()
        self.average_agent_sales_count = (self.tickets_sales_count) / (self.total_terminals_count if self.total_terminals_count else 1)
        self.previous_month_average_agent_sales_count = (self.previous_month_overall_sales_count) / (
            self.previous_month_total_terminals_count if self.previous_month_total_terminals_count else 1
        )
        self.average_agent_sales_count_this_month = (self.ticket_sold_count_this_month) / (
            self.total_terminals_count if self.total_terminals_count else 1
        )

        self.average_agent_sales_count_change = get_percentage_change(self.previous_month_average_agent_sales_count, self.average_agent_sales_count)
        self.average_agent_sales_amount_change = get_percentage_change(
            self.average_agent_sales_amount_previous_month, self.average_agent_sales_amount
        )

        # Average Work Hours
        self.average_work_hours = 0

        # Tickets Bought Range Amount ==> Game Types | Tickets Sold ---> POS and Online (All)
        game_play_tickets_qs = LottoTicket.objects.all().filter(paid=True, **self.date_filter_two)
        wyse_cash_game_play_tickets_qs = LotteryModel.objects.all().filter(**self.date_filter_two, paid=True)
        soccer_cash_game_play_qs = SoccerPrediction.objects.all().filter(paid=True, **self.date_filter_two)

        self.salary_4_life_qs = game_play_tickets_qs.filter(lottery_type="SALARY_FOR_LIFE")
        self.instant_cashout_qs = game_play_tickets_qs.filter(lottery_type="INSTANT_CASHOUT")
        self.wysecash_qs = wyse_cash_game_play_tickets_qs.filter(lottery_type="WYSE_CASH")
        self.soccercash_qs = soccer_cash_game_play_qs.filter(lottery_type="SOCCER_CASH")
        self.banker_qs = game_play_tickets_qs.filter(lottery_type="BANKER")
        self.quika_qs = game_play_tickets_qs.filter(lottery_type="QUIKA")

        # Winnings Qs ----> Online and POS
        winnings_pos_web_qs = LottoWinners.objects.all().filter(**self.date_filter_five)
        wysecash_winnings_pos_web_qs = LotteryWinnersTable.objects.all().filter(**self.date_filter_five)
        soccercash_winnings_post_web_qs = SoccerCashWinner.objects.all().filter(**self.date_filter_five)

        self.salary_4_life_winnings_qs = winnings_pos_web_qs.filter(lotto_type="SALARY_FOR_LIFE")
        self.instant_cashout_winnings_qs = winnings_pos_web_qs.filter(lotto_type="INSTANT_CASHOUT")
        self.wysecash_winnings_qs = wysecash_winnings_pos_web_qs.filter()
        self.soccercash_winnings_qs = soccercash_winnings_post_web_qs.filter()
        self.banker_winnings_qs = winnings_pos_web_qs.filter(lotto_type="BANKER")
        self.quika_winnings_qs = winnings_pos_web_qs.filter(lotto_type="QUIKA")

        # Game Types Tickets Sales Count ----> Online and POS
        self.salary_4_life_350_700_count = self.salary_4_life_qs.count()
        self.instant_cashout_200_600_count = self.instant_cashout_qs.count()
        self.wysecash_200_600_count = self.wysecash_qs.count()
        self.soccercash_200_600_count = self.soccercash_qs.count()
        self.banker_200_600_count = self.banker_qs.count()
        self.quika_200_600_count = self.quika_qs.count()

        # Game Types Average Ticket Sales -----> online and POS
        self.average_ticket_sales_salary_4_life = list(self.salary_4_life_qs.aggregate(Avg("amount_paid")).values())[0]
        self.average_ticket_sales_instant_cashout = list(self.instant_cashout_qs.aggregate(Avg("amount_paid")).values())[0]
        self.average_ticket_sales_wysecash = list(self.wysecash_qs.aggregate(Avg("amount_paid")).values())[0]
        self.average_ticket_sales_soccercash = list(self.soccercash_qs.aggregate(Avg("amount_paid")).values())[0]
        self.average_ticket_sales_banker = list(self.banker_qs.aggregate(Avg("amount_paid")).values())[0]
        self.average_ticket_sales_quika = list(self.quika_qs.aggregate(Avg("amount_paid")).values())[0]

        # Game Types Tickets Sales Amount ----> online and POS
        self.total_ticket_sales_amount_salary_4_life = list(self.salary_4_life_qs.aggregate(Sum("amount_paid")).values())[0]
        self.total_ticket_sales_amount_instant_cashout = list(self.instant_cashout_qs.aggregate(Sum("amount_paid")).values())[0]
        self.total_ticket_sales_amount_wysecash = list(self.wysecash_qs.aggregate(Sum("amount_paid")).values())[0]
        self.total_ticket_sales_amount_soccercash = list(self.soccercash_qs.aggregate(Sum("amount_paid")).values())[0]
        self.total_ticket_sales_amount_banker = list(self.banker_qs.aggregate(Sum("amount_paid")).values())[0]
        self.total_ticket_sales_amount_quika = list(self.quika_qs.aggregate(Sum("amount_paid")).values())[0]

        # Game Types Winnings Amount ---> POS and Online
        self.total_winnings_amount_salary_4_life = list(self.salary_4_life_winnings_qs.aggregate(Sum("earning")).values())[0]
        self.total_winnings_amount_instant_cashout = list(self.instant_cashout_winnings_qs.aggregate(Sum("earning")).values())[0]
        self.total_winnings_amount_wysecash = list(self.wysecash_winnings_qs.aggregate(Sum("earning")).values())[0]
        self.total_winnings_amount_soccercash = list(self.soccercash_winnings_qs.aggregate(Sum("earning")).values())[0]
        self.total_winnings_amount_banker = list(self.banker_winnings_qs.aggregate(Sum("earning")).values())[0]
        self.total_winnings_amount_quika = list(self.quika_winnings_qs.aggregate(Sum("earning")).values())[0]

        # Game Types Winnings Percentage
        self.winnings_percentage_salary_4_life = (
            abs(
                (self.total_ticket_sales_amount_salary_4_life if self.total_ticket_sales_amount_salary_4_life else 0)
                - (self.total_winnings_amount_salary_4_life if self.total_winnings_amount_salary_4_life else 0.00)
            )
            / (self.total_ticket_sales_amount_salary_4_life if self.total_ticket_sales_amount_salary_4_life else 1)
            * 100
        )

        self.winnings_percentage_instant_cashout = (
            abs(
                (self.total_ticket_sales_amount_instant_cashout if self.total_ticket_sales_amount_instant_cashout else 0)
                - (self.total_winnings_amount_instant_cashout if self.total_winnings_amount_instant_cashout else 0.00)
            )
            / (self.total_ticket_sales_amount_instant_cashout if self.total_ticket_sales_amount_instant_cashout else 1)
            * 100
        )

        self.winnings_percentage_wysecash = (
            abs(
                (self.total_ticket_sales_amount_wysecash if self.total_ticket_sales_amount_wysecash else 0)
                - (self.total_winnings_amount_wysecash if self.total_winnings_amount_wysecash else 0.00)
            )
            / (self.total_ticket_sales_amount_wysecash if self.total_ticket_sales_amount_wysecash else 1)
            * 100
        )

        self.winnings_percentage_soccercash = (
            abs(
                (self.total_ticket_sales_amount_soccercash if self.total_ticket_sales_amount_soccercash else 0)
                - (self.total_winnings_amount_soccercash if self.total_winnings_amount_soccercash else 0.00)
            )
            / (self.total_ticket_sales_amount_soccercash if self.total_ticket_sales_amount_soccercash else 1)
            * 100
        )

        self.winnings_percentage_banker = (
            abs(
                (self.total_ticket_sales_amount_banker if self.total_ticket_sales_amount_banker else 0)
                - (self.total_winnings_amount_banker if self.total_winnings_amount_banker else 0.00)
            )
            / (self.total_ticket_sales_amount_banker if self.total_ticket_sales_amount_banker else 1)
            * 100
        )

        self.winnings_percentage_quika = (
            abs(
                (self.total_ticket_sales_amount_quika if self.total_ticket_sales_amount_quika else 0)
                - (self.total_winnings_amount_quika if self.total_winnings_amount_quika else 0.00)
            )
            / (self.total_ticket_sales_amount_quika if self.total_ticket_sales_amount_quika else 1)
            * 100
        )

        # Change
        try:
            self.change_percentage_salary_4_life = (
                "up" if self.total_ticket_sales_amount_salary_4_life > self.total_winnings_amount_salary_4_life else "down"
            )
        except Exception:
            self.change_percentage_salary_4_life = "None"

        try:
            self.change_percentage_instant_cashout = (
                "up" if self.total_ticket_sales_amount_instant_cashout > self.total_winnings_amount_instant_cashout else "down"
            )
        except Exception:
            self.change_percentage_instant_cashout = "None"

        try:
            self.change_percentage_wysecash = "up" if self.total_ticket_sales_amount_wysecash > self.total_winnings_amount_wysecash else "down"
        except Exception:
            self.change_percentage_wysecash = "None"

        try:
            self.change_percentage_soccercash = "up" if self.total_ticket_sales_amount_soccercash > self.total_winnings_amount_soccercash else "down"
        except Exception:
            self.change_percentage_soccercash = "None"

        try:
            self.change_percentage_banker = "up" if self.total_ticket_sales_amount_banker > self.total_winnings_amount_banker else "down"
        except Exception:
            self.change_percentage_banker = "None"

        try:
            self.change_percentage_quika = "up" if self.total_ticket_sales_amount_quika > self.total_winnings_amount_quika else "down"
        except Exception:
            self.change_percentage_quika = "None"

        # Inflow
        inflow_list = ["REMITTANCE", "VFD_ACCOUNT_FUNDING", "LIBERTY_PAY_LOTTO_CHARGE", "WALLET", "REMITTANCE_EXCESS"]
        inflow_qs = AgentWalletTransaction.objects.all().filter(status="SUCCESSFUL", transaction_from__in=inflow_list)
        # CREDITS - WINNINGS
        # TRANSACTION_FROM = remittance_excess, remittance, wallet, vfd, liberty_pay_lotto_charge,

        # Inflow Amount
        self.inflow_amount_today = list(inflow_qs.filter(date_created__date=date_today_date).aggregate(Sum("amount")).values())[0]
        self.inflow_amount_week = list(inflow_qs.filter(date_created__gte=week_start).aggregate(Sum("amount")).values())[0]
        self.inflow_amount_month = list(inflow_qs.filter(date_created__gte=month_start).aggregate(Sum("amount")).values())[0]
        self.inflow_amount_year = list(inflow_qs.filter(date_created__gte=year_start).aggregate(Sum("amount")).values())[0]
        self.inflow_amount_last_year = list(
            inflow_qs.filter(date_created__gte=previous_year_start, date_created__lte=previous_year_end).aggregate(Sum("amount")).values()
        )[0]
        self.inflow_amount_last_month = list(
            inflow_qs.filter(date_created__gte=previous_month_start, date_created__lte=previous_month_end).aggregate(Sum("amount")).values()
        )[0]

        # Remittances ---> AgentWalletTransaction
        remittance_qs = agents_remittance_queryset.filter(remitted=True)
        self.remittance_amount_today = list(remittance_qs.filter(created_at__date=date_today_date).aggregate(Sum("amount")).values())[0]
        self.remittance_amount_week = list(remittance_qs.filter(created_at__gte=week_start).aggregate(Sum("amount")).values())[0]
        self.remittance_amount_month = list(remittance_qs.filter(created_at__gte=month_start).aggregate(Sum("amount")).values())[0]
        self.remittance_amount_year = list(remittance_qs.filter(created_at__gte=year_start).aggregate(Sum("amount")).values())[0]
        self.remittance_amount_last_year = list(
            remittance_qs.filter(created_at__gte=previous_year_start, created_at__lte=previous_year_end).aggregate(Sum("amount")).values()
        )[0]
        self.remittance_amount_last_month = list(
            remittance_qs.filter(created_at__gte=previous_month_start, created_at__lte=previous_month_end).aggregate(Sum("amount")).values()
        )[0]

        # For remittances paid on the respective dates
        self.remittance_paid_qs = AgentWalletTransaction.objects.all().filter(
            transaction_from__in=["REMITTANCE", "REMITTANCE_EXCESS"], status="SUCCESSFUL"
        )
        self.remittance_amount_paid_today = list(
            self.remittance_paid_qs.filter(date_created__date=date_today_date).aggregate(Sum("amount")).values()
        )[0]
        self.remittance_amount_paid_week = list(self.remittance_paid_qs.filter(date_created__gte=week_start).aggregate(Sum("amount")).values())[0]
        self.remittance_amount_paid_month = list(self.remittance_paid_qs.filter(date_created__gte=month_start).aggregate(Sum("amount")).values())[0]
        self.remittance_amount_paid_year = list(self.remittance_paid_qs.filter(date_created__gte=year_start).aggregate(Sum("amount")).values())[0]
        self.remittance_amount_paid_last_year = list(
            self.remittance_paid_qs.filter(date_created__gte=previous_year_start, date_created__lte=previous_year_end)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        self.remittance_amount_paid_last_month = list(
            self.remittance_paid_qs.filter(date_created__gte=previous_month_start, date_created__lte=previous_month_end)
            .aggregate(Sum("amount"))
            .values()
        )[0]

        # Remittances Created on respective Dates
        self.remittance_created_qs = agents_remittance_queryset.filter()
        self.remittance_created_amount_today = list(
            self.remittance_created_qs.filter(created_at__date=date_today_date).aggregate(Sum("amount")).values()
        )[0]
        self.remittance_created_amount_week = list(self.remittance_created_qs.filter(created_at__gte=week_start).aggregate(Sum("amount")).values())[0]
        self.remittance_created_amount_month = list(self.remittance_created_qs.filter(created_at__gte=month_start).aggregate(Sum("amount")).values())[
            0
        ]
        self.remittance_created_amount_year = list(self.remittance_created_qs.filter(created_at__gte=year_start).aggregate(Sum("amount")).values())[0]
        self.remittance_created_amount_last_year = list(
            self.remittance_created_qs.filter(created_at__gte=previous_year_start, created_at__lte=previous_year_end)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        self.remittance_created_amount_last_month = list(
            self.remittance_created_qs.filter(created_at__gte=previous_month_start, created_at__lte=previous_month_end)
            .aggregate(Sum("amount"))
            .values()
        )[0]

    def get_total_tickets_sold_amount(self):
        data = {
            "total_tickets_sold": {
                "today": self.ticket_sold_amount_today if self.ticket_sold_amount_today else 0.00,
                "this_week": self.ticket_sold_amount_week if self.ticket_sold_amount_week else 0.00,
                "this_month": self.ticket_sold_amount_month if self.ticket_sold_amount_month else 0.00,
                "last_month": self.ticket_sold_amount_last_month if self.ticket_sold_amount_last_month else 0.00,
                "this_year": self.ticket_sold_amount_year if self.ticket_sold_amount_year else 0.00,
                "last_year": self.ticket_sold_amount_last_year if self.ticket_sold_amount_last_year else 0.00,
            },
            "total_tickets_sold_web": {
                "total": self.total_tickets_sold_web_amount if self.total_tickets_sold_web_amount else 0.00,
                "today": self.total_tickets_sold_web_amount_today if self.total_tickets_sold_web_amount_today else 0.00,
                "this_week": self.total_tickets_sold_web_amount_week if self.total_tickets_sold_web_amount_week else 0.00,
                "this_month": self.total_tickets_sold_web_amount_month if self.total_tickets_sold_web_amount_month else 0.00,
                "last_month": self.total_tickets_sold_web_amount_last_month if self.total_tickets_sold_web_amount_last_month else 0.00,
                "this_year": self.total_tickets_sold_web_amount_year if self.total_tickets_sold_web_amount_year else 0.00,
                "last_year": self.total_tickets_sold_web_amount_last_year if self.total_tickets_sold_web_amount_last_year else 0.00,
            },
            "total_tickets_sold_ussd": {
                "total": self.total_tickets_sold_ussd_amount if self.total_tickets_sold_ussd_amount else 0.00,
                "today": self.total_tickets_sold_ussd_amount_today if self.total_tickets_sold_ussd_amount_today else 0.00,
                "this_week": self.total_tickets_sold_ussd_amount_week if self.total_tickets_sold_ussd_amount_week else 0.00,
                "this_month": self.total_tickets_sold_ussd_amount_month if self.total_tickets_sold_ussd_amount_month else 0.00,
                "last_month": self.total_tickets_sold_ussd_amount_last_month if self.total_tickets_sold_ussd_amount_last_month else 0.00,
                "this_year": self.total_tickets_sold_ussd_amount_year if self.total_tickets_sold_ussd_amount_year else 0.00,
                "last_year": self.total_tickets_sold_ussd_amount_last_year if self.total_tickets_sold_ussd_amount_last_year else 0.00,
            },
            "total_tickets_sold_web_pos": {
                "today": self.total_tickets_sold_web_pos_amount_today if self.total_tickets_sold_web_pos_amount_today else 0.00,
                "this_week": self.total_tickets_sold_web_pos_amount_week if self.total_tickets_sold_web_pos_amount_week else 0.00,
                "this_month": self.total_tickets_sold_web_pos_amount_month if self.total_tickets_sold_web_pos_amount_month else 0.00,
                "last_month": self.total_tickets_sold_web_pos_amount_last_month if self.total_tickets_sold_web_pos_amount_last_month else 0.00,
                "this_year": self.total_tickets_sold_web_pos_amount_year if self.total_tickets_sold_web_pos_amount_year else 0.00,
                "last_year": self.total_tickets_sold_web_pos_amount_last_year if self.total_tickets_sold_web_pos_amount_last_year else 0.00,
            },
        }
        return data

    def get_total_tickets_sold_count(self):
        data = {
            "total_tickets_sold_count": {
                "today": self.ticket_sold_count_today,
                "this_week": self.ticket_sold_count_week,
                "this_month": self.ticket_sold_count_month,
                "last_month": self.ticket_sold_count_last_month,
                "this_year": self.ticket_sold_count_year,
                "last_year": self.ticket_sold_count_last_year,
            },
            "total_tickets_sold_web_count": {
                "today": self.total_tickets_sold_web_count_today,
                "this_week": self.total_tickets_sold_web_count_week,
                "this_month": self.total_tickets_sold_web_count_month,
                "last_month": self.total_tickets_sold_web_count_last_month,
                "this_year": self.total_tickets_sold_web_count_year,
                "last_year": self.total_tickets_sold_web_count_last_year,
            },
            "total_tickets_sold_ussd_count": {
                "today": self.total_tickets_sold_ussd_count_today,
                "this_week": self.total_tickets_sold_ussd_count_week,
                "this_month": self.total_tickets_sold_ussd_count_month,
                "last_month": self.total_tickets_sold_ussd_count_last_month,
                "this_year": self.total_tickets_sold_ussd_count_year,
                "last_year": self.total_tickets_sold_ussd_count_last_year,
            },
            "total_tickets_sold_web_pos_count": {
                "today": self.total_tickets_sold_web_pos_count_today,
                "this_week": self.total_tickets_sold_web_pos_count_week,
                "this_month": self.total_tickets_sold_web_pos_count_month,
                "last_month": self.total_tickets_sold_web_pos_count_last_month,
                "this_year": self.total_tickets_sold_web_pos_count_year,
                "last_year": self.total_tickets_sold_web_pos_count_last_year,
            },
        }
        return data

    def get_payout_data(self):
        data = {
            "total_payouts": {
                "today": self.payouts_amount_today if self.payouts_amount_today else 0.00,
                "this_week": self.payouts_amount_week if self.payouts_amount_week else 0.00,
                "this_month": self.payouts_amount_month if self.payouts_amount_month else 0.00,
                "last_month": self.payouts_amount_last_month if self.payouts_amount_last_month else 0.00,
                "this_year": self.payouts_amount_year if self.payouts_amount_year else 0.00,
                "last_year": self.payouts_amount_last_year if self.payouts_amount_last_year else 0.00,
            },
            "total_payouts_web": {
                "today": self.payouts_web_amount_today if self.payouts_web_amount_today else 0.00,
                "this_week": self.payouts_web_amount_week if self.payouts_web_amount_week else 0.00,
                "this_month": self.payouts_web_amount_month if self.payouts_web_amount_month else 0.00,
                "last_month": self.payouts_web_amount_last_month if self.payouts_web_amount_last_month else 0.00,
                "this_year": self.payouts_web_amount_year if self.payouts_web_amount_year else 0.00,
                "last_year": self.payouts_web_amount_last_year if self.payouts_web_amount_last_year else 0.00,
            },
            "total_payouts_ussd": {
                "today": self.payouts_ussd_amount_today if self.payouts_ussd_amount_today else 0.00,
                "this_week": self.payouts_ussd_amount_week if self.payouts_ussd_amount_week else 0.00,
                "this_month": self.payouts_ussd_amount_month if self.payouts_ussd_amount_month else 0.00,
                "last_month": self.payouts_ussd_amount_last_month if self.payouts_ussd_amount_last_month else 0.00,
                "this_year": self.payouts_ussd_amount_year if self.payouts_ussd_amount_year else 0.00,
                "last_year": self.payouts_ussd_amount_last_year if self.payouts_ussd_amount_last_year else 0.00,
            },
            "total_payouts_pos": {
                "today": self.payouts_pos_amount_today if self.payouts_pos_amount_today else 0.00,
                "this_week": self.payouts_pos_amount_week if self.payouts_pos_amount_week else 0.00,
                "this_month": self.payouts_pos_amount_month if self.payouts_pos_amount_month else 0.00,
                "last_month": self.payouts_pos_amount_last_month if self.payouts_pos_amount_last_month else 0.00,
                "this_year": self.payouts_pos_amount_year if self.payouts_pos_amount_year else 0.00,
                "last_year": self.payouts_pos_amount_last_year if self.payouts_pos_amount_last_year else 0.00,
            },
        }
        return data

    def get_remittance_data(self):
        data = {
            "total_remittance": {
                "today": self.remittance_amount_today if self.remittance_amount_today else 0.00,
                "this_week": self.remittance_amount_week if self.remittance_amount_week else 0.00,
                "this_month": self.remittance_amount_month if self.remittance_amount_month else 0.00,
                "last_month": self.remittance_amount_last_month if self.remittance_amount_last_month else 0.00,
                "this_year": self.remittance_amount_year if self.remittance_amount_year else 0.00,
                "last_year": self.remittance_amount_last_year if self.remittance_amount_last_year else 0.00,
                "remittance_created_paid_today": self.remittance_amount_today if self.remittance_amount_today else 0.00,
                "remittance_paid_today": self.remittance_amount_paid_today if self.remittance_amount_paid_today else 0.00,
                "remittance_created_paid_this_week": self.remittance_amount_week if self.remittance_amount_week else 0.00,
                "remittance_paid_this_week": self.remittance_amount_paid_week if self.remittance_amount_paid_week else 0.00,
                "remittance_created_paid_this_this_month": self.remittance_amount_month if self.remittance_amount_month else 0.00,
                "remittance_paid_this_this_month": self.remittance_amount_paid_month if self.remittance_amount_paid_month else 0.00,
                "remittance_created_paid_last_month": self.remittance_amount_last_month if self.remittance_amount_last_month else 0.00,
                "remittance_paid_last_month": self.remittance_amount_paid_last_month if self.remittance_amount_paid_last_month else 0.00,
                "remittance_created_paid_year": self.remittance_amount_paid_year if self.remittance_amount_paid_year else 0.00,
                "remittance_paid_year": self.remittance_amount_paid_year if self.remittance_amount_paid_year else 0.00,
                "remittance_created_paid_last_year": self.remittance_amount_last_year if self.remittance_amount_last_year else 0.00,
                "remittance_paid_last_year": self.remittance_amount_paid_last_year if self.remittance_amount_paid_last_year else 0.00,
                # Remittances Created
                "remittance_created_amount_today": self.remittance_created_amount_today,
                "remittance_created_amount_week": self.remittance_created_amount_week,
                "remittance_created_amount_month": self.remittance_created_amount_month,
                "remittance_created_amount_year": self.remittance_created_amount_year,
                "remittance_created_amount_last_year": self.remittance_created_amount_last_year,
                "remittance_created_amount_last_month": self.remittance_created_amount_last_month,
            },
        }
        return data

    def get_terminals_count(self):
        data = {
            "total_terminal": {
                "total_terminals_count": self.total_terminals_count,
                "winwise_agents": self.winwise_agents_terminals_count,
                "other_lotto_agents": self.other_lotto_agents_terminals_count,
                "percentages": {
                    "winwise_agents": self.winwise_agents_terminals_count_percentage,
                    "other_lotto_agents": self.other_lotto_agents_terminals_percentage,
                },
            }
        }
        return data

    def get_inflows_data(self):
        data = {
            "total_inflow": {
                "today": self.inflow_amount_today if self.inflow_amount_today else 0.00,
                "this_week": self.inflow_amount_week if self.inflow_amount_week else 0.00,
                "this_month": self.inflow_amount_month if self.inflow_amount_month else 0.00,
                "last_month": self.inflow_amount_last_month if self.inflow_amount_last_month else 0.00,
                "this_year": self.inflow_amount_year if self.inflow_amount_year else 0.00,
                "last_year": self.inflow_amount_last_year if self.inflow_amount_last_year else 0.00,
            }
        }
        return data

    def get_wallet_metrics(self):
        data = {
            "wallet_balance": {
                "overall_wallet_balance": self.overall_wallet_balance or 0.00,
                "overall_game_play_wallet_balance": self.overall_game_play_wallet_balance or 0.00,
                "overall_winnings_wallet_balance": self.overall_winnings_wallet_balance or 0.00,
                "winwise_agents": self.winwise_agents_overall_wallet_balance or 0.00,
                "winwise_agents_game_play_balance": self.winwise_agents_game_play_wallet_balance or 0.00,
                "winwise_agents_winnings_balance": self.winwise_agents_winnings_wallet_balance or 0.00,
                "other_lotto_agents": self.other_lotto_agents_overall_wallet_balance or 0.00,
                "other_lotto_agents_game_play_balance": self.other_lotto_agents_game_play_wallet_balance or 0.00,
                "other_lotto_agents_winnings_balance": self.other_lotto_agents_winnings_wallet_balance or 0.00,
                "percentages": {
                    "winwise_agents": self.winwise_agents_percentage,
                    "other_lotto_agents": self.other_lotto_agents_percentage
                    },
                "game_play_balance_percentages": {
                    "winwise_agents": self.winwise_agents_game_play_percentage,
                    "other_lotto_agents": self.other_lotto_agents_game_play_percentage,
                },
                "winnings_balance_percentages": {
                    "winwise_agents": self.winwise_agents_winnings_percentage,
                    "other_lotto_agents": self.other_lotto_agents_winnings_percentage,
                },
            }
        }
        return data

    def get_failed_transactions_amount(self):
        data = {
            "failed_transactions": {
                "cashout_amount": self.failed_cashout_transactions_amount or 0.00,
                "game_play_amount": self.failed_game_play_transactions_amount or 0.00,
                "game_play_this_year": self.failed_game_play_transactions_amount_year or 0.00,
                "cashout_count": self.failed_cashout_transactions_count,
                "game_play_count": self.failed_game_play_transactions_count,
                "game_play_this_year_count": self.failed_game_play_transactions_count_year,
            }
        }
        return data

    def get_transaction_comparatives(self):
        data = {
            "transaction_comparatives": {
                "remittance": self.remittance_amount if self.remittance_amount else 0.00,
                "game_plays": self.game_play_amount if self.game_play_amount else 0.00,
                "winnings": self.winnings_amount if self.winnings_amount else 0.00,
                "percentages": {
                    "game_plays": self.game_play_amount_percentage,
                    "winnings": self.winnings_amount_percentage,
                    "remittance": self.remittance_amount_percentage,
                },
            }
        }
        return data

    def get_transactions_count(self):
        data = {
            "transaction_count": {
                "total_transaction_count": self.total_transactions_count,
                "cashout": self.cashout_transactions_count,
                "game_plays": self.game_play_transactions_count,
                "winnings": self.winnings_transactions_count,
            }
        }
        return data

    def get_transactions_metrics(self):
        data = {
            "transactions": {
                "total_transactions_amount": self.total_transactions_amount or 0.00,
                "ticket_sales": self.ticket_sales_amount or 0.00,
                "remittance": self.remittance_amount or 0.00,
                "percentages": {
                    "ticket_sales": self.ticket_sales_amount_percentage,
                    "remittance": self.remittance_amount_percentage
                },
            },
            "average_agent_transactions": {
                "average_agent_transaction_amount": self.average_agents_transactions_amount or 0.00,
                "average_agent_transaction_amount_this_month": self.average_agents_transactions_amount_this_month or 0.00,
                "percentages": {
                    "percentage": self.average_agents_transactions_amount_percentage_change.get("percentage"),
                    "change": self.average_agents_transactions_amount_percentage_change.get("change"),
                },
            },
        }
        return data

    def get_average_sales(self):
        data = {
            "average_sales": {
                "average_sales_per_agent": self.average_agent_sales_amount or 0.00,
                "average_sales_per_agent_this_month": self.average_sales_this_month_amount or 0.00,
                "average_sales_today_amount": self.average_sales_today_amount,
                "average_sales_this_week_amount": self.average_sales_this_week_amount,
                "average_sales_this_month_amount": self.average_sales_this_month_amount,
                "average_sales_last_month_amount": self.average_sales_last_month_amount,
                "average_sales_year_amount": self.average_sales_year_amount,
                "average_sales_previous_year_amount": self.average_sales_previous_year_amount,
                "percentages": {
                    "percentage": self.overall_overall_sales_amount_percentage.get("percentage"),
                    "change": self.overall_overall_sales_amount_percentage.get("change"),
                },
            },
            "average_ticket_sales": {
                "average_ticket_sales_count": self.average_agent_sales_count,
                "average_ticket_sales_count_this_month": self.average_agent_sales_count_this_month,
                "avereage_ticket_sold_count_today": self.avereage_ticket_sold_count_today,
                "avereage_ticket_sold_count_week": self.avereage_ticket_sold_count_week,
                "avereage_ticket_sold_count_month": self.avereage_ticket_sold_count_month,
                "avereage_ticket_sold_count_year": self.avereage_ticket_sold_count_year,
                "avereage_ticket_sold_count_last_year": self.avereage_ticket_sold_count_last_year,
                "avereage_ticket_sold_count_last_month": self.avereage_ticket_sold_count_last_month,
                "avereage_ticket_sold_count_this_month": self.avereage_ticket_sold_count_this_month,
                "percentages": {
                    "percentage": self.average_agent_sales_count_change.get("percentage"),
                    "change": self.average_agent_sales_count_change.get("change"),
                },
            },
        }
        return data

    def get_commission_metrics(self):
        data = {
            "commissions": {
                "commission_amount": self.total_commission_amount or 0.00,
                "commission_amount_this_month": self.total_commission_amount_this_month or 0.00,
                "paid_commission": self.paid_commission_amount or 0.00,
                "paid_commission_this_month": self.paid_commission_amount_this_month or 0.00,
                "retained_commission": self.retained_commission_amount or 0.00,
                "retained_commission_this_month": self.retained_commission_amount_this_month or 0.00,
                "percentages": {
                    "percentage": self.total_commission_amount_change.get("percentage"),
                    "change": self.total_commission_amount_change.get("change"),
                },
            }
        }
        return data

    def get_sales_metrics(self):
        data = {
            "total_players": {
                "count": self.total_players_count,
                "percentages": {"percentage": self.total_players_change.get("percentage"), "change": self.total_players_change.get("change")},
            },
            "new_players": {
                "count": self.new_players_count,
                "percentages": {"percentage": self.new_players_change.get("percentage"), "change": self.new_players_change.get("change")},
            },
            "sales": {
                "total_sales_all_time": self.overall_sales_amount or 0.00,
                "winwise_agents": self.winwise_lotto_agents_sales_amount or 0.00,
                "other_lotto_agents": self.other_lotto_agents_sales_amount or 0.00,
                "percentages": {
                    "winwise_agents": self.winwise_agents_percentage,
                    "other_lotto_agents": self.other_lotto_agents_percentage,
                },
            },
            "agents_salary": {
                "salary_amount": self.agent_salary_amount or 0.00,
                "salary_amount_this_month": self.agent_salary_amount_this_month or 0.00,
                "percentages": {"percentage": 0.00, "change": "up"},
            },
            "sms_cost": {
                "sms_cost_amount": self.sms_cost_amount or 0.00,
                "percentages": {"percentage": self.sms_charge_change.get("percentage"), "change": self.sms_charge_change.get("change")},
            },
            "average_work_hours": {"average_work_hours": self.average_work_hours, "percentages": {"percentage": 0.00, "change": "up"}},
        }
        return data

    def get_sales_winnings_comparison(self):
        data = {
            # Game Type Ticket Sales Amount
            "total_ticket_sales_amount_salary_4_life": (
                self.total_ticket_sales_amount_salary_4_life or 0.00
            ),
            "total_ticket_sales_amount_instant_cashout": (
                self.total_ticket_sales_amount_instant_cashout or 0.00
            ),
            "total_ticket_sales_amount_wysecash": self.total_ticket_sales_amount_wysecash or 0.00,
            "total_ticket_sales_amount_soccercash": self.total_ticket_sales_amount_soccercash or 0.00,
            "total_ticket_sales_amount_banker": self.total_ticket_sales_amount_banker or 0.00,
            "total_ticket_sales_amount_quika": self.total_ticket_sales_amount_quika or 0.00,
            # Game Type Winnings Amount
            "total_winnings_amount_salary_4_life": self.total_winnings_amount_salary_4_life or 0.00,
            "total_winnings_amount_instant_cashout": self.total_winnings_amount_instant_cashout or 0.00,
            "total_winnings_amount_wysecash": self.total_winnings_amount_wysecash or 0.00,
            "total_winnings_amount_soccercash": self.total_winnings_amount_soccercash or 0.00,
            "total_winnings_amount_banker": self.total_winnings_amount_banker or 0.00,
            "total_winnings_amount_quika": self.total_winnings_amount_quika or 0.00,
            # Game Type Winnings Percentage
            "margin_percentage_salary_4_life": self.winnings_percentage_salary_4_life,
            "change_percentage_salary_4_life": self.change_percentage_salary_4_life,
            "margin_percentage_instant_cashout": self.winnings_percentage_instant_cashout,
            "change_percentage_instant_cashout": self.change_percentage_instant_cashout,
            "margin_percentage_wysecash": self.winnings_percentage_wysecash,
            "change_percentage_wysecash": self.change_percentage_wysecash,
            "margin_percentage_soccercash": self.winnings_percentage_soccercash,
            "change_percentage_soccercash": self.change_percentage_soccercash,
            "margin_percentage_banker": self.winnings_percentage_banker,
            "change_percentage_banker": self.change_percentage_banker,
            "margin_percentage_quika": self.winnings_percentage_quika,
            "change_percentage_quika": self.change_percentage_quika,
        }
        return data

    def get_tickets_bought_range(self):
        data = {
            "salary_4_life_350_700": self.salary_4_life_350_700_count,
            "instant_cashout_200_600": self.instant_cashout_200_600_count,
            "wysecash_200_600": self.wysecash_200_600_count,
            "soccercash_200_600": self.soccercash_200_600_count,
            "banker_200_600": self.banker_200_600_count,
            "quika_200_600": self.quika_200_600_count,
            "average_ticket_sales_salary_4_life": self.average_ticket_sales_salary_4_life or 0.00,
            "average_ticket_sales_instant_cashout": self.average_ticket_sales_instant_cashout or 0.00,
            "average_ticket_sales_wysecash": self.average_ticket_sales_wysecash or 0.00,
            "average_ticket_sales_soccercash": self.average_ticket_sales_soccercash or 0.00,
            "average_ticket_sales_banker": self.average_ticket_sales_banker or 0.00,
            "average_ticket_sales_quika": self.average_ticket_sales_quika or 0.00,
        }
        return data


class DashboardChart:
    def __init__(self, request):
        self.date_filter = DateUtility().get_filter(request).get("date_filter")
        self.date_filter_two = DateUtility().get_filter(request).get("date_filter_two")
        self.date_filter_three = DateUtility().get_filter(request).get("date_filter_three")
        self.date_filter_four = DateUtility().get_filter(request).get("date_filter_four")
        self.date_filter_five = DateUtility().get_filter(request).get("date_filter_five")

        DateUtility().date_today_date
        DateUtility().week_start
        DateUtility().month_start
        DateUtility().previous_month_start
        DateUtility().previous_month_end
        DateUtility().year_start
        DateUtility().previous_year_start
        DateUtility().previous_year_end
        previous_year_current_month_start = DateUtility().previous_year_current_month_start
        months_list = DateUtility().months_list
        date_today = DateUtility().date_today
        year_months_tuple_list = DateUtility().year_months_tuple_list
        self.months_list_names = DateUtility().months_list_names

        tickets_sold_qs = (
            AgentWalletTransaction.objects.all()
            .filter(
                date_created__gte=previous_year_current_month_start,
                status="SUCCESSFUL",
                transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"],
            )
            .filter(**self.date_filter)
        )
        agents_remittance_queryset = LottoAgentRemittanceTable.objects.all()

        # Sales Count Chart for Winwise agents
        winwise_agents_monthly_sales_chart = (
            tickets_sold_qs.filter(agent_wallet__agent__first_name__startswith="Win wise")
            .values("date_created__year", "date_created__month")
            .annotate(total_sales=Count("amount"))
        )
        winwise_sales_past_year_list = [
            (obj["date_created__year"], obj["date_created__month"], obj["total_sales"]) for obj in winwise_agents_monthly_sales_chart
        ]

        all_month_winwise_sales_count = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if winwise_sales_past_year_list:
                for transaction in (
                    winwise_sales_past_year_list[: len(winwise_sales_past_year_list) - 1]
                    if winwise_sales_past_year_list[-1][1] == date_today.month
                    else winwise_sales_past_year_list
                ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_winwise_sales_count.append(month_sum)
        current_month_sales_count = (
            winwise_sales_past_year_list[-1][2] if winwise_sales_past_year_list and winwise_sales_past_year_list[-1][1] == date_today.month else 0
        )
        all_month_winwise_sales_count.append(current_month_sales_count)

        self.all_month_winwise_sales_count = all_month_winwise_sales_count
        self.winwise_overall_tickets_sold_count = (
            AgentWalletTransaction.objects.all()
            .filter(agent_wallet__agent__first_name__startswith="Win wise", transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"])
            .count()
        )

        # Sales Count Chart for Other Lotto agents
        other_agents_monthly_sales_chart = (
            tickets_sold_qs.exclude(agent_wallet__agent__first_name__startswith="Win wise")
            .values("date_created__year", "date_created__month")
            .annotate(total_sales=Count("amount"))
        )
        other_agents_sales_past_year_list = [
            (obj["date_created__year"], obj["date_created__month"], obj["total_sales"]) for obj in other_agents_monthly_sales_chart
        ]

        all_month_other_agents_sales_count = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if other_agents_sales_past_year_list:
                for transaction in (
                    other_agents_sales_past_year_list[: len(other_agents_sales_past_year_list) - 1]
                    if other_agents_sales_past_year_list[-1][1] == date_today.month
                    else other_agents_sales_past_year_list
                ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_other_agents_sales_count.append(month_sum)
        current_month_sales_count = (
            other_agents_sales_past_year_list[-1][2]
            if other_agents_sales_past_year_list and other_agents_sales_past_year_list[-1][1] == date_today.month
            else 0
        )
        all_month_other_agents_sales_count.append(current_month_sales_count)

        self.all_month_other_agents_sales_count = all_month_other_agents_sales_count
        self.other_agents_overall_tickets_sold_count = (
            AgentWalletTransaction.objects.all().exclude(agent_wallet__agent__first_name__startswith="Win wise").count()
        )

        # Commission Amount Chart
        commission_qs = (
            AgentWalletTransaction.objects.all().filter(transaction_from="COMMISSION_ON_GAME_PLAY", status="SUCCESSFUL").filter(**self.date_filter)
        )
        commission_monthly_chart = commission_qs.values("date_created__year", "date_created__month").annotate(total_amount=Sum("amount"))
        commission_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["total_amount"]) for obj in commission_monthly_chart]

        all_month_commission_amount = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if commission_past_year_list:
                for transaction in (
                    commission_past_year_list[: len(commission_past_year_list) - 1]
                    if commission_past_year_list[-1][1] == date_today.month
                    else commission_past_year_list
                ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_commission_amount.append(month_sum)
        current_month_comission_amount = (
            commission_past_year_list[-1][2] if commission_past_year_list and commission_past_year_list[-1][1] == date_today.month else 0
        )
        all_month_commission_amount.append(current_month_comission_amount)

        self.all_month_commission_amount = all_month_commission_amount

        # Game Play Amount Chart
        game_play_qs = LottoTicket.objects.all().filter(paid=True).filter(**self.date_filter_two)
        wyse_cash_game_play_qs = LotteryModel.objects.filter(paid=True, lottery_type="WYSE_CASH").filter(**self.date_filter_two)
        game_play_monthly_chart = game_play_qs.values("date__year", "date__month").annotate(total_amount=Sum("stake_amount"))
        game_play_past_year_list = [(obj["date__year"], obj["date__month"], obj["total_amount"]) for obj in game_play_monthly_chart]

        all_month_game_play_amount = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if game_play_past_year_list:
                for transaction in (
                    game_play_past_year_list[: len(game_play_past_year_list) - 1]
                    if game_play_past_year_list[-1][1] == date_today.month
                    else game_play_past_year_list
                ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_game_play_amount.append(month_sum)
        current_month_game_play_amount = (
            game_play_past_year_list[-1][2] if game_play_past_year_list and game_play_past_year_list[-1][1] == date_today.month else 0
        )
        all_month_game_play_amount.append(current_month_game_play_amount)

        self.all_month_game_play_amount = all_month_game_play_amount

        # Remittances Amount Chart
        remittance_qs = agents_remittance_queryset.filter(remitted=True).filter(**self.date_filter_three)
        remittance_monthly_chart = remittance_qs.values("created_at__year", "created_at__month").annotate(total_amount=Sum("amount"))
        [(obj["created_at__year"], obj["created_at__month"], obj["total_amount"]) for obj in remittance_monthly_chart]
        past_year_remittance_list = [(data["created_at__year"], data["created_at__month"], data["total_amount"]) for data in remittance_monthly_chart]
        past_year_remittance_final_list = []
        for item in year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_remittance_list]:
                for trans in past_year_remittance_list:
                    if item == (trans[0], trans[1]):
                        past_year_remittance_final_list.append(trans)
                        break
            else:
                past_year_remittance_final_list.append((0, 0, 0))
        self.remittance_amount_values_list = [trans[2] for trans in past_year_remittance_final_list]

        # Winnings Amount Chart
        winning_qs = AgentWalletTransaction.objects.all().filter(transaction_from="WINNINGS", status="SUCCESSFUL").filter(**self.date_filter)
        winning_monthly_chart = winning_qs.values("date_created__year", "date_created__month").annotate(total_amount=Sum("amount"))
        winning_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["total_amount"]) for obj in winning_monthly_chart]

        all_month_winning_amount = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if winning_past_year_list:
                for transaction in (
                    winning_past_year_list[: len(winning_past_year_list) - 1]
                    if winning_past_year_list[-1][1] == date_today.month
                    else winning_past_year_list
                ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_winning_amount.append(month_sum)
        current_month_winning_amount = (
            winning_past_year_list[-1][2] if winning_past_year_list and winning_past_year_list[-1][1] == date_today.month else 0
        )
        all_month_winning_amount.append(current_month_winning_amount)

        self.all_month_winning_amount = all_month_winning_amount

        # Games Comparatives Amount
        instant_cashout_amount = list(game_play_qs.filter(lottery_type="INSTANT_CASHOUT").aggregate(Sum("stake_amount")).values())[0]
        salary_4_life_amount = list(game_play_qs.filter(lottery_type="SALARY_FOR_LIFE").aggregate(Sum("stake_amount")).values())[0]
        wyse_cash_amount = list(wyse_cash_game_play_qs.aggregate(Sum("stake_amount")).values())[0]
        banker_amount = list(game_play_qs.filter(lottery_type="BANKER").aggregate(Sum("stake_amount")).values())[0]
        soccer_amount = list(game_play_qs.filter(lottery_type="VIRTUAL_SOCCER").aggregate(Sum("stake_amount")).values())[0]
        quicka_amount = list(game_play_qs.filter(lottery_type="QUIKA").aggregate(Sum("stake_amount")).values())[0]
        lifestyle_amount = list(game_play_qs.filter(lottery_type="LIFESTYLE").aggregate(Sum("stake_amount")).values())[0]

        self.game_comp_amount = [
            instant_cashout_amount or 0.00,
            salary_4_life_amount or 0.00,
            wyse_cash_amount or 0.00,
            banker_amount or 0.00,
            soccer_amount or 0.00,
            quicka_amount or 0.00,
            lifestyle_amount or 0.00,
        ]

        # Games Comparatives Count
        instant_cashout_count = game_play_qs.filter(lottery_type="INSTANT_CASHOUT").count()
        salary_4_life_count = game_play_qs.filter(lottery_type="SALARY_FOR_LIFE").count()
        wyse_cash_count = wyse_cash_game_play_qs.count()
        banker_count = game_play_qs.filter(lottery_type="BANKER").count()
        soccer_count = game_play_qs.filter(lottery_type="VIRTUAL_SOCCER").count()
        quicka_count = game_play_qs.filter(lottery_type="QUIKA").count()
        lifestyle_count = game_play_qs.filter(lottery_type="LIFESTYLE").count()

        self.game_comp_count = [
            instant_cashout_count,
            salary_4_life_count,
            wyse_cash_count,
            banker_count,
            soccer_count,
            quicka_count,
            lifestyle_count,
        ]

        # Salary Charts
        try:
            salary_amount = ConstantTable.objects.all().last().winwise_agents_salary_amount
        except Exception:
            salary_amount = 30000

        winwise_agents_monthly_chart = (
            Agent.objects.all()
            .filter(first_name__startswith="Win wise", created_date__gte=previous_year_current_month_start)
            .distinct()
            .values("id", "created_date__year", "created_date__month")
            .annotate(agents_count=Count("created_date__month"))
        )

        winwise_agents_past_year_list = [
            (obj["created_date__year"], obj["created_date__month"], obj["agents_count"]) for obj in winwise_agents_monthly_chart
        ]

        all_month_winwise_agents_count = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if winwise_agents_past_year_list:
                for transaction in (
                    winwise_agents_past_year_list[: len(winwise_agents_past_year_list) - 1]
                    if winwise_agents_past_year_list[-1][1] == date_today.month
                    else winwise_agents_past_year_list
                ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                month_sum = sum(month_transactions)
            all_month_winwise_agents_count.append(month_sum)
        current_month_agents_count = (
            winwise_agents_past_year_list[-1][2] if winwise_agents_past_year_list and winwise_agents_past_year_list[-1][1] == date_today.month else 0
        )
        all_month_winwise_agents_count.append(current_month_agents_count)

        self.all_month_winwise_agents_count = all_month_winwise_agents_count

        self.salary_values = [count * salary_amount for count in self.all_month_winwise_agents_count]

    def get_ticket_sales_count_chart(self):
        data = {
            "months": self.months_list_names,
            "amount_label": ["1M", "2M", "3M", "4M", "5M", "6M"],
            "winwise_agents": {"values": self.all_month_winwise_sales_count, "total_count": self.winwise_overall_tickets_sold_count},
            "other_agents": {"values": self.all_month_other_agents_sales_count, "total_count": self.other_agents_overall_tickets_sold_count},
            "percentages": {"percentage": 0.00, "change": "up"},
        }
        return data

    def get_commissions_salary_chart(self):
        data = {
            "months": self.months_list_names,
            "amount_label": ["1M", "2M", "3M", "4M", "5M", "6M"],
            "salary_values": self.salary_values,
            "commission_values": self.all_month_commission_amount,
            "total_amount": 0.00,
            "percentages": {"percentage": 0.00, "change": "up"},
        }
        return data

    def get_transation_comparative_chart(self):
        data = {
            "months": self.months_list_names,
            "amount_label": ["1M", "2M", "3M", "4M", "5M", "6M"],
            "game_play_values": self.all_month_game_play_amount,
            "remittance_values": self.remittance_amount_values_list,
            "winnings_values": self.all_month_winning_amount,
            "total_amount": 0.00,
            "percentages": {"percentage": 0.00, "change": "up"},
        }
        return data

    def get_games_comparatives_amount_graph(self):
        data = {
            "labels": ["Instant", "SAL", "Wyse", "Banker", "Soccer", "Quika", "Lifestyle"],
            "values": self.game_comp_amount,
            "percentages": {"percentage": 0.00, "change": "up"},
        }
        return data

    def get_games_comparatives_count_graph(self):
        data = {
            "labels": ["Instant", "SAL", "Wyse", "Banker", "Soccer", "Quika", "Lifestyle"],
            "values": self.game_comp_count,
            "percentages": {"percentage": 0.00, "change": "up"},
        }
        return data


# AGENTS SCREEN
class Agents:
    def __init__(self, request):
        self.date_filter = DateUtility().get_filter(request).get("date_filter")
        self.date_filter_two = DateUtility().get_filter(request).get("date_filter_two")
        self.date_filter_three = DateUtility().get_filter(request).get("date_filter_three")
        self.date_filter_four = DateUtility().get_filter(request).get("date_filter_four")
        self.date_filter_five = DateUtility().get_filter(request).get("date_filter_five")

        self.date_today_date = DateUtility().date_today_date
        self.week_start = DateUtility().week_start
        self.month_start = DateUtility().month_start
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
        self.months_list_names = DateUtility().months_list_names

        agents_remittance_queryset = LottoAgentRemittanceTable.objects.all()

        # Agents
        self.agents_qs = Agent.objects.all().filter(agent_type="LOTTO_AGENT", terminal_id__isnull=False, terminal_retrieved=False, supervisor__user_id=request.user.id)
        active_agents_qs = self.agents_qs.exclude(performance_status="INACTIVE")
        inactive_agents_qs = self.agents_qs.filter(performance_status="INACTIVE")
        new_agents_qs = self.agents_qs.filter(created_date__gte=self.month_start)
        self.agent_wallet_qs = AgentWallet.objects.all()

        self.all_agents_count = self.agents_qs.count()
        self.active_agents_count = active_agents_qs.count()
        self.inactive_agents_count = inactive_agents_qs.count()
        self.new_agents_count = new_agents_qs.count()

        # Transactions
        self.transactions_qs = (
            AgentWalletTransaction.objects.all()
            .filter(status="SUCCESSFUL", transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"])
            .filter(**self.date_filter)
        )
        # self.all_transactions_qs = AgentWalletTransaction.objects.all().filter(status="SUCCESSFUL").filter(**self.date_filter)
        self.all_transactions_qs = AgentWalletTransaction.objects.filter(Q(status="SUCCESSFUL") & Q(**self.date_filter) & Q(agent_wallet__agent__supervisor__user_id=request.user.id))
        self.winnings_withdrawn_qs = PosLotteryWinners.objects.all().filter(
            is_win_claimed=True, withdrawl_initiated=True, payout_successful=True, payout_verified=True
        )
        # transactions_qs_today = self.transactions_qs.filter(date_created__date=date_today_date)

        self.total_transactions_amount = list(self.all_transactions_qs.aggregate(Sum("amount")).values())[0]
        self.total_transactions_amount_today = list(
            self.all_transactions_qs.filter(date_created__date=self.date_today_date).aggregate(Sum("amount")).values()
        )[0]

        self.total_transactions_count = self.all_transactions_qs.count()
        self.total_transactions_count_today = self.all_transactions_qs.filter(date_created__date=self.date_today_date).count()

        self.tickets_sold_qs = (
            AgentWalletTransaction.objects.all()
            .filter(
                status="SUCCESSFUL",
                transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"],
                agent_wallet__agent__agent_type="LOTTO_AGENT",
                agent_wallet__agent__terminal_id__isnull=False,
                agent_wallet__agent__supervisor__user_id=request.user.id,
            )
            .values(
                "agent_wallet__agent__email",
                "agent_wallet__agent__first_name",
                "agent_wallet__agent__last_name",
                "agent_wallet__agent__id",
                "agent_wallet__agent__last_updated",
            )
            .annotate(
                email=F("agent_wallet__agent__email"),
                fullname=F("agent_wallet__agent__full_name"),
                agent_id=F("agent_wallet__agent__id"),
            )
            .values("agent_id", "email", "fullname")
            .filter(**self.date_filter)
        )

        discrete_tickets_sold_amount = self.tickets_sold_qs.filter(**self.date_filter).annotate(amount=Sum("amount"))
        discrete_tickets_sold_count = self.tickets_sold_qs.filter(**self.date_filter).annotate(count=Count("amount"))

        # Amount
        sorted_tickets_sold_amount_list = sorted(discrete_tickets_sold_amount, key=lambda d: d["amount"])[::-1]
        self.top10_tickets_sold_amount = sorted_tickets_sold_amount_list[:10]

        # Count
        sorted_tickets_sold_count_list = sorted(discrete_tickets_sold_count, key=lambda d: d["count"])[::-1]
        self.top10_tickets_sold_count = sorted_tickets_sold_count_list[:10]

        # Agent Details
        self.agent_details_qs = self.tickets_sold_qs.annotate(
            id=F("agent_id"),
            first_name=F("agent_wallet__agent__first_name"),
            last_name=F("agent_wallet__agent__last_name"),
            transaction_count=Count("amount"),
            tickets_sold=Sum("amount"),
            last_updated=F("agent_wallet__agent__last_updated"),
            phone_number=F("agent_wallet__agent__phone"),
            full_name=F("agent_wallet__agent__full_name"),
        ).order_by("-agent_wallet__agent__last_updated")

        # Remittance
        self.remittance_qs = agents_remittance_queryset.filter(**self.date_filter_three).order_by("-updated_at")

    def get_agents_overview_metrics(self):
        data = {
            "all_agents": self.all_agents_count,
            "active_agents": {"count": self.active_agents_count, "percentage": {"percent": 0.00, "change": "no_change"}},
            "new_agents": {"count": self.new_agents_count, "percentage": {"percent": 0.00, "change": "no_change"}},
            "inactive_agents": {"count": self.inactive_agents_count, "percentage": {"percent": 0.00, "change": "no_change"}},
            "today_transaction_amount": self.total_transactions_amount_today if self.total_transactions_amount_today else 0.00,
            "total_transaction_amount": self.total_transactions_amount or 0.00,
            "transactions_count": self.total_transactions_count,
            "today_transactions_count": self.total_transactions_count_today,
        }
        return data

    def get_all_agents_tickets(self):
        data = {"top10_tickets_sales": self.top10_tickets_sold_amount, "top10_tickets_count": self.top10_tickets_sold_count}
        return data

    def get_all_agents_details_table(self):
        agent_details_list = []
        for agent in self.agents_qs:
            tickets_sold_qs = self.tickets_sold_qs.all().filter(agent_wallet__agent=agent)
            tickets_sold_amount = list(tickets_sold_qs.aggregate(Sum("amount")).values())[0]
            tickets_sold_count = tickets_sold_qs.count()
            transactions_qs = self.all_transactions_qs.all().filter(agent_wallet__agent=agent)
            transaction_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0] or 0.00
            transaction_count = transactions_qs.count()

            data = {
                "email": agent.email,
                "fullname": agent.full_name,
                "agent_id": agent.id,
                "id": agent.id,
                "first_name": agent.first_name,
                "last_name": agent.last_name,
                "transaction_count": transaction_count,
                "transaction_amount": transaction_amount,
                "tickets_sold": tickets_sold_amount or 0.00,
                "tickets_sold_count": tickets_sold_count,
                "last_updated": f"{agent.last_updated}",
                "phone_number": agent.phone,
                "full_name": agent.full_name,
            }
            agent_details_list.append(data)
        data = {"agents_details_list": agent_details_list, "count": len(agent_details_list)}

        return data

    # All Active Agents
    def get_all_active_agents_details_table(self):
        active_agents_qs = self.agents_qs.exclude(performance_status__in=["INACTIVE", "ABSENT"])

        active_agent_details_list = []
        for agent in active_agents_qs:
            tickets_sold_qs = self.tickets_sold_qs.all().filter(agent_wallet__agent=agent)
            tickets_sold_amount = list(tickets_sold_qs.aggregate(Sum("amount")).values())[0]
            tickets_sold_count = tickets_sold_qs.count()
            transactions_qs = self.all_transactions_qs.all().filter(agent_wallet__agent=agent)
            transaction_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]
            transaction_count = transactions_qs.count()

            data = {
                "email": agent.email,
                "fullname": agent.full_name,
                "agent_id": agent.id,
                "id": agent.id,
                "first_name": agent.first_name,
                "last_name": agent.last_name,
                "transaction_count": transaction_count,
                "transaction_amount": transaction_amount if transaction_amount else 0.00,
                "tickets_sold": tickets_sold_amount if tickets_sold_amount else 0.00,
                "tickets_sold_count": tickets_sold_count if tickets_sold_count else 0.00,
                "last_updated": f"{agent.last_updated}",
                "phone_number": agent.phone,
                "full_name": agent.full_name,
            }
            active_agent_details_list.append(data)

        data = {"agents_details_list": active_agent_details_list, "count": len(active_agent_details_list)}
        return data

    # All InActive Agents
    def get_all_inactive_agents_details_table(self):
        inactive_agents_qs = self.agents_qs.filter(performance_status__in=["INACTIVE", "ABSENT"])
        inactive_agent_details_list = []
        for agent in inactive_agents_qs:
            tickets_sold_qs = self.tickets_sold_qs.all().filter(agent_wallet__agent=agent)
            tickets_sold_amount = list(tickets_sold_qs.aggregate(Sum("amount")).values())[0]
            tickets_sold_count = tickets_sold_qs.count()
            transactions_qs = self.all_transactions_qs.all().filter(agent_wallet__agent=agent)
            transaction_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]
            transaction_count = transactions_qs.count()

            data = {
                "email": agent.email,
                "fullname": agent.full_name,
                "agent_id": agent.id,
                "id": agent.id,
                "first_name": agent.first_name,
                "last_name": agent.last_name,
                "transaction_count": transaction_count,
                "transaction_amount": transaction_amount or 0.00,
                "tickets_sold": tickets_sold_amount or 0.00,
                "tickets_sold_count": tickets_sold_count,
                "last_updated": f"{agent.last_updated}",
                "phone_number": agent.phone,
                "full_name": agent.full_name,
            }
            inactive_agent_details_list.append(data)
        data = {"agents_details_list": inactive_agent_details_list, "count": len(inactive_agent_details_list)}
        return data

    # Single Agents
    def get_single_agent_details(self, id):
        single_agent = self.agents_qs.get(id=id)
        tickets_sales_qs = self.tickets_sold_qs.filter(agent_wallet__agent=single_agent)
        transactions_qs = self.all_transactions_qs.filter(agent_wallet__agent=single_agent)
        tickets_sales_last_month_qs = tickets_sales_qs.filter(date_created__gte=self.previous_month_start, date_created__lte=self.previous_month_end)
        tickets_sales_this_month_qs = tickets_sales_qs.filter(date_created__date__gte=self.month_start)

        transactions_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]
        transactions_today_amount = list(transactions_qs.filter(date_created__date=self.date_today_date).aggregate(Sum("amount")).values())[0]
        ticket_sales_amount = list(tickets_sales_qs.aggregate(Sum("amount")).values())[0]
        ticket_sales_today_amount = list(tickets_sales_qs.filter(date_created__date=self.date_today_date).aggregate(Sum("amount")).values())[0]
        ticket_sales_last_month_amount = list(tickets_sales_last_month_qs.aggregate(Sum("amount")).values())[0]
        ticket_sales_this_month_amount = list(tickets_sales_this_month_qs.aggregate(Sum("amount")).values())[0]

        ticket_sales_count = tickets_sales_qs.count()
        ticket_sales_today_count = tickets_sales_qs.filter(date_created__date=self.date_today_date).count()
        ticket_sales_this_month_count = tickets_sales_this_month_qs.count()
        ticket_sales_last_month_count = tickets_sales_last_month_qs.count()

        ticket_sales_percentage_diff = get_percentage_change(ticket_sales_last_month_amount, ticket_sales_this_month_amount)
        ticket_sales_count_percentage_diff = get_percentage_change(ticket_sales_last_month_count, ticket_sales_this_month_count)

        agent_id_list = [agent["id"] for agent in self.agent_details_qs]
        try:
            next_agent_id = agent_id_list[agent_id_list.index(id) + 1]
        except IndexError:
            next_agent_id = agent_id_list[agent_id_list.index(id) - 1]
        except Exception:
            next_agent_id = id

        try:
            previous_agent_id = agent_id_list[agent_id_list.index(id) - 1]
        except IndexError:
            previous_agent_id = agent_id_list[agent_id_list.index(id) - 1]
        except Exception:
            previous_agent_id = id

        cashout_transactions_qs = tickets_sales_qs.filter(transaction_from__in=["WINNINGS_WITHDRAW", "GAME_PLAY", "WINNINGS"])
        transaction_days_count = (
            tickets_sales_qs.values("date_created__year", "date_created__month", "date_created__day").distinct("date_created__day").count()
        )
        # average_daily_sales_qs = tickets_sales_qs.values("date_created__month", "amount").annotate(month_sum=Sum("amount"))
        days_since_joined = (self.date_today - single_agent.created_date).days
        months_since_joined = floor(days_since_joined / 30)

        if days_since_joined > 7:
            num_weeks_since_joined = floor(days_since_joined / 7)
            days_since_joined_less_weekends = ((num_weeks_since_joined * 7) + (days_since_joined % 7)) - num_weeks_since_joined
        else:
            days_since_joined_less_weekends = 0

        agent_wallet = self.agent_wallet_qs.get(agent=single_agent)
        agent_play_wallet = agent_wallet.game_play_bal
        agent_winnings_wallet = agent_wallet.winnings_bal

        winnings_withdraw_amount = list(self.winnings_withdrawn_qs.filter(agent=single_agent).aggregate(Sum("amount_won")).values())[0]
        winnings_withdraw_amount_today = list(
            self.winnings_withdrawn_qs.filter(agent=single_agent, date_created__date=self.date_today_date).aggregate(Sum("amount_won")).values()
        )[0]
        agent_remittance_qs = self.remittance_qs.filter(agent=single_agent)
        paid_remittance = list(agent_remittance_qs.aggregate(Sum("amount_paid")).values())[0]
        paid_remittance_today = list(agent_remittance_qs.filter(created_at__date=self.date_today_date).aggregate(Sum("amount_paid")).values())[0]
        unpaid_remittance = list(
            agent_remittance_qs.filter(remitted=False, due=True)
            .annotate(amount_owed=F("amount") - F("amount_paid"))
            .aggregate(Sum("amount_owed"))
            .values()
        )[0]
        unpaid_remittance_today = list(
            agent_remittance_qs.filter(remitted=False, due=True, created_at__date=self.date_today_date)
            .annotate(amount_owed=F("amount") - F("amount_paid"))
            .aggregate(Sum("amount_owed"))
            .values()
        )[0]
        accrued_remittance = list(
            agent_remittance_qs.filter(due=False, remitted=False)
            .annotate(amount_owed=F("amount") - F("amount_paid"))
            .aggregate(Sum("amount_owed"))
            .values()
        )[0]
        accrued_remittance_today = list(
            agent_remittance_qs.filter(due=False, remitted=False, created_at__date=self.date_today_date)
            .annotate(amount_owed=F("amount") - F("amount_paid"))
            .aggregate(Sum("amount_owed"))
            .values()
        )[0]

        average_daily_sales = (ticket_sales_amount or 0) / (
            days_since_joined_less_weekends if days_since_joined_less_weekends else 1
        )
        average_monthly_sales = (ticket_sales_amount or 0) / (months_since_joined if months_since_joined else 1)
        average_games_played = list(tickets_sales_qs.filter(transaction_from="GAME_PLAY").aggregate(Avg("amount")).values())[0]
        failed_cashout = list(cashout_transactions_qs.filter(status="FAILED").aggregate(Sum("amount")).values())[0]
        monthly_score = 0
        # days_absent = days_since_joined - transaction_days_count
        has_recent_payment = agent_remittance_qs.filter(
            amount_paid__gt=0,
            updated_at__gte=self.date_today - dt.timedelta(minutes=2)
        ).exists()

        if has_recent_payment:
            days_absent = days_since_joined - transaction_days_count
        else:
            days_absent = days_since_joined - transaction_days_count + 1

        data = {
            "location": single_agent.address,
            "account_name": single_agent.full_name,
            "phone_number": single_agent.phone,
            "monthly_score": monthly_score,
            "terminal_id": single_agent.terminal_id,
            "terminal_sim": "",
            "email": single_agent.email,
            "ticket_sales": ticket_sales_amount or 0.00,
            "ticket_sales_today": ticket_sales_today_amount or 0.00,
            "ticket_sales_change": ticket_sales_percentage_diff.get("change"),
            "ticket_count_change": ticket_sales_count_percentage_diff.get("change"),
            "ticket_count": ticket_sales_count,
            "ticket_count_today": ticket_sales_today_count,
            "days_absent": days_absent,
            "play_wallet": agent_play_wallet or 0.00,
            "winnings_wallet": agent_winnings_wallet or 0.00,
            "winnings_withdrawn": winnings_withdraw_amount or 0.00,
            "winnings_withdrawn_today": winnings_withdraw_amount_today or 0.00,
            "paid_remittance": paid_remittance or 0.00,
            "paid_remittance_today": paid_remittance_today or 0.00,
            "remittance_unpaid": unpaid_remittance or 0.00,
            "remittance_unpaid_today": unpaid_remittance_today or 0.00,
            "remittance_accrued": accrued_remittance or 0.00,
            "remittance_accrued_today": accrued_remittance_today or 0.00,
            "average_daily_sales": average_daily_sales,
            "average_monthly_sales": average_monthly_sales,
            "average_games_played": average_games_played,
            "failed_cashout": failed_cashout or 0.00,
            "next_agent_id": next_agent_id,
            "previous_agent_id": previous_agent_id,
            "transactions_amount": transactions_amount or 0.00,
            "today_transactions_amount": transactions_today_amount or 0.00,
            "date_joined": f"{single_agent.created_date}",
        }
        return data

    def get_game_play_details(self, id):
        # Game Play
        single_agent = self.agents_qs.get(id=id)
        agent_game_plays = self.transactions_qs.filter(agent_wallet__agent=single_agent).order_by("-date_created")

        game_play_details_list = []
        for agent_game_play in agent_game_plays:
            game_play_details = {
                "id": id,
                "game_type": agent_game_play.game_type or "",
                "amount_played": agent_game_play.amount or 0.00,
                "number_of_tickets": 1,
                "email": agent_game_play.agent_wallet.agent.email,
                "game_status": agent_game_play.status,
                "last_updated": f"{agent_game_play.date_created}",
            }
            game_play_details_list.append(game_play_details)

        data = {"game_type": game_play_details_list, "count": len(game_play_details_list)}

        return data

    # Single Agent Remittances Table
    def get_remittance_details(self, request, id):
        # agent = Agent.objects.get(id=id)
        agent = Agent.objects.filter(id=id, supervisor__user_id=request.user.id)[0]
        agent_remittance_qs = self.remittance_qs.filter(agent=agent)

        agent_remittance_details_list = []
        for remittance in agent_remittance_qs:
            remittance_owed = remittance.amount - remittance.amount_paid

            details = {
                "id": remittance.id,
                "email": agent.email,
                "amount_owed": remittance_owed,
                "amount_paid": remittance.amount_paid,
                "days_due": remittance.days_due,
                "remittance_status": "Owing" if remittance_owed and remittance_owed > 0 else "Not owing",
                "last_updated": f"{remittance.updated_at}",
            }
            agent_remittance_details_list.append(details)

        data = {"remittance_list": agent_remittance_details_list, "count": len(agent_remittance_details_list)}
        return data

    def get_agents_performance(self):
        non_performing_agents_list_details = self.agent_details_qs.filter(agent_wallet__agent__performance_status="INACTIVE")
        today_non_performing = 0
        week_non_performing = 0
        month_non_performing = 0
        data = {
            "overview": {
                "today_non_performing": today_non_performing,
                "week_non_performing": week_non_performing,
                "month_non_performing": month_non_performing,
            },
            "non_performing_agents_table": non_performing_agents_list_details,
            "count": len(non_performing_agents_list_details),
        }
        return data


class SupervisorAnalytics:
    def __init__(self, request):
        self.under_performing_agents_count =100
        self.date_filter = DateUtility().get_filter(request).get("date_filter")
        self.date_filter_two = DateUtility().get_filter(request).get("date_filter_two")
        self.date_filter_three = DateUtility().get_filter(request).get("date_filter_three")
        self.date_filter_four = DateUtility().get_filter(request).get("date_filter_four")
        self.date_filter_five = DateUtility().get_filter(request).get("date_filter_five")

        self.date_today_date = DateUtility().date_today_date
        self.week_start = DateUtility().week_start
        self.month_start = DateUtility().month_start
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
        self.months_list_names = DateUtility().months_list_names

        self.sup_agents_qs = Agent.objects.all().filter(agent_type="LOTTO_AGENT", terminal_id__isnull=False, terminal_retrieved=False)
        self.agents_qs = Agent.objects.all().filter(agent_type="LOTTO_AGENT", terminal_id__isnull=False, terminal_retrieved=False, supervisor__user_id=request.user.id)
        self.supervisors_qs = Supervisor.objects.all().filter(location__isnull=False, user_id=request.user.id)
        self.locations_qs = SupervisorLocation.objects.all()
        # self.remittance_qs = LottoAgentRemittanceTable.objects.all().filter(**self.date_filter_three)
        self.remittance_qs = LottoAgentRemittanceTable.objects.filter(Q(**self.date_filter_three) & Q(agent__supervisor__user_id=request.user.id))
        # self.tickets_sold_qs = (
        #     AgentWalletTransaction.objects.all()
        #     .filter(status="SUCCESSFUL", transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"])
        #     .filter(**self.date_filter)
        # )
        self.tickets_sold_qs = (
            AgentWalletTransaction.objects.filter(
            Q(status="SUCCESSFUL", transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"])
            & Q(**self.date_filter) & Q(agent_wallet__agent__supervisor__user_id=request.user.id))
        )

        # Supervisor Analytics
        self.number_of_supervisors = self.supervisors_qs.count()
        self.number_of_locations = self.locations_qs.count()
        self.number_of_agents = self.sup_agents_qs.count()
        self.inactive_agents = self.sup_agents_qs.filter(performance_status="INACTIVE").count()
        self.performing_agents = self.sup_agents_qs.filter(performance_status="PERFORMING").count()
        self.non_performing_agents = self.sup_agents_qs.filter(performance_status="UNDER_PERFORMING").count()
        self.absent_agents_count = self.sup_agents_qs.filter(performance_status="ABSENT").count()

        self.paid_remittance = list(self.remittance_qs.filter(remitted=True).aggregate(Sum("amount")).values())[0]
        self.unpaid_remittance = list(self.remittance_qs.filter(remitted=False).aggregate(Sum("amount")).values())[0]
        self.accrued_remittance = list(
            self.remittance_qs.filter(due=False, remitted=False)
            .annotate(amount_owed=F("amount") - F("amount_paid"))
            .aggregate(Sum("amount_owed"))
            .values()
        )[0]

        self.remittance_paid_qs = AgentWalletTransaction.objects.all().filter(
            transaction_from__in=["REMITTANCE", "REMITTANCE_EXCESS"], status="SUCCESSFUL"
        )
        self.paid_remittance_today = list(self.remittance_paid_qs.filter(date_created__date=self.date_today_date).aggregate(Sum("amount")).values())[
            0
        ]
        self.unpaid_remittance_today = list(self.remittance_qs.filter(remitted=False, due=True).aggregate(Sum("amount")).values())[0]
        self.accrued_remittance_today = list(
            self.remittance_qs.filter(due=False, remitted=False, created_at__date=self.date_today_date)
            .annotate(amount_owed=F("amount") - F("amount_paid"))
            .aggregate(Sum("amount_owed"))
            .values()
        )[0]

        # Transaction Comparatives
        self.transactions_qs = AgentWalletTransaction.objects.all().filter(status="SUCCESSFUL").filter(**self.date_filter)

        # --> REMITTANCE, GAMEPLAY, WINNINGS
        self.remittance_values_list = []
        self.game_play_values_list = []
        self.winnings_values_list = []
        self.performing_values_list = []
        self.under_performing_values_list = []
        self.performing_supervisor_data = []
        self.under_performing_supervisor_data = []
        self.non_performing_supervisors_list = []
        self.absent_supervisors_list = []

        self.supervisors_locations_list = [supervisor.location.name for supervisor in self.supervisors_qs]

        self.supervisor_details_list = []
        for supervisor in self.supervisors_qs:
            supervisor_base_qs = self.transactions_qs.filter(agent_wallet__agent__supervisor=supervisor, agent_wallet__agent__supervisor__user_id=request.user.id)
            remittance_amount = list(self.remittance_qs.filter(remitted=True, agent__supervisor=supervisor, agent_wallet__agent__supervisor__user_id=request.user.id).aggregate(Sum("amount")).values())[0]
            game_play_amount = list(supervisor_base_qs.filter(transaction_from="GAME_PLAY").aggregate(Sum("amount")).values())[0]
            winnings_amount = list(supervisor_base_qs.filter(transaction_from="WINNINGS").aggregate(Sum("amount")).values())[0]
            transactions_count = supervisor_base_qs.count()
            transactions_amount = list(supervisor_base_qs.aggregate(Sum("amount")).values())[0]
            tickets_sold_count = self.tickets_sold_qs.filter(agent_wallet__agent__supervisor=supervisor).count()
            tickets_sold_amount = list(self.tickets_sold_qs.filter(agent_wallet__agent__supervisor=supervisor, agent_wallet__agent__supervisor__user_id=request.user.id).aggregate(Sum("amount")).values())[0]

            self.performing_agents_count = (
                self.agents_qs.filter(performance_status="PERFORMING").filter(**self.date_filter_four).count()
            )
            self.under_performing_agents_count = (
                self.agents_qs.filter(performance_status="UNDER_PERFORMING").filter(**self.date_filter_four).count()
            )
            self.supervisor_location = supervisor.location.name

            self.remittance_values_list.append(remittance_amount or 0.00)
            self.game_play_values_list.append(game_play_amount or 0.00)
            self.winnings_values_list.append(winnings_amount or 0.00)

            self.performing_values_list.append(self.performing_agents_count)
            self.under_performing_values_list.append(self.under_performing_agents_count)

            self.performing_supervisor_data.append(
                {"name": supervisor.location.name, "count": self.performing_agents_count, "location": self.supervisor_location, "score": 0}
            )
            self.under_performing_supervisor_data.append(
                {"name": supervisor.location.name, "count": self.performing_agents_count, "location": self.supervisor_location, "score": 0}
            )

            # supervisor details list
            supervisor_location = SupervisorLocation.objects.all().filter(supervisor=supervisor).last()
            data = {
                "id": supervisor_location.id if supervisor_location else supervisor.id,
                "name": supervisor.full_name,
                "location": supervisor_location.name if supervisor_location else "",
                "number_of_agents": self.agents_qs.filter(supervisor=supervisor).count(),
                "number_of_tickets_sold": tickets_sold_count,
                "transaction_count": transactions_count,
                "transaction_amount": transactions_amount or 0.00,
                "tickets_sold": tickets_sold_amount or 0.00,
                "phone_number": supervisor.phone,
            }
            self.supervisor_details_list.append(data)

            if supervisor.performance_status == "UNDER_PERFORMING":
                self.non_performing_supervisors_list.append(data)

            if supervisor.performance_status == "INACTIVE":
                self.absent_supervisors_list.append(data)

        # Supervisor Data
        # Top3 Performing Supervisors
        self.top3_performing_supervisors = sorted(self.performing_supervisor_data, key=lambda d: d["count"])[::-1][:3]
        self.top3_under_performing_supervisors = sorted(self.under_performing_supervisor_data, key=lambda d: d["count"])[::-1][-1:-3]

    def get_supervisor_analytics(self):
        data = {
            "overview": {
                "number_of_locations": self.number_of_locations,
                "number_of_supervisors": self.number_of_supervisors,
                "number_of_agents": self.number_of_agents,
                "inactive_agents": self.inactive_agents,
                "non_performing": self.non_performing_agents,
                "paid_remittance": self.paid_remittance or 0.00,
                "unpaid_remittance": self.unpaid_remittance or 0.00,
                "accrued_remittance": self.accrued_remittance or 0.00,
                "paid_remittance_today": self.paid_remittance_today or 0.00,
                "unpaid_remittance_today": self.unpaid_remittance_today or 0.00,
                "accrued_remittance_today": self.accrued_remittance_today or 0.00,
            },
            "transaction_comparatives": {
                "labels": ["1M", "2M", "3M", "4M", "5M", "6M"],
                "locations_label": self.supervisors_locations_list,
                "remittance_values": self.remittance_values_list,
                "gameplay_values": self.game_play_values_list,
                "winnings_values": self.winnings_values_list,
                "percentages": {"percent": 1, "change": "up"},
            },
            "location_performance": {
                "total_performing": self.performing_agents,
                "total_underperforming": self.under_performing_agents_count,
                "labels": ["1000", "2000", "3000", "4000", "5000"],
                "locations_label": self.supervisors_locations_list,
                "performing_values": self.performing_values_list,
                "underperforming_values": self.under_performing_values_list,
                "percentages": {"percent": 1, "change": "up"},
            },
        }
        return data

    def get_supervisor_data(self):
        data = {
            "overview": {
                "number_of_locations": self.number_of_locations,
                "number_of_Supervisors": self.number_of_supervisors,
                "number_of_agents": self.number_of_agents,
                "inactive_agents": self.inactive_agents,
                "non_performing": self.non_performing_agents,
                "absent_agents": self.absent_agents_count,
                "paid_remittance": self.paid_remittance or 0.00,
                "unpaid_remittance": self.unpaid_remittance or 0.00,
                "accrued_remittance": self.accrued_remittance or 0.00,
                "paid_remittance_today": self.paid_remittance_today or 0.00,
                "unpaid_remittance_today": self.unpaid_remittance_today or 0.00,
                "accrued_remittance_today": self.accrued_remittance_today or 0.00,
            },
            "top3_performing_supervisors": self.top3_performing_supervisors,
            "top3_non_performing_supervisors": self.top3_under_performing_supervisors,
        }
        return data

    def get_location_details(self, id):
        location = SupervisorLocation.objects.get(id=id)
        supervisor = self.supervisors_qs.filter(location=location).last()
        supervisor_agents_qs = self.agents_qs.filter(supervisor=supervisor)

        # Agents
        number_of_agents = supervisor_agents_qs.count()
        non_performing_agents_count = supervisor_agents_qs.filter(performance_status="UNDER_PERFORMING").count()
        performing_agents_count = supervisor_agents_qs.filter(performance_status="PERFORMING").count()
        under_performing_agents_count = supervisor_agents_qs.filter(performance_status="UNDER_PERFORMING").count()
        absent_agents_count = supervisor_agents_qs.filter(performance_status="ABSENT").count()
        inactive_agents_count = supervisor_agents_qs.filter(performance_status="INACTIVE").count()

        # Transactions
        supervisor_transaction_qs = self.transactions_qs.filter(agent_wallet__agent__supervisor=supervisor)
        total_transaction_amount = list(
            supervisor_transaction_qs.filter(agent_wallet__agent__supervisor=supervisor).aggregate(Sum("amount")).values()
        )[0]
        today_total_transaction_amount = list(
            supervisor_transaction_qs.filter(date_created__date__gte=self.date_today_date, agent_wallet__agent__supervisor=supervisor)
            .aggregate(Sum("amount"))
            .values()
        )[0]

        total_transaction_count = supervisor_transaction_qs.filter(agent_wallet__agent__supervisor=supervisor).count()
        today_total_transaction_count = supervisor_transaction_qs.filter(
            date_created__date__gte=self.date_today_date, agent_wallet__agent__supervisor=supervisor
        ).count()

        # Tickets
        tickets_sold_qs = AgentWalletTransaction.objects.all().filter(
            status="SUCCESSFUL", transaction_from__in=["GAME_PLAY", "LIBERTY_PAY_LOTTO_CHARGE"]
        )
        tickets_sold_count = tickets_sold_qs.filter(agent_wallet__agent__supervisor=supervisor).count()
        tickets_sold_amount = list(tickets_sold_qs.filter(agent_wallet__agent__supervisor=supervisor).aggregate(Sum("amount")).values())[0]
        tickets_sold_amount_today = list(
            tickets_sold_qs.filter(date_created__date__gte=self.date_today_date, agent_wallet__agent__supervisor=supervisor)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        tickets_sold_count_today = tickets_sold_qs.filter(
            date_created__date__gte=self.date_today_date, agent_wallet__agent__supervisor=supervisor
        ).count()

        # Daily Ticket Sales
        current_week_ticket_sales = (
            tickets_sold_qs.filter(agent_wallet__agent__supervisor=supervisor, date_created__date__gte=self.week_start)
            .values("date_created__day")
            .annotate(transaction_amount=Sum("amount"))
        )

        try:
            if current_week_ticket_sales:
                week_days_amount_list = [
                    (self.date_today_date.replace(day=week_day["date_created__day"]).weekday(), week_day["transaction_amount"])
                    for week_day in current_week_ticket_sales
                ]
                week_days_amount_dict = dict(week_days_amount_list)
                week_days_sales_list = [week_days_amount_dict[v] if v in week_days_amount_dict.keys() else 0.00 for v in [0, 1, 2, 3, 4, 5, 6]]
            else:
                week_days_sales_list = [0] * 7
        except Exception:
            week_days_sales_list = [0] * 7

        # Remittance
        remittance_qs = self.remittance_qs.filter(agent__supervisor=supervisor)
        paid_remittance = list(remittance_qs.filter().aggregate(Sum("amount_paid")).values())[0]
        unpaid_remittance = list(
            remittance_qs.filter(due=True).annotate(amount_owed=F("amount") - F("amount_paid")).aggregate(Sum("amount_owed")).values()
        )[0]
        accrued_remittance = list(
            remittance_qs.filter(due=False, remitted=False)
            .annotate(amount_owed=F("amount") - F("amount_paid"))
            .aggregate(Sum("amount_owed"))
            .values()
        )[0]

        paid_remittance_today = list(remittance_qs.filter(created_at__date=self.date_today_date).aggregate(Sum("amount_paid")).values())[0]
        unpaid_remittance_today = list(
            remittance_qs.filter(created_at__date=self.date_today_date, due=True)
            .annotate(amount_owed=F("amount") - F("amount_paid"))
            .aggregate(Sum("amount_owed"))
            .values()
        )[0]
        accrued_remittance_today = list(
            remittance_qs.filter(created_at__date=self.date_today_date, due=False, remitted=False)
            .annotate(amount_owed=F("amount") - F("amount_paid"))
            .aggregate(Sum("amount_owed"))
            .values()
        )[0]

        # Agents Performance
        underperforming_agents_percentage = (under_performing_agents_count / (number_of_agents if number_of_agents else 1)) * 100
        performing_agents_percentage = (performing_agents_count / (number_of_agents if number_of_agents else 1)) * 100

        data = {
            "supervisor_details": {
                "location": location.name,
                "name": supervisor.full_name,
                "email": supervisor.email,
                "phone_number": supervisor.phone,
            },
            "number_of_agents": number_of_agents,
            "number_of_agents_change": "up",
            "nonperforming": non_performing_agents_count,
            "nonperforming_change": "down",
            "monthly_score": 5,
            "total_transactions_amount": total_transaction_amount if total_transaction_amount else 0.00,
            "total_transactions_amount_change": "up",
            "today_transactions_amount": today_total_transaction_amount if today_total_transaction_amount else 0.00,
            "total_transactions_counts": total_transaction_count,
            "today_transactions_counts": today_total_transaction_count,
            "total_tickets_counts": tickets_sold_count,
            "total_tickets_counts_today": tickets_sold_count_today,
            "total_tickets_amounts": tickets_sold_amount if tickets_sold_amount else 0.00,
            "total_tickets_amounts_today": tickets_sold_amount_today if tickets_sold_amount_today else 0.00,
            "total_tickets_counts_change": "up",
            "total_tickets_amounts_change": "up",
            "absent_agents": absent_agents_count,
            "inactive_agents": inactive_agents_count,
            "inactive_agents_change": "down",
            "absent_agents_change": "down",
            "performance": {
                "performing": {"count": performing_agents_count, "percentage": performing_agents_percentage},
                "underperforming": {"count": under_performing_agents_count, "percentage": underperforming_agents_percentage},
            },
            "remittance": {
                "remittance_paid": {
                    "amount": paid_remittance if paid_remittance else 0.00,
                    "today": paid_remittance_today if paid_remittance_today else 0.00,
                    "percentage": 0,
                    "change": "up",
                },
                "remittance_unpaid": {
                    "amount": unpaid_remittance if unpaid_remittance else 0.00,
                    "today": unpaid_remittance_today if unpaid_remittance_today else 0.00,
                    "percentage": 0,
                    "change": "down",
                },
                "remittance_accrued": {
                    "amount": accrued_remittance if accrued_remittance else 0.00,
                    "today": accrued_remittance_today if accrued_remittance_today else 0.00,
                    "percentage": 0,
                    "change": "down",
                },
            },
            "ticket_sales_bar_graph": {
                "amount_label": ["100K", "200K", "400K", "600K", "800K", "1M"],
                "days_label": ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
                "ticket_sales_values": week_days_sales_list,
            },
        }
        return data

    def get_supervisors_agents_list(self):
        data = {"agents_details_list": self.supervisor_details_list, "count": len(self.supervisor_details_list)}
        return data

    def get_supervisors_no_performing_agents_list(self):
        data = {"agents_details_list": self.non_performing_supervisors_list, "count": len(self.non_performing_supervisors_list)}
        return data

    def get_supervisors_absent_agents_list(self):
        data = {"agents_details_list": self.absent_supervisors_list, "count": len(self.absent_supervisors_list)}
        return data

    def get_location_agents_list(self, id):
        location = SupervisorLocation.objects.get(id=id)
        supervisor = Supervisor.objects.all().filter(location=location).last()
        supervisor_agents = self.agents_qs.filter(supervisor=supervisor)

        agent_details_list = []
        for agent in supervisor_agents:
            transactions_qs = self.transactions_qs.filter(agent_wallet__agent=agent)
            tickets_sold_qs = self.tickets_sold_qs.filter(agent_wallet__agent=agent)
            tickets_sold_amount = list(tickets_sold_qs.aggregate(Sum("amount")).values())[0]
            transaction_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]
            transaction_count = transactions_qs.count()
            tickets_sold_count = tickets_sold_qs.count()

            data = {
                "email": agent.email,
                "agent_id": agent.id,
                "id": agent.id,
                "first_name": agent.first_name,
                "last_name": agent.last_name,
                "transaction_count": transaction_count,
                "transaction_amount": transaction_amount if transaction_amount else 0.00,
                "tickets_sold": tickets_sold_amount if tickets_sold_amount else 0.00,
                "tickets_sold_count": tickets_sold_count,
                "last_updated": agent.last_updated,
                "phone_number": agent.phone,
                "full_name": agent.full_name,
            }
            agent_details_list.append(data)

        # agent_details_list = self.agent_details_qs.filter(agent_wallet__agent__supervisor=supervisor)
        data = {"agents_details_list": agent_details_list, "count": len(agent_details_list)}
        return data

    def get_location_no_performing_agents_list(self, id):
        location = SupervisorLocation.objects.get(id=id)
        supervisor = Supervisor.objects.all().filter(location=location).last()

        supervisor_agents = self.agents_qs.filter(supervisor=supervisor, performance_status__in=["UNDER_PERFORMING", "INACTIVE"])

        agent_details_list = []
        for agent in supervisor_agents:
            transactions_qs = self.transactions_qs.filter(agent_wallet__agent=agent)
            tickets_sold_qs = self.tickets_sold_qs.filter(agent_wallet__agent=agent)
            tickets_sold_amount = list(tickets_sold_qs.aggregate(Sum("amount")).values())[0]
            transaction_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]
            transaction_count = transactions_qs.count()
            tickets_sold_count = tickets_sold_qs.count()

            data = {
                "email": agent.email,
                "agent_id": agent.id,
                "id": agent.id,
                "first_name": agent.first_name,
                "last_name": agent.last_name,
                "transaction_count": transaction_count,
                "transaction_amount": transaction_amount if transaction_amount else 0.00,
                "tickets_sold": tickets_sold_amount if tickets_sold_amount else 0.00,
                "tickets_sold_count": tickets_sold_count,
                "last_updated": agent.last_updated,
                "phone_number": agent.phone,
                "full_name": agent.full_name,
            }
            agent_details_list.append(data)

        data = {"agents_details_list": agent_details_list, "count": len(agent_details_list)}
        return data

    def get_location_absent_agents_list(self, id):
        location = SupervisorLocation.objects.get(id=id)
        supervisor = Supervisor.objects.all().filter(location=location).last()

        supervisor_agents = self.agents_qs.filter(supervisor=supervisor, performance_status="ABSENT")

        agent_details_list = []
        for agent in supervisor_agents:
            transactions_qs = self.transactions_qs.filter(agent_wallet__agent=agent)
            tickets_sold_qs = self.tickets_sold_qs.filter(agent_wallet__agent=agent)
            tickets_sold_amount = list(tickets_sold_qs.aggregate(Sum("amount")).values())[0]
            transaction_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]
            transaction_count = transactions_qs.count()
            tickets_sold_count = tickets_sold_qs.count()

            data = {
                "email": agent.email,
                "agent_id": agent.id,
                "id": agent.id,
                "first_name": agent.first_name,
                "last_name": agent.last_name,
                "transaction_count": transaction_count,
                "transaction_amount": transaction_amount if transaction_amount else 0.00,
                "tickets_sold": tickets_sold_amount if tickets_sold_amount else 0.00,
                "tickets_sold_count": tickets_sold_count,
                "last_updated": agent.last_updated,
                "phone_number": agent.phone,
                "full_name": agent.full_name,
            }
            agent_details_list.append(data)

        data = {"agents_details_list": agent_details_list, "count": len(agent_details_list)}
        return data

    def get_location_remittance_list(self, id):
        location = SupervisorLocation.objects.get(id=id)
        supervisor = Supervisor.objects.all().filter(location=location).last()

        supervisor_agent_remittance_details_list = []
        supervisor_agents_qs = Agent.objects.all().filter(supervisor=supervisor)
        for agent in supervisor_agents_qs:
            agent_remittance_qs = self.remittance_qs.filter(agent=agent)
            amount_owed = list(
                agent_remittance_qs.filter(remitted=False, due=True)
                .annotate(amount_owed=F("amount") - F("amount_paid"))
                .aggregate(Sum("amount_owed"))
                .values()
            )[0]
            amount_paid = list(agent_remittance_qs.filter(remitted=True).aggregate(Sum("amount_paid")).values())[0]

            if agent_remittance_qs.filter(remitted=False, due=True):
                remittance_status = "Owing"
                days_due = agent_remittance_qs.filter(remitted=False).last().days_due
            else:
                remittance_status = "Not owing"
                days_due = 0

            details = {
                "id": agent.id,
                "email": agent.email,
                "amount_owed": amount_owed if amount_owed else 0.00,
                "amount_paid": amount_paid if amount_paid else 0.00,
                "days_due": days_due,
                "remittance_status": remittance_status,
                "last_updated": agent.last_updated,
                "phone_number": agent.phone,
                "full_name": agent.full_name,
                "first_name": agent.first_name,
                "last_name": agent.last_name,
            }
            supervisor_agent_remittance_details_list.append(details)

        data = {"remittance_list": supervisor_agent_remittance_details_list, "count": len(supervisor_agent_remittance_details_list)}
        return data

    def get_supervisor_user_role(self, request):
        user = request.user
        user_email = user.email
        supervisor = Supervisor.objects.filter(email=user_email).last()

        if supervisor and user.is_staff:
            location = SupervisorLocation.objects.filter(supervisor=supervisor).last()
            user_groups = user.groups.all()
            group_name_list = [group.name for group in user_groups]  # list(user.groups.all())
            role = "supervisor" if "Winwise Supervisor" in group_name_list else "supervisor1" if "Head of Supervisor" in group_name_list else ""
            location_id = location.id if location else 0
        elif user.is_staff:
            role = "admin"
            location_id = ""
        else:
            location_id = ""
            role = ""

        user_details = {"email": user_email, "username": user.username, "user_id": user.id, "user_role": role, "location_id": location_id}
        return user_details


class LottoAgentsTransactionsAnalytics:
    """
    For Agency Banking Weekly Agents Activities Report
    """

    def get_agent_transaction_details(self, request):
        month_start = DateUtility().month_start

        agents_qs = Agent.objects.all().filter(agent_type="LOTTO_AGENT", terminal_id__isnull=False, supervisor__user_id=request.user.id)

        agent_records_list = []
        for agent in agents_qs:
            agent_transaction_qs = (
                AgentWalletTransaction.objects.all()
                .filter(agent_wallet__agent=agent, agent_wallet__agent__terminal_id__isnull=False, status="SUCCESSFUL")
                .order_by("date_created")
            )
            agent_first_transaction = agent_transaction_qs.first()
            try:
                first_transaction_date = agent_first_transaction.date_created
                days_before_first_transaction = abs(first_transaction_date - agent.created_date).days
            except Exception:
                days_before_first_transaction = None
                first_transaction_date = None

            total_transactions_count = agent_transaction_qs.count()
            total_transactions_amount = list(agent_transaction_qs.aggregate(Sum("amount")).values())[0]
            overall_total_transactions_count = agent_transaction_qs.count()
            agent_current_month_transactions_count = agent_transaction_qs.filter(date_created__gte=month_start).count()
            agent_current_month_transactions_amount = list(
                agent_transaction_qs.filter(date_created__gte=month_start).aggregate(Sum("amount")).values()
            )[0]
            last_transaction = agent_transaction_qs.last()

            last_transaction_date = last_transaction.date_created if last_transaction else None
            last_transaction_type = last_transaction.transaction_type if last_transaction else None

            data = {
                "terminal_id": agent.terminal_id,
                # "agent_first_transaction": agent_first_transaction,
                "first_transaction_date": first_transaction_date if first_transaction_date else "",
                "days_before_first_transaction": days_before_first_transaction if days_before_first_transaction else "",
                "total_transactions_count": total_transactions_count,
                "total_transactions_amount": total_transactions_amount if total_transactions_amount else 0.00,
                "overall_total_transactions_count": overall_total_transactions_count,
                "agent_current_month_transactions_count": agent_current_month_transactions_count,
                "agent_current_month_transactions_amount": (
                    agent_current_month_transactions_amount if agent_current_month_transactions_amount else 0.00
                ),
                # "last_transaction": last_transaction,
                "last_transaction_date": last_transaction_date if last_transaction_date else "",
                "last_transaction_type": last_transaction_type if last_transaction_type else "",
            }

            agent_records_list.append(data)
        return agent_records_list


class DataAnalyticsDataExposure:
    def get_lotto_ticket_table(request):
        lotto_tickets_qs = LottoTicket.objects.all().filter()
        lotto_tickets_all_qs = LottoTicket.objects.all().filter()
        lotto_tickets_qs = Paginator.paginate(request=request, queryset=lotto_tickets_qs, page=1)

        lotto_tickets_list = []
        for ticket in lotto_tickets_qs:
            data = {
                "id": ticket.id,
                "date": ticket.date,
                "game_play_id": ticket.game_play_id,
                "amount_paid": ticket.amount_paid,
                "rto": ticket.rto,
                "rtp": ticket.rtp,
                "rtp_per": ticket.rtp_per,
                "lottery_type": ticket.lottery_type,
                "channel": ticket.channel,
                "paid": ticket.paid,
            }
            lotto_tickets_list.append(data)
        return {
            "data": lotto_tickets_list,
            "count": len(lotto_tickets_qs),
            "num_pages": floor(len(lotto_tickets_all_qs) / (len(lotto_tickets_list) if lotto_tickets_list else 1)),
        }

    def get_lottery_model_table():
        lotto_tickets_qs = LotteryModel.objects.all()

        lotto_tickets_list = []
        for ticket in lotto_tickets_qs:
            data = {
                "id": ticket.id,
                "date": ticket.date,
                "game_play_id": ticket.game_play_id,
                "amount_paid": ticket.amount_paid,
                "rto": ticket.rto,
                "rtp": ticket.rtp,
                "rtp_per": ticket.rtp_per,
                "lottery_type": ticket.lottery_type,
                "channel": ticket.channel,
                "paid": ticket.paid,
            }
            lotto_tickets_list.append(data)
        return {"data": lotto_tickets_list, "count": lotto_tickets_qs.count()}

    def get_awoof_game_table_model_table():
        lotto_tickets_qs = AwoofGameTable.objects.all()

        lotto_tickets_list = []
        for ticket in lotto_tickets_qs:
            data = {
                "id": ticket.id,
                "date_created": ticket.date_created,  # p
                "game_play_id": ticket.game_play_id,  # p
                "amount_paid": ticket.amount_paid,  # p
                "lottery_type": ticket.lottery_type,  # p
                "channel": ticket.channel,  # p
                "paid": ticket.paid,  # p
            }
            lotto_tickets_list.append(data)
        return {"data": lotto_tickets_list, "count": lotto_tickets_qs.count()}

    def get_lotto_winners_model_table():
        lotto_tickets_qs = LottoWinners.objects.all()

        lotto_tickets_list = []
        for ticket in lotto_tickets_qs:
            data = {
                "id": ticket.id,
                "date_won": ticket.date_won,
                "game_play_id": ticket.game_play_id,
                "earning": ticket.earning,
                "stake_amount": ticket.stake_amount,
                "lotto_type": ticket.lotto_type,
                "channel_played_from": ticket.channel_played_from,
            }
            lotto_tickets_list.append(data)
        return {"data": lotto_tickets_list, "count": lotto_tickets_qs.count()}

    def get_lottery_winners_model_table():
        lotto_tickets_qs = LotteryWinnersTable.objects.all()

        lotto_tickets_list = []
        for ticket in lotto_tickets_qs:
            data = {
                "id": ticket.id,
                "date_won": ticket.date_won,
                "game_play_id": ticket.game_play_id,
                "earning": ticket.earning,
                "stake_amount": ticket.stake_amount,
                "lottery_type": "WYSE_CASH",
                "lottery_source_tag": ticket.lottery_source_tag,
            }
            lotto_tickets_list.append(data)
        return {"data": lotto_tickets_list, "count": lotto_tickets_qs.count()}

    def get_awoof_draw_table_model_table():
        lotto_tickets_qs = AwoofDrawTable.objects.all()

        lotto_tickets_list = []
        for ticket in lotto_tickets_qs:
            data = {
                "id": ticket.id,
                "date_created": ticket.date_created,
                "ticket_id": ticket.ticket_id,
                "lottery_type": "AWOOF",
                "is_redeemed": ticket.is_redeemed,
            }
            lotto_tickets_list.append(data)
        return {"data": lotto_tickets_list, "count": lotto_tickets_qs.count()}

    def get_soccer_prediction_model_table():
        lotto_tickets_qs = SoccerPrediction.objects.all()

        lotto_tickets_list = []
        for ticket in lotto_tickets_qs:
            data = {
                "id": ticket.id,
                "date": ticket.date,
                "game_id": ticket.game_id,
                "amount_paid": ticket.amount_paid,
                "lottery_type": ticket.lottery_type,
                "channel": ticket.channel,
                "paid": ticket.paid,
            }
            lotto_tickets_list.append(data)
        return {"data": lotto_tickets_list, "count": lotto_tickets_qs.count()}

    # def get_lotto_winners_model_table():
    #     lotto_tickets_qs = LottoWinners.objects.all()

    #     lotto_tickets_list = []
    #     for ticket in lotto_tickets_qs:
    #         data = {
    #             "id": ticket.id,
    #             "date_won": ticket.date_won,
    #             "game_play_id": ticket.game_play_id,
    #             "earning": ticket.earning,
    #             "stake_amount": ticket.stake_amount,
    #             "lotto_type": ticket.lotto_type,
    #             "channel_played_from": ticket.channel_played_from,
    #         }
    #         lotto_tickets_list.append(data)
    #     return {"data": lotto_tickets_list, "count": lotto_tickets_qs.count()}

    def get_soccer_winners_model_table():
        lotto_tickets_qs = SoccerCashWinner.objects.all()

        lotto_tickets_list = []
        for ticket in lotto_tickets_qs:
            data = {
                "id": ticket.id,
                "date_won": ticket.date_won,
                "game_play_id": ticket.game_play_id,
                "earning": ticket.earning,
                "stake_amount": ticket.stake_amount,
                "lottery_type": "SOCCER_CASH",
                "channel_played_from": ticket.channel_played_from,
            }
            lotto_tickets_list.append(data)
        return {"data": lotto_tickets_list, "count": lotto_tickets_qs.count()}

    def get_agent_fundingtable_model():
        agent_funding_qs = AgentFundingTable.objects.all()

        agent_funding_list = []
        for funding in agent_funding_qs:
            data = {
                "id": funding.id,
                "created_at": funding.created_at,
                "amount": funding.amount,
                "is_verified": funding.is_verified,
                "source": funding.source,
            }
            agent_funding_list.append(data)
        return {"data": agent_funding_list, "count": agent_funding_qs.count()}

    def get_payment_transaction_model():
        agent_funding_qs = PaymentTransaction.objects.all()

        agent_funding_list = []
        for funding in agent_funding_qs:
            data = {
                "id": funding.id,
                "date_paid": funding.date_paid,
                "amount": funding.amount,
                "status": funding.status,
                "payment_channel": funding.payment_channel,
            }
            agent_funding_list.append(data)
        return {"data": agent_funding_list, "count": agent_funding_qs.count()}

    def get_failed_remittance_agency_wallet_charge_table_model():
        agent_funding_qs = FailedRemittanceAgencyWalletCharge.objects.all()

        agent_funding_list = []
        for funding in agent_funding_qs:
            data = {
                "id": funding.id,
                "created_at": funding.created_at,
                "amount_charge": funding.amount_charge,
                "successfully_charged": funding.successfully_charged,
            }
            agent_funding_list.append(data)
        return {"data": agent_funding_list, "count": agent_funding_qs.count()}
