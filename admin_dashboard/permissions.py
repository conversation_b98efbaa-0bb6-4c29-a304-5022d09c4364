from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.permissions import BasePermission

from pos_app.models import Supervisor, SupervisorLocation, Agent

# from django.contrib.auth.models import Group


class OnlySupervisorsAllowed(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "Only supervisors with assigned locations or admin staff are allowed to view this page",
    }
    default_code = "Not permitted"


class OnlyAdminAllowed(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "Only admin staff are allowed to view this page",
    }
    default_code = "Not permitted"


class OnlySupervisorsAgentsAccess(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "You only have access to your own agents!!!",
    }
    default_code = "Not permitted"


class SupervisorPermission(BasePermission):
    def has_permission(self, request, view):
        user = request.user
        user_groups = user.groups.filter(name__in=["Head of Supervisor", "Winwise Supervisor"])
        supervisor = Supervisor.objects.filter(email=user.email).last()
        # location = SupervisorLocation.objects.filter(supervisor=supervisor).last()

        if user.is_staff:
            return True
        elif user_groups or supervisor:
            return True
        else:
            raise OnlySupervisorsAllowed()


class AdminOnlyPermission(BasePermission):
    def has_permission(self, request, view):
        user = request.user

        if user.is_staff:
            return True
        else:
            raise OnlyAdminAllowed()
        

class SupervisorAgentPermission(BasePermission):
    
    def has_permission(self, request, view):
        agent_id = view.kwargs.get("id")
        if not agent_id:
            return False
        
        try:
            agent = Agent.objects.get(id=agent_id)
        except Agent.DoesNotExist:
            return False
        
        if not agent.supervisor:
            return False
        
        # return 
        if agent.supervisor.user_id == request.user.id:
            return True
        else:
            raise OnlySupervisorsAgentsAccess
