import base64
import csv
import io
import json
import os
import pprint
import random
import time
import uuid
from datetime import datetime, timedelta
from math import ceil
from pathlib import Path
from string import Template
from time import sleep

import numpy as np
import pandas as pd
import requests
from celery import shared_task
from celery.utils.log import get_task_logger
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db.models import Q, Sum
from django.utils import timezone

from main.api.api_lottery_helpers import generate_lucky_number
from main.helpers.game.decisioning_manager import Game
from main.helpers.helper_functions import (
    link_shortener,
    send_out_winners_result_data,
    trigger_whatsapp_admin_on_player_payment,
)
from main.helpers.loandisk import update_pool_on_loandisk
from main.helpers.pabbly_manager import create_pabbly_plan
from main.helpers.redis_storage import RedisStorage
from main.helpers.vfd_disbursement_helper import VfdDisbursementHelperFunc
from main.helpers.watupay_manager import generate_ussd_collection_code
from main.helpers.whisper_sms_managers import (
    general_withdraw_wallet_bal_notification,
    payment_receipt_sms,
    send_sms_for_payment_collection,
    send_sms_for_s4fgame_lost,
    send_sms_for_wyse_lost,
    send_sms_for_wyse_winners,
)
from main.helpers.woven_manager import generate_woven_collection_account_number
from main.ussd.helpers import Utility
# from overide_print import print

logger = get_task_logger(__name__)

mock_data = {
    10: {"jackpot_winners": [], "ordinary_winners": [], "total_jackpot_amount": 0},
    50: {
        "jackpot_winners": [],
        "ordinary_winners": [
            {
                "earning": 6407.************,
                "id": *********,
                "phone": "***********",
                "unique_id": "Jun-df-as1",
            }
        ],
        "total_jackpot_amount": 0,
    },
    250: {
        "jackpot_winners": [],
        "ordinary_winners": [],
        "total_jackpot_amount": 250000,
    },
    500: {
        "jackpot_winners": [],
        "ordinary_winners": [],
        "total_jackpot_amount": 750000,
    },
    1000: {
        "jackpot_winners": [],
        "ordinary_winners": [],
        "total_jackpot_amount": 1750000,
    },
    "totals": {
        "RTO": 2299830.0,
        "RTP": 5366270.0,
        "super_winners": None,
        "total_revenue": 7666100,
    },
}


def round_down(num):
    return num // 100 * 100


@shared_task
def refresh_marketting_bonuses():
    from main.models import ConstantVariable

    response = ConstantVariable.generate_bonus_schedule_dict()
    return response


@shared_task
def remit_marketting_bonus():
    from main.models import ConstantVariable

    if ConstantVariable.objects.all().last().seed_out_marketting_bonuses:
        ConstantVariable.remit_marketting_bonus()
    else:
        print("SEEDING IS OFF IN CONFIG")


@shared_task
def reduce_winnings_by_percent():
    from main.models import ConstantVariable, InstantCashoutPendingWinning

    const_obj = ConstantVariable.objects.all().last()

    if const_obj.suppress_a_percent_of_wins:
        all_wins = InstantCashoutPendingWinning.objects.filter(created_at__date=timezone.now().date()).count() + 1  # Plus 1 handles division by zero
        all_suppressed = InstantCashoutPendingWinning.objects.filter(created_at__date=timezone.now().date(), suppresed=True).count() + 1

        print("+++++++++++++++++++++++++++++++++++++++++")
        print("+++++++++++++++++++++++++++++++++++++++++")
        print("+++++++++++++++++++++++++++++++++++++++++")
        print(f"SUPPRESSION VALUE ::: {(all_wins / all_suppressed)}")
        print("+++++++++++++++++++++++++++++++++++++++++")
        print("+++++++++++++++++++++++++++++++++++++++++")
        if (all_wins / all_suppressed) > const_obj.suppression_percent:
            pending_win_to_deactivate = InstantCashoutPendingWinning.objects.filter(Q(is_avialable=True) | Q(is_available_for_pos=True)).last()

            if pending_win_to_deactivate:
                pending_win_to_deactivate.suppresed = True
                pending_win_to_deactivate.is_avialable = False
                pending_win_to_deactivate.is_available_for_pos = False
                pending_win_to_deactivate.is_for_pos_only = False
                # pending_win_to_deactivate.amount = pending_win_to_deactivate.amount/1000

                pending_win_to_deactivate.save()


@shared_task
def quika_new_icash_local_old_before_price_review(ticket_id):
    from django.db.models import F

    from main.models import (
        Agent,
        ConstantVariable,
        InstantCashoutPendingWinning,
        LottoTicket,
    )

    icash_to_use = ConstantVariable.objects.all().last().icash_to_use

    if icash_to_use == "OLD ICASH":
        return "OLD ICASH ACTIVE"

    instance: LottoTicket = LottoTicket.objects.get(id=ticket_id)

    if instance.number_of_ticket > 1 and ConstantVariable.objects.all().last().icash_to_use == "MIXED ICASH":
        print("PASSSING CANT LOG THIS")
        print("PASSSING CANT LOG THIS")
        print(f"TICKET NUBER {instance.number_of_ticket}")
        print("PASSSING CANT LOG THIS")
        return

    fields_to_exclude = {
        "system_generated_num",
    }
    # automatically populate a list with all fields, except the ones you want to exclude
    fields_to_update = [f.name for f in LottoTicket._meta.get_fields() if f.name not in fields_to_exclude and not f.auto_created]

    from math import ceil

    from django.conf import settings

    if settings.DEBUG:
        DEFAULT_AGENT_FOR_WEB = 43
        # DEFAULT_AGENT_FOR_WEB = 1

    else:
        # DEFAULT_AGENT_FOR_WEB = settings.DEFAULT_AGENT_ID
        DEFAULT_AGENT_FOR_WEB = 43

    if not instance.paid:
        return

    if not instance.agent_profile:
        instance.is_agent = True
        agent_profile = Agent.objects.filter(id=DEFAULT_AGENT_FOR_WEB)
        instance.agent_profile = agent_profile.last()
        instance.save()

    print("::::::::::::::1::::::::::::::")
    if instance.icash_2_counted or not instance.is_agent:
        return

    print("::::::::::::::2::::::::::::::")
    print("LOTTERY TYPE", instance.lottery_type)
    if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA", "VIRTUAL_SOCCER"]:
        return
    print("::::::::::::::3::::::::::::::")

    WIN_GIVEN_OUT = False
    LG_WIN_FACTOR = 1.5
    const_obj: ConstantVariable = ConstantVariable.objects.all().last()

    # if const_obj.icash_to_use != "NEW ICASH":
    #     return

    print("::::::::::::::4::::::::::::::")
    # def round_down(num):
    #     return num//100 * 100

    WHITE_WINNINGS = {
        200: {
            "least_win": 600,
            "minor_win": 800,
            "min_win": 1000,
            "mid_win": 1200,
            "max_win": 2500,
        },
        500: {
            "least_win": 1500,
            "minor_win": 1800,
            "min_win": 2000,
            "mid_win": 2500,
            "max_win": 4500,
        },
        750: {
            "least_win": 1400,
            "minor_win": 1700,
            "min_win": round_down(9000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(10800 / const_obj.icash_winnings_divisor),
            "max_win": round_down(15750 / const_obj.icash_winnings_divisor),
        },
        800: {
            "least_win": 1800,
            "minor_win": 2400,
            "min_win": round_down(11400 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18000 / const_obj.icash_winnings_divisor),
        },
        1250: {
            "least_win": 1250,
            "minor_win": 1950,
            "min_win": round_down(12000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(14000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18750 / const_obj.icash_winnings_divisor),
        },
        1300: {
            "least_win": 1300,
            "minor_win": 1700,
            "min_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(25000 / const_obj.icash_winnings_divisor),
        },
        1400: {
            "least_win": 2000,
            "minor_win": 2666,
            "min_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(18000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(27000 / const_obj.icash_winnings_divisor),
        },
    }

    BLACK_WINNINGS = {
        200: {
            "least_win": 600,
            "minor_win": 800,
            "min_win": 1000,
            "mid_win": 1200,
            "max_win": 1500,
        },
        500: {
            "least_win": 1200,
            "minor_win": 1400,
            "min_win": 1500,
            "mid_win": 2000,
            "max_win": 3000,
        },
        750: {
            "least_win": 1125,
            "minor_win": 1200,
            "min_win": round_down(9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
            "mid_win": round_down(10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
            "max_win": round_down(15750 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
        },
        800: {
            "least_win": 1802,
            "minor_win": 1801,
            "min_win": 1800,
            "mid_win": 2000,
            "max_win": 3500,
        },
        1250: {
            "least_win": 2500,
            "minor_win": 2350,
            "min_win": 2200,
            "mid_win": 2500,
            "max_win": 3500,
        },
        1300: {
            "least_win": 2750,
            "minor_win": 1800,
            "min_win": 2000,
            "mid_win": 3000,
            "max_win": 4000,
        },
        1400: {
            "least_win": 2002,
            "minor_win": 2001,
            "min_win": 2000,
            "mid_win": 2000,
            "max_win": 3800,
        },
    }

    # {"200": 0, "500": 0, "750": 0, "1000": 0, "1250": 0, "1300": 0, "1500": 0}
    print("           |||              ")
    print("           |||              ")
    print(
        "Stake Amount :::",
        instance.stake_amount,
        instance.number_of_ticket,
        instance.game_play_id,
    )
    print("           |||              ")
    print("           |||              ")
    actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

    agent = instance.agent_profile

    agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
    global_flavour_list = const_obj.icash_flavour_dict.get(str((actual_stake_amount)), [])

    const_obj = ConstantVariable.objects.all().last()

    if not len(agent_flavour_list):
        agent.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        agent.save()

    if not len(global_flavour_list):
        const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        const_obj.save()

    agent_current_flavour = agent.icash_flavour_dict[str((actual_stake_amount))][0]
    global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]

    agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[str((actual_stake_amount))][1:]
    const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

    new_agent_dict = dict(agent.icash_flavour_dict)
    new_global_dict = dict(const_obj.icash_flavour_dict)

    GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")

    # giver_threshold = InstantCashGiverThresholds.objects.first()
    # giver_threshold.GIVER_THRESHOLD_DICT[actual_stake_amount] = F(GIVER_THRESHOLD_DICT[actual_stake_amount]) + 1
    # giver_threshold.save()

    const_obj = ConstantVariable.objects.all().last()

    try:
        global_count_to_giver = const_obj.new_quika_icash_count_to_giver[str(actual_stake_amount)]
        agent_count_to_giver = agent.new_quika_icash_count_to_giver[str(actual_stake_amount)]
    except Exception:
        base_global_giver_dict = const_obj.new_quika_icash_count_to_giver
        base_global_giver_dict[str(actual_stake_amount)] = 0

        base_agent_giver_dict = agent.new_quika_icash_count_to_giver
        base_agent_giver_dict[str(actual_stake_amount)] = 0

        ConstantVariable.objects.all().update(new_quika_icash_count_to_giver=base_global_giver_dict)
        agent.new_quika_icash_count_to_giver = base_agent_giver_dict
        agent.save()

        global_count_to_giver = -999
        agent_count_to_giver = -999

    const_obj = ConstantVariable.objects.all().last()
    GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

    # GIVER_THRESHOLD = GIVER_THRESHOLD_DICT[str(actual_stake_amount)]

    WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

    GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
    AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

    BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
    BONUS_WINNING = WINNINGS_DICT[BONUS_FLAVOUR]
    BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

    print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

    const_obj = ConstantVariable.objects.all().first()

    def get_averages(band):
        values = [band["least_win"]] + [band["minor_win"]] + [band["min_win"]] + [band["mid_win"]] + [band["max_win"]]
        return sum(values) / 5

    if instance.lottery_type == "INSTANT_CASHOUT" or instance.lottery_type == "QUIKA":
        if instance.paid and instance.effective_rtp > 0 and instance.is_agent:
            print("HERE TOO..!!!!!!")

            agent = instance.agent_profile
            agent.icash_sold = agent.icash_sold + 1
            instance.icash_2_counted = True
            instance.icash_counted = True
            instance.save(update_fields=fields_to_update)

            tickets_per_teir = instance.number_of_ticket

            band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
            rtp = instance.effective_rtp * instance.number_of_ticket

            print(
                "\n BAND Average +++++++++++++++++++++++++++++++++++++++++++++++++>",
                band_average,
                "<+++++++++++++++++++++++++++++++++++++++++++++++++\n",
            )

            num_win_tickets = rtp / band_average
            print(
                "NUM WINNING TICKS ::::",
                num_win_tickets,
                "\n",
                f"Inst-RTP::{instance.rtp}\n",
                f"Inst-Effective-RTP::{instance.effective_rtp}\n",
                f"Num tiks{instance.number_of_ticket}\n",
                f"Tot RTP{rtp}\n",
                band_average,
                "\n",
                GLOBAL_WINNINGS[int(actual_stake_amount)],
            )
            percent_win_ticket = num_win_tickets / instance.number_of_ticket

            count_before = (1 / percent_win_ticket) / instance.number_of_ticket
            icash_excesses_from_count_before = (instance.number_of_ticket * instance.effective_rtp) * (ceil(count_before) - count_before)
            print("EXCESSES FROM CB4 ::::", icash_excesses_from_count_before)

            print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
            print(
                "A-FLAVOUR ::::",
                agent_current_flavour,
                "G-FLAVOUR BEF ::::",
                global_current_flavour,
            )
            print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

            if not agent.icash_sold_dict.get(str(actual_stake_amount)):
                agent.icash_sold_dict[str(actual_stake_amount)] = 0

            if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
                const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

            agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) + 1

            const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + 1
            const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + 1
            )

            win_rank_ratio = ["least_win"] + ["minor_win"] + ["min_win"] + ["mid_win"] + ["max_win"]
            win_rank_choice = random.choice(win_rank_ratio)

            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JINSTANCE GAME ID", instance.game_play_id)
            print("COUNT TO GIVER", global_count_to_giver, "GTHRES", GIVER_THRESHOLD)
            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("                                      ")

            if const_obj.icash_to_use == "NEW ICASH":
                const_obj.save()
                agent.save()

                if global_count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "GLOBAL":
                    # Handle giver for GLOBAL
                    print("HERE 1..!!!")
                    print("RELEASING GIVER")
                    print(
                        "COUNT TO GIVER",
                        global_count_to_giver,
                        "GTHRES",
                        GIVER_THRESHOLD,
                    )
                    print("                                      ")
                    print("                                      ")
                    print("                                      ")
                    print("                                      ")

                    const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold - 1
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                        const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) - 1
                    )
                    const_obj.save()
                    WIN_GIVEN_OUT = True

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"GIVER-G-{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    # const_obj.count_to_giver = 0
                    # const_obj.save()
                    ConstantVariable.update_giver(actual_stake_amount, reset=True)
                    return

                if agent_count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "LOCAL":
                    # Handle giver for LOCAL
                    print("HERE 2..!!!")

                    agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
                    agent.save()
                    WIN_GIVEN_OUT = True
                    # Offset the number of tickets sold because of the giver

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        "GIVER-L-{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        agent=agent,
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    # agent.count_to_giver = 0
                    # agent.save()
                    agent.update_giver(actual_stake_amount, reset=True)
                    return

                if (
                    (const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before
                ) and const_obj.icash2_draw_mode == "GLOBAL":
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"G-{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice] / LG_WIN_FACTOR,
                        ),
                        count_before=count_before,
                        flavour=global_current_flavour,
                    )
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
                    const_obj.icash_excesses_from_count_before = const_obj.icash_excesses_from_count_before + icash_excesses_from_count_before
                    const_obj.save()

                    # const_obj.count_to_giver = const_obj.count_to_giver + 1
                    ConstantVariable.update_giver(actual_stake_amount)
                    WIN_GIVEN_OUT = True

                if ((agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before) and const_obj.icash2_draw_mode == "LOCAL":
                    print("HERE 6..!!!")
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        "LL-{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][win_rank_choice] / LG_WIN_FACTOR,
                        ),
                        agent=agent,
                        count_before=count_before,
                        flavour=agent_current_flavour,
                    )
                    agent.icash_sold_dict[str(actual_stake_amount)] = 0
                    # agent.count_to_giver = agent.count_to_giver + 1
                    # agent.save()

                    agent.icash_excesses_from_count_before = agent.icash_excesses_from_count_before + icash_excesses_from_count_before
                    agent.save()
                    agent.update_giver(actual_stake_amount)
                    WIN_GIVEN_OUT = True

                    # if (
                    #     agent.icash2_draw_mode == "LOCAL"
                    #     and const_obj.icash2_draw_mode == "GLOBAL"
                    # ):
                    """New implementation for local winning"""

                # TURN OFF LOCAL DRAW
                if agent.icash2_draw_mode == "LOCAL" and const_obj.icash2_draw_mode == "GLOBAL":
                    # if False:

                    """New implementation for local winning"""

                    icash_sold_game_ids = list(agent.icash_sold_game_ids) + [instance.game_play_id]
                    Agent.objects.filter(id=instance.agent_profile.id).update(
                        icash_sold_game_ids=icash_sold_game_ids,
                        icash_sold_amount=F("icash_sold_amount") + instance.effective_rtp,
                    )

                    const_obj = ConstantVariable.objects.all().last()
                    win_tiers = const_obj.icash2_quika_giveout_tier

                    if win_tiers:
                        win_rank_choice = win_tiers[0]
                    else:
                        win_tiers = [
                            "least_win",
                            "minor_win",
                            "min_win",
                            "mid_win",
                            "least_win",
                            "minor_win",
                            "min_win",
                            "mid_win",
                            "max_win",
                        ]
                        win_rank_choice = win_tiers[0]

                    winning = AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][win_rank_choice]

                    agent = Agent.objects.get(id=instance.agent_profile.id)

                    tickets_sold_amount = agent.icash_sold_amount
                    agent.unique_tickets_sold

                    difference_after_payout = tickets_sold_amount - winning

                    print("**************")
                    print("**************")
                    print("**************")
                    print("              ")
                    print("              ")
                    print("AGENT ::,", instance.agent_profile)
                    print("POSSIBLE WINNING::::", winning)
                    print("WINNING RANK::::", win_rank_choice)
                    print("ACTIVE FLAVOUR::::", agent_current_flavour)
                    print("ACTUAL STAKE AMOUNT::::", actual_stake_amount)
                    print("AMOUNT SOLD::::", agent.icash_sold_amount)
                    print("I GOT HERE NOW..!!!")
                    print("              ")
                    print("              ")
                    print("**************")
                    print("**************")
                    print("**************")

                    if difference_after_payout >= 0:
                        if len(set(agent.icash_sold_game_ids)) <= 7 and actual_stake_amount == 200:
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
                            print(
                                "SALES :::",
                                len(set(agent.icash_sold_game_ids)),
                                "@@@@@@@@@@@",
                            )
                            print("@@@@@@@@@                    @@@@@@@@@@@")
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")

                        else:
                            ConstantVariable.objects.all().update(icash2_quika_giveout_tier=win_tiers[1:])

                            Agent.objects.filter(id=instance.agent_profile.id).update(
                                icash_sold_game_ids=[],
                                icash_sold_amount=difference_after_payout,
                            )
                            agent = Agent.objects.get(id=instance.agent_profile.id)

                            InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                                "LA{datetime.now()}",
                                (
                                    win_rank_choice,
                                    actual_stake_amount,
                                    winning / LG_WIN_FACTOR,
                                ),
                                agent=agent,
                                count_before=count_before,
                                flavour=agent_current_flavour,
                            )
                            agent.icash_sold_dict[str(actual_stake_amount)] = 0

                            agent.icash_excesses_from_count_before = agent.icash_excesses_from_count_before + icash_excesses_from_count_before
                            agent.save()
                            agent.update_giver(actual_stake_amount)
                            WIN_GIVEN_OUT = True

                    else:
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")

            ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
            number_of_single_tickets_in_largest_ticket = random.randint(int(ticket_min), int(ticket_max))

            if WIN_GIVEN_OUT:
                ConstantVariable.objects.all().update(icash_flavour_dict=new_agent_dict)
                Agent.objects.filter().update(icash_flavour_dict=new_global_dict)

            (
                thresholdx,
                thresholdy,
                thresholdz,
            ) = const_obj.excesses_giveout_threshold.split(",")
            # excess_giveout_amount = random.choice(
            #     list(range(int(thresholdx), int(thresholdy), int(thresholdz)))
            # )
            excess_giveout_amount = random.choice([600, 700, 800, 900, 1000])
            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            print("EXCESS AMOUNT :::", excess_giveout_amount)
            BALANCE_AFTER_EXCESS_GIVEOUT = 0

            if const_obj.icash_excesses_from_count_before >= excess_giveout_amount and const_obj.icash2_draw_mode == "GLOBAL":
                InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                    "G-EXCESS-{datetime.now()}",
                    ("min_win", 200, excess_giveout_amount),
                    count_before=number_of_single_tickets_in_largest_ticket,
                    flavour="CASHBACK",
                )

                BALANCE_AFTER_EXCESS_GIVEOUT = const_obj.icash_excesses_from_count_before - excess_giveout_amount
                ConstantVariable.objects.all().update(icash_excesses_from_count_before=BALANCE_AFTER_EXCESS_GIVEOUT)

            if agent.icash_excesses_from_count_before >= excess_giveout_amount and const_obj.icash2_draw_mode == "LOCAL":
                InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                    "L-EXCESS-{datetime.now()}",
                    ("min_win", 200, excess_giveout_amount),
                    agent,
                    count_before=number_of_single_tickets_in_largest_ticket,
                    flavour="CASHBACK",
                )

                BALANCE_AFTER_EXCESS_GIVEOUT = agent.icash_excesses_from_count_before - excess_giveout_amount
                agent.icash_excesses_from_count_before = BALANCE_AFTER_EXCESS_GIVEOUT
                agent.save()

            tickets_to_bonus = agent.icash_sold / number_of_single_tickets_in_largest_ticket

            if tickets_to_bonus >= const_obj.icash2_local_bonus_threshold and instance.lottery_type == "INSTANT_CASHOUT":
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")

                if agent_tickets.exists():
                    [int(amount * qty) for amount, qty in list(agent_tickets)]
                    bonus_band = 200  # int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_local_bonus_available = const_obj.icash2_local_bonus_available - BLACK_WINNINGS[bonus_band][BONUS_TIER]

                    if not const_obj.icash2_local_bonus_available <= 0:
                        const_obj.save()
                        agent.icash_sold = 0
                        agent.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            "BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )

                    agent.icash2_bonus_left = agent.icash2_bonus_left - BLACK_WINNINGS[bonus_band][BONUS_TIER]

                    if not agent.icash2_bonus_left <= 0:
                        agent.icash_sold = 0
                        agent.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            "BONUS-LOC-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )

            if (
                const_obj.global_agent_icash_sold / number_of_single_tickets_in_largest_ticket
            ) >= const_obj.icash2_local_bonus_threshold and instance.lottery_type == "INSTANT_CASHOUT":
                print("HERE 9..!!!")
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")
                if agent_tickets.exists():
                    [int(amount * qty) for amount, qty in list(agent_tickets)]

                    bonus_band = 200  # int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_global_bonus_available = const_obj.icash2_global_bonus_available - BONUS_WINNING[bonus_band][BONUS_TIER]
                    const_obj.global_agent_icash_sold = 0

                    if not const_obj.icash2_global_bonus_available < 0:
                        const_obj.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            "GLOB-BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )


@shared_task
def quika_new_icash_local_11_may_2024(ticket_id):
    from main.models import (
        Agent,
        ConstantVariable,
        InstantCashoutPendingWinning,
        LottoTicket,
    )

    fields_to_exclude = {
        "system_generated_num",
    }

    icash_to_use = ConstantVariable.objects.all().last().icash_to_use

    if icash_to_use == "OLD ICASH":
        return "OLD ICASH ACTIVE"

    instance: LottoTicket = LottoTicket.objects.get(id=ticket_id)
    if instance.paid is False:
        return

    actual_rtp = instance.effective_rtp

    if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA", "VIRTUAL_SOCCER"]:
        return

    """
    CHECK IF AMOUNT PAID IS 50 OR 100 NAIRA
    THEN HANDLE IT'S DRAW AND WINNING DIFFERENTLY USING THE DROUGHT CONTROL

    50 naira
        3 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning
        5 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        7 ticket play
            give out a bonus winning
        3 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning
        5 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        7 ticket play
            give out a bonus winning
        3 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning

        RESET

    100 naira
        2 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning
        5 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        5 ticket play
            give out a bonus winning
        2 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning
        5 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        2 ticket play
            give out a bonus winning
        2 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning

        15 times, then reset

    Drought control Price combination
    200:
        bonus: 1
            100
    200:
        main: 2
            200
    300:
        bonus: 3
            200
    400:
        bonus: 4
            300
    200:
        main: 5
            100
    450:
        bonus: 6
            300
    200:
        bonus: 7
            150
    200:
        main: 8
            200
    300:
        bonus: 9
            200
    400:
        bonus: 10
            300
    200:
        main: 11
            100
    300:
        bonus: 12
            200
    200:
        bonus: 13
            150
    200:
        main: 14
            100
    300:
        bonus: 15
            200
    """

    # DROUGHT CONTROL #
    _winning_amount = 0
    if (instance.amount_paid == 50 or instance.amount_paid == 100) and (
        instance.paid is True and instance.icash_counted is False and instance.icash_2_counted is False
    ):
        const = ConstantVariable.objects.first()
        _winning_amount = ConstantVariable.release_draft(instance.effective_rtp)
        print(f":::::::::::: WINNING :::: {_winning_amount}")

        if _winning_amount != 0 and const.drought_enabled:
            InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                f"DROUGHT-GIVER-{datetime.now()}",
                (
                    "min_win",
                    200,
                    _winning_amount,
                ),
                count_before=0,
                flavour="CASHBACK",
                is_for_pos_only=False,
                is_avail_50_n_100=True,
            )

            # fields_to_update = [
            #     f.name
            #     for f in LottoTicket._meta.get_fields()
            #     if f.name not in fields_to_exclude and not f.auto_created
            # ]

            # if not instance.agent_profile:
            #     agent_profile = Agent.objects.filter(id=DEFAULT_AGENT_FOR_WEB)
            #     instance.agent_profile = agent_profile.last()

            # agent = instance.agent_profile
            # instance.icash_counted = True
            # instance.icash_2_counted = True
            # # agent.icash_sold = agent.icash_sold + 1
            # instance.save(update_fields=fields_to_update)

            # return

        # DROUGHT CONTROL #

    print(f":::::::::::: WINNING AMOUNT :::: {_winning_amount}")
    if instance.number_of_ticket > 1 and ConstantVariable.objects.all().last().icash_to_use == "MIXED ICASH":
        print("PASSSING CANT LOG THIS")
        print("PASSSING CANT LOG THIS")
        print(f"TICKET NUBER {instance.number_of_ticket}")
        print("PASSSING CANT LOG THIS")
        return

    # automatically populate a list with all fields, except the ones you want to exclude
    fields_to_update = [f.name for f in LottoTicket._meta.get_fields() if f.name not in fields_to_exclude and not f.auto_created]

    from math import ceil

    from django.conf import settings

    if settings.DEBUG:
        DEFAULT_AGENT_FOR_WEB = 43
        # DEFAULT_AGENT_FOR_WEB = 1

    else:
        # DEFAULT_AGENT_FOR_WEB = settings.DEFAULT_AGENT_ID
        DEFAULT_AGENT_FOR_WEB = 43

    if not instance.paid:
        return

    if not instance.agent_profile:
        instance.is_agent = True
        agent_profile = Agent.objects.filter(id=DEFAULT_AGENT_FOR_WEB)
        instance.agent_profile = agent_profile.last()
        instance.save()

    print("::::::::::::::1::::::::::::::")
    if instance.icash_2_counted or not instance.is_agent:
        return

    print("::::::::::::::2::::::::::::::")
    print("LOTTERY TYPE", instance.lottery_type)
    if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA", "VIRTUAL_SOCCER"]:
        return
    print("::::::::::::::3::::::::::::::")

    WIN_GIVEN_OUT = False
    LG_WIN_FACTOR = 1
    const_obj: ConstantVariable = ConstantVariable.objects.all().last()

    # if const_obj.icash_to_use != "NEW ICASH":
    #     return

    print("::::::::::::::4::::::::::::::")
    # def round_down(num):
    #     return num//100 * 100

    WHITE_WINNINGS = {
        200: {
            "least_win": 1200,
            "minor_win": 1500,
            "min_win": 1800,
            "mid_win": 2000,
            "max_win": 2400,
        },
        500: {
            "least_win": 1500,
            "minor_win": 1800,
            "min_win": 2000,
            "mid_win": 2500,
            "max_win": 4500,
        },
        750: {
            "least_win": 1400,
            "minor_win": 1700,
            "min_win": round_down(9000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(10800 / const_obj.icash_winnings_divisor),
            "max_win": round_down(15750 / const_obj.icash_winnings_divisor),
        },
        800: {
            "least_win": 1800,
            "minor_win": 2400,
            "min_win": round_down(11400 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18000 / const_obj.icash_winnings_divisor),
        },
        1250: {
            "least_win": 1250,
            "minor_win": 1950,
            "min_win": round_down(12000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(14000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18750 / const_obj.icash_winnings_divisor),
        },
        1300: {
            "least_win": 1300,
            "minor_win": 1700,
            "min_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(25000 / const_obj.icash_winnings_divisor),
        },
        1400: {
            "least_win": 2000,
            "minor_win": 2666,
            "min_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(18000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(27000 / const_obj.icash_winnings_divisor),
        },
    }

    BLACK_WINNINGS = {
        200: {
            "least_win": 500,
            "minor_win": 600,
            "min_win": 800,
            "mid_win": 1200,
            "max_win": 1500,
        },
        500: {
            "least_win": 1200,
            "minor_win": 1400,
            "min_win": 1500,
            "mid_win": 2000,
            "max_win": 3000,
        },
        750: {
            "least_win": 1125,
            "minor_win": 1200,
            "min_win": round_down(9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
            "mid_win": round_down(10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
            "max_win": round_down(15750 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
        },
        800: {
            "least_win": 1802,
            "minor_win": 1801,
            "min_win": 1800,
            "mid_win": 2000,
            "max_win": 3500,
        },
        1250: {
            "least_win": 2500,
            "minor_win": 2350,
            "min_win": 2200,
            "mid_win": 2500,
            "max_win": 3500,
        },
        1300: {
            "least_win": 2750,
            "minor_win": 1800,
            "min_win": 2000,
            "mid_win": 3000,
            "max_win": 4000,
        },
        1400: {
            "least_win": 2002,
            "minor_win": 2001,
            "min_win": 2000,
            "mid_win": 2000,
            "max_win": 3800,
        },
    }

    # {"200": 0, "500": 0, "750": 0, "1000": 0, "1250": 0, "1300": 0, "1500": 0}
    print("           |||              ")
    print("           |||              ")
    print(
        "Stake Amount :::",
        instance.stake_amount,
        instance.number_of_ticket,
        instance.game_play_id,
    )
    print("           |||              ")
    print("           |||              ")

    """
    THE REASON WHY I'M CHANGING THIS IS BEACUSE, 200, 500, 750, 1000, 1250, 1300, 1400
    IS THE EXPECTED STAKE AMOUNT, BUT IN CASES WHERE 200 IS RECORDED MULTIPLE TIMES FOR A GAME PLAY, WE'VE TO CHECK IT AS A SINGLE 200
    NOT THE MULTIPLICATION OF NUMBER OF NUMBER OF TICKETS

    """
    if instance.stake_amount == 200:
        actual_stake_amount = str(int(instance.stake_amount))
    else:
        actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

    agent = instance.agent_profile

    agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
    global_flavour_list = const_obj.icash_flavour_dict.get(str((actual_stake_amount)), [])

    const_obj = ConstantVariable.objects.all().last()

    if not len(agent_flavour_list):
        agent.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        agent.save()

    if not len(global_flavour_list):
        const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        const_obj.save()

    agent_current_flavour = "BLACK"  # agent.icash_flavour_dict[str((actual_stake_amount))][0]
    global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]
    print(f"****************************************************************")
    print(f"****************************************************************")
    print(f"********************     GLOBAl FLAVOUR     ********************")
    print(f"******************** {global_current_flavour} ********************")
    print(f"******************** {const_obj.icash_flavour_dict[str((actual_stake_amount))]} ********************")
    print(f"****************************************************************")
    print(f"****************************************************************")
    print(f"****************************************************************")

    agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[str((actual_stake_amount))][1:]
    const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

    new_agent_dict = dict(agent.icash_flavour_dict)
    new_global_dict = dict(const_obj.icash_flavour_dict)

    GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")

    # giver_threshold = InstantCashGiverThresholds.objects.first()
    # giver_threshold.GIVER_THRESHOLD_DICT[actual_stake_amount] = F(GIVER_THRESHOLD_DICT[actual_stake_amount]) + 1
    # giver_threshold.save()

    const_obj = ConstantVariable.objects.all().last()

    try:
        global_count_to_giver = const_obj.new_quika_icash_count_to_giver[str(actual_stake_amount)]
        agent.new_quika_icash_count_to_giver[str(actual_stake_amount)]
    except Exception:
        base_global_giver_dict = const_obj.new_quika_icash_count_to_giver
        base_global_giver_dict[str(actual_stake_amount)] = 0

        base_agent_giver_dict = agent.new_quika_icash_count_to_giver
        base_agent_giver_dict[str(actual_stake_amount)] = 0

        ConstantVariable.objects.all().update(new_quika_icash_count_to_giver=base_global_giver_dict)
        agent.new_quika_icash_count_to_giver = base_agent_giver_dict
        agent.save()

        global_count_to_giver = -999

    const_obj = ConstantVariable.objects.all().last()
    GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

    # GIVER_THRESHOLD = GIVER_THRESHOLD_DICT[str(actual_stake_amount)]

    WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

    GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
    AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

    BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
    BONUS_WINNING = WINNINGS_DICT[BONUS_FLAVOUR]
    BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

    print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

    const_obj = ConstantVariable.objects.all().first()

    def get_averages(band):
        values = [band["least_win"]] + [band["minor_win"]] + [band["min_win"]] + [band["mid_win"]] + [band["max_win"]]
        return sum(values) / 5

    if instance.lottery_type == "INSTANT_CASHOUT" or instance.lottery_type == "QUIKA":
        if instance.paid and actual_rtp > 0 and instance.is_agent:
            print("HERE TOO..!!!!!!")

            agent = instance.agent_profile
            agent.icash_sold = agent.icash_sold + 1
            instance.icash_2_counted = True
            instance.icash_counted = True
            instance.save(update_fields=fields_to_update)

            tickets_per_teir = instance.number_of_ticket
            print("GLOBAL_WINNINGS", GLOBAL_WINNINGS)
            band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
            rtp = actual_rtp * instance.number_of_ticket

            print(
                "\n BAND Average +++++++++++++++++++++++++++++++++++++++++++++++++>",
                band_average,
                "<+++++++++++++++++++++++++++++++++++++++++++++++++\n",
            )

            num_win_tickets = const_obj.icash_rtps.get(str(int(instance.stake_amount)), rtp) / band_average
            fixed_rtp = const_obj.icash_rtps.get(str(int(instance.stake_amount)), 0)

            if fixed_rtp == 0:
                fixed_rtp = 1

            scaled_rtp = instance.effective_rtp / fixed_rtp

            if fixed_rtp == 1:
                scaled_rtp = 1

            if _winning_amount:  # UPDATE[SET RTP BACK 1TICKET AFTER EVERY WIN BECAUSE OF DROUGHT CONTROL]
                scaled_rtp = -0.50

            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(f"WINNING AMOUNT HERE:::::  :::::::::::::::::::::::: {_winning_amount}")
            print("SCALLED RTP :::::", scaled_rtp)
            print("INSTANCE RTP :::::", instance.effective_rtp)
            print("FIXED RTP :::::", fixed_rtp)
            print(
                "FIXED RTP :::::",
                const_obj.icash_rtps.get(str(instance.stake_amount), 0),
            )
            print("Stake amouint :::::", int(instance.stake_amount))
            print("CONST :::::", const_obj.icash_rtps)
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")

            print(
                "NUM WINNING TICKS ::::",
                num_win_tickets,
                "\n",
                "Inst-RTP::{actual_rtp}\n",
                "Num tiks{instance.number_of_ticket}\n",
                "Tot RTP{rtp}\n",
                band_average,
                "\n",
                GLOBAL_WINNINGS[int(actual_stake_amount)],
            )
            percent_win_ticket = num_win_tickets / instance.number_of_ticket

            count_before = (1 / percent_win_ticket) / instance.number_of_ticket
            icash_excesses_from_count_before = (instance.number_of_ticket * actual_rtp) * (ceil(count_before) - count_before)
            print("EXCESSES FROM CB4 ::::", icash_excesses_from_count_before)
            print(":::::::::::COUNT BEFORE ::::", count_before)

            print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
            print(
                "A-FLAVOUR ::::",
                agent_current_flavour,
                "G-FLAVOUR BEF ::::",
                global_current_flavour,
            )
            print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

            if not agent.icash_sold_dict.get(str(actual_stake_amount)):
                agent.icash_sold_dict[str(actual_stake_amount)] = 0

            if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
                const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

            agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) + scaled_rtp

            const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + scaled_rtp
            const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + scaled_rtp
            )

            win_rank_ratio = ["least_win"] + ["minor_win"] + ["min_win"] + ["mid_win"] + ["max_win"]
            win_rank_choice = random.choice(win_rank_ratio)

            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JINSTANCE GAME ID", instance.game_play_id)
            print("COUNT TO GIVER", global_count_to_giver, "GTHRES", GIVER_THRESHOLD)
            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("                                      ")

            if const_obj.icash_to_use == "NEW ICASH":
                const_obj.save()
                agent.save()

                if global_count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "GLOBAL":
                    # Handle giver for GLOBAL
                    print("HERE 1..!!!")
                    print("RELEASING GIVER")
                    print(
                        "COUNT TO GIVER",
                        global_count_to_giver,
                        "GTHRES",
                        GIVER_THRESHOLD,
                    )
                    print("                                      ")
                    print("                                      ")
                    print("                                      ")
                    print("                                      ")

                    const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold - 1
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                        const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) - 1
                    )
                    const_obj.save()
                    WIN_GIVEN_OUT = True

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"GIVER-G-{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    # const_obj.count_to_giver = 0
                    # const_obj.save()
                    ConstantVariable.update_giver(actual_stake_amount, reset=True)
                    return

                if False:  # OVERIDE LOCAL WINNINGS
                    # if (
                    #         agent_count_to_giver >= GIVER_THRESHOLD
                    #         and const_obj.icash2_draw_mode == "LOCAL"
                    # ):
                    # Handle giver for LOCAL
                    print("HERE 2..!!!")

                    agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
                    agent.save()
                    WIN_GIVEN_OUT = True
                    # Offset the number of tickets sold because of the giver

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        "GIVER-L-{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        agent=agent,
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    # agent.count_to_giver = 0
                    # agent.save()
                    agent.update_giver(actual_stake_amount, reset=True)
                    return

                if (
                    (const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before
                ) and const_obj.icash2_draw_mode == "GLOBAL":
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"G-{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice] / LG_WIN_FACTOR,
                        ),
                        count_before=count_before,
                        flavour=global_current_flavour,
                    )
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
                    const_obj.icash_excesses_from_count_before = const_obj.icash_excesses_from_count_before + icash_excesses_from_count_before

                    const_obj.save()

                    # const_obj.count_to_giver = const_obj.count_to_giver + 1
                    ConstantVariable.update_giver(actual_stake_amount)
                    WIN_GIVEN_OUT = True

                if False:  # OVERIDE LOCAL WINNINGS
                    # if (
                    #         (agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir)
                    #         >= count_before
                    # ) and const_obj.icash2_draw_mode == "LOCAL":
                    print("HERE 6..!!!")
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        "LL-{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][win_rank_choice] / LG_WIN_FACTOR,
                        ),
                        agent=agent,
                        count_before=count_before,
                        flavour=agent_current_flavour,
                    )
                    agent.icash_sold_dict[str(actual_stake_amount)] = 0
                    # agent.count_to_giver = agent.count_to_giver + 1
                    # agent.save()

                    agent.icash_excesses_from_count_before = agent.icash_excesses_from_count_before + icash_excesses_from_count_before
                    agent.save()
                    agent.update_giver(actual_stake_amount)
                    WIN_GIVEN_OUT = True

                    # if (
                    #     agent.icash2_draw_mode == "LOCAL"
                    #     and const_obj.icash2_draw_mode == "GLOBAL"
                    # ):
                    """New implementation for local winning"""

                # TURN OFF LOCAL DRAW
                if False:  # OVERIDE LOCAL WINNINGS
                    # if (
                    #         agent.icash2_draw_mode == "LOCAL"
                    #         and const_obj.icash2_draw_mode == "GLOBAL"
                    # ):
                    # if False:

                    """New implementation for local winning"""

                    icash_sold_game_ids = list(agent.icash_sold_game_ids) + [instance.game_play_id]
                    Agent.objects.filter(id=instance.agent_profile.id).update(
                        icash_sold_game_ids=icash_sold_game_ids,
                        icash_sold_amount=F("icash_sold_amount") + actual_rtp,  # noqa
                    )

                    const_obj = ConstantVariable.objects.all().last()
                    win_tiers = const_obj.icash2_quika_giveout_tier

                    if win_tiers:
                        win_rank_choice = win_tiers[0]
                    else:
                        win_tiers = [
                            "least_win",
                            "minor_win",
                            "min_win",
                            "mid_win",
                            "least_win",
                            "minor_win",
                            "min_win",
                            "mid_win",
                            "max_win",
                        ]
                        win_rank_choice = win_tiers[0]

                    winning = AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][win_rank_choice]

                    agent = Agent.objects.get(id=instance.agent_profile.id)

                    tickets_sold_amount = agent.icash_sold_amount
                    agent.unique_tickets_sold

                    difference_after_payout = tickets_sold_amount - winning

                    print("**************")
                    print("**************")
                    print("**************")
                    print("              ")
                    print("              ")
                    print("AGENT ::,", instance.agent_profile)
                    print("POSSIBLE WINNING::::", winning)
                    print("WINNING RANK::::", win_rank_choice)
                    print("ACTIVE FLAVOUR::::", agent_current_flavour)
                    print("ACTUAL STAKE AMOUNT::::", actual_stake_amount)
                    print("AMOUNT SOLD::::", agent.icash_sold_amount)
                    print("I GOT HERE NOW..!!!")
                    print("              ")
                    print("              ")
                    print("**************")
                    print("**************")
                    print("**************")

                    if difference_after_payout >= 0:
                        if len(set(agent.icash_sold_game_ids)) <= 7 and actual_stake_amount == 200:
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
                            print(
                                "SALES :::",
                                len(set(agent.icash_sold_game_ids)),
                                "@@@@@@@@@@@",
                            )
                            print("@@@@@@@@@                    @@@@@@@@@@@")
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")

                        else:
                            ConstantVariable.objects.all().update(icash2_quika_giveout_tier=win_tiers[1:])

                            Agent.objects.filter(id=instance.agent_profile.id).update(
                                icash_sold_game_ids=[],
                                icash_sold_amount=difference_after_payout,
                            )
                            agent = Agent.objects.get(id=instance.agent_profile.id)

                            InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                                "LA{datetime.now()}",
                                (
                                    win_rank_choice,
                                    actual_stake_amount,
                                    winning / LG_WIN_FACTOR,
                                ),
                                agent=agent,
                                count_before=count_before,
                                flavour=agent_current_flavour,
                            )
                            agent.icash_sold_dict[str(actual_stake_amount)] = 0

                            agent.icash_excesses_from_count_before = agent.icash_excesses_from_count_before + icash_excesses_from_count_before
                            agent.save()
                            agent.update_giver(actual_stake_amount)
                            WIN_GIVEN_OUT = True

                    else:
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")

            ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
            number_of_single_tickets_in_largest_ticket = random.randint(int(ticket_min), int(ticket_max))

            if WIN_GIVEN_OUT:
                ConstantVariable.objects.all().update(icash_flavour_dict=new_global_dict)
                Agent.objects.filter().update(icash_flavour_dict=new_agent_dict)

            (
                thresholdx,
                thresholdy,
                thresholdz,
            ) = const_obj.excesses_giveout_threshold.split(",")
            # excess_giveout_amount = random.choice(
            #     list(range(int(thresholdx), int(thresholdy), int(thresholdz)))
            # )
            excess_giveout_amount = random.choice([600, 700, 800, 900, 1000])
            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            print("EXCESS AMOUNT :::", excess_giveout_amount)
            BALANCE_AFTER_EXCESS_GIVEOUT = 0

            if const_obj.icash_excesses_from_count_before >= excess_giveout_amount and const_obj.icash2_draw_mode == "GLOBAL":
                InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                    "G-EXCESS-{datetime.now()}",
                    ("min_win", 200, excess_giveout_amount),
                    count_before=number_of_single_tickets_in_largest_ticket,
                    flavour="CASHBACK",
                )

                BALANCE_AFTER_EXCESS_GIVEOUT = const_obj.icash_excesses_from_count_before - excess_giveout_amount
                ConstantVariable.objects.all().update(icash_excesses_from_count_before=BALANCE_AFTER_EXCESS_GIVEOUT)

            if False:  # OVERIDE LOCAL WINNINGS
                # if (
                #         agent.icash_excesses_from_count_before >= excess_giveout_amount
                #         and const_obj.icash2_draw_mode == "LOCAL"
                # ):
                InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                    "L-EXCESS-{datetime.now()}",
                    ("min_win", 200, excess_giveout_amount),
                    agent,
                    count_before=number_of_single_tickets_in_largest_ticket,
                    flavour="CASHBACK",
                )

                BALANCE_AFTER_EXCESS_GIVEOUT = agent.icash_excesses_from_count_before - excess_giveout_amount
                agent.icash_excesses_from_count_before = BALANCE_AFTER_EXCESS_GIVEOUT
                agent.save()

            agent.icash_sold / number_of_single_tickets_in_largest_ticket

            if False:  # OVERIDE LOCAL WINNINGS
                # if (
                #         tickets_to_bonus >= const_obj.icash2_local_bonus_threshold
                #         and instance.lottery_type == "INSTANT_CASHOUT"
                # ):
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")

                if agent_tickets.exists():
                    [int(amount * qty) for amount, qty in list(agent_tickets)]
                    bonus_band = 200  # int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_local_bonus_available = const_obj.icash2_local_bonus_available - BLACK_WINNINGS[bonus_band][BONUS_TIER]

                    if not const_obj.icash2_local_bonus_available <= 0:
                        const_obj.save()
                        agent.icash_sold = 0
                        agent.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            "BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )

                    agent.icash2_bonus_left = agent.icash2_bonus_left - BLACK_WINNINGS[bonus_band][BONUS_TIER]

                    if not agent.icash2_bonus_left <= 0:
                        agent.icash_sold = 0
                        agent.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            "BONUS-LOC-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )

            if (
                const_obj.global_agent_icash_sold / number_of_single_tickets_in_largest_ticket
            ) >= const_obj.icash2_local_bonus_threshold and instance.lottery_type == "INSTANT_CASHOUT":
                print("HERE 9..!!!")
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")
                if agent_tickets.exists():
                    [int(amount * qty) for amount, qty in list(agent_tickets)]

                    bonus_band = 200  # int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_global_bonus_available = const_obj.icash2_global_bonus_available - BONUS_WINNING[bonus_band][BONUS_TIER]
                    const_obj.global_agent_icash_sold = 0

                    if not const_obj.icash2_global_bonus_available < 0:
                        const_obj.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            "GLOB-BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )


@shared_task
def quika_new_icash_local(ticket_id):

    from main.models import DrawLog

    DrawLog.create_draw_log(frequency_minutes=5, draw_name="INSTANT CASH")
    print("TICKET ID ::", ticket_id)

    from main.models import (
        Agent,
        ConstantVariable,
        InstantCashoutPendingWinning,
        LottoTicket,
    )
    print("HERE 10..!!!")
    fields_to_exclude = {"system_generated_num", "effective_rtp", "rtp"}

    icash_to_use = ConstantVariable.objects.all().last().icash_to_use

    print("HERE 11..!!!")
    if icash_to_use == "OLD ICASH":
        return "OLD ICASH ACTIVE"

    instance: LottoTicket = LottoTicket.objects.filter(id=ticket_id, game_id_treated=False).last()

    if instance is None or instance.paid is False:
        return
    print("HERE 12..!!!")
    LottoTicket.objects.filter(game_play_id=instance.game_play_id).update(
        game_id_treated=True
    )  # UPDATE ALL THESE TICKETS OF SAME GAMEPLAY ID BEFORE DB UPDATE.

    actual_rtp = instance.rtp * instance.number_of_ticket

    if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA", "VIRTUAL_SOCCER"]:
        return
    print("HERE 13..!!!")
    # ready_agent_for_local = Agent.objects.filter(agent_icash_sold__gte = 800).last() #USE THIS TO REWARD LOCAL

    """
    CHECK IF AMOUNT PAID IS 50 OR 100 NAIRA
    THEN HANDLE IT'S DRAW AND WINNING DIFFERENTLY USING THE DROUGHT CONTROL

    50 naira
        3 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning
        5 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        7 ticket play
            give out a bonus winning
        3 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning
        5 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        7 ticket play
            give out a bonus winning
        3 ticket play
            give out a bonus winning
        4 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning

        RESET

    100 naira
        2 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning
        5 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        5 ticket play
            give out a bonus winning
        2 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning
        5 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        2 ticket play
            give out a bonus winning
        2 ticket play
            give out a bonus winning
        2 ticket play
            route to main icash draw
        4 ticket play
            give out a bonus winning

        15 times, then reset

    Drought control Price combination
    200:
        bonus: 1
            100
    200:
        main: 2
            200
    300:
        bonus: 3
            200
    400:
        bonus: 4
            300
    200:
        main: 5
            100
    450:
        bonus: 6
            300
    200:
        bonus: 7
            150
    200:
        main: 8
            200
    300:
        bonus: 9
            200
    400:
        bonus: 10
            300
    200:
        main: 11
            100
    300:
        bonus: 12
            200
    200:
        bonus: 13
            150
    200:
        main: 14
            100
    300:
        bonus: 15
            200
    """

    # DROUGHT CONTROL #
    _winning_amount = 0
    actual_amount_paid = instance.amount_paid * instance.number_of_ticket
    actual_effective_rtp = instance.rtp * instance.number_of_ticket

    const = ConstantVariable.objects.first()
    # if const.drought_enabled and (instance.amount_paid == 50 or instance.amount_paid == 100) and (instance.paid is True):
    if const.drought_enabled and (instance.paid is True):

        contribute_to_pool, _winning_amount = ConstantVariable.release_draft(actual_effective_rtp)
        print(f":::::::::::: WINNING :::: {_winning_amount}")

        if _winning_amount != 0:
            InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                f"DROUGHT-GIVER-{datetime.now()}",
                (
                    "min_win",
                    200,
                    _winning_amount,
                ),
                count_before=0,
                flavour="CASHBACK",
                is_for_pos_only=False,
                is_avail_50_n_100=True,
            )
        print("CONTRIB POOL", contribute_to_pool, "---AMOUNT PAID", actual_amount_paid)
        if not contribute_to_pool and actual_amount_paid <= 100:
            return

        if actual_amount_paid > 100 and not contribute_to_pool:
            actual_effective_rtp = actual_effective_rtp - 100

            print("DEDUCTING 100BUCKS FROM RTP FOR DROUGHT.!!")

    # DROUGHT CONTROL #

    from django.db.models import F

    from main.models import ConstantVariableParam

    param = ConstantVariableParam.get_param(f"icash_{int(instance.stake_amount * instance.number_of_ticket)}_count")
    param.value_float = F("value_float") + 1
    param.save()

    print(f":::::::::::: WINNING AMOUNT :::: {_winning_amount}")
    if instance.number_of_ticket > 1 and ConstantVariable.objects.all().last().icash_to_use == "MIXED ICASH":
        print("PASSSING CANT LOG THIS")
        print("PASSSING CANT LOG THIS")
        print(f"TICKET NUBER {instance.number_of_ticket}")
        print("PASSSING CANT LOG THIS")
        return

    # automatically populate a list with all fields, except the ones you want to exclude
    fields_to_update = [f.name for f in LottoTicket._meta.get_fields() if f.name not in fields_to_exclude and not f.auto_created]

    from math import ceil

    from django.conf import settings

    if settings.DEBUG:
        DEFAULT_AGENT_FOR_WEB = 43
        # DEFAULT_AGENT_FOR_WEB = 1

    else:
        # DEFAULT_AGENT_FOR_WEB = settings.DEFAULT_AGENT_ID
        DEFAULT_AGENT_FOR_WEB = 43

    if not instance.paid:
        return

    if not instance.agent_profile:
        instance.is_agent = True
        agent_profile = Agent.objects.filter(id=DEFAULT_AGENT_FOR_WEB)
        instance.agent_profile = agent_profile.last()
        instance.save()

    print("::::::::::::::1::::::::::::::")
    # if instance.icash_2_counted or not instance.is_agent:
    #     return

    print("::::::::::::::2::::::::::::::")
    print("LOTTERY TYPE", instance.lottery_type)
    if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA", "VIRTUAL_SOCCER"]:
        return
    print("::::::::::::::3::::::::::::::")

    WIN_GIVEN_OUT = False
    LG_WIN_FACTOR = 1
    const_obj: ConstantVariable = ConstantVariable.objects.all().last()

    # if const_obj.icash_to_use != "NEW ICASH":
    #     return

    print("::::::::::::::4::::::::::::::")
    # def round_down(num):
    #     return num//100 * 100

    WHITE_WINNINGS = {
        200: {
            "least_win": 1200,
            "minor_win": 1500,
            "min_win": 1800,
            "mid_win": 2000,
            "max_win": 2400,
        },
        500: {
            "least_win": 1500,
            "minor_win": 1800,
            "min_win": 2000,
            "mid_win": 2500,
            "max_win": 4500,
        },
        400: {
            "least_win": 1500,
            "minor_win": 1800,
            "min_win": 2000,
            "mid_win": 2500,
            "max_win": 4500,
        },
        750: {
            "least_win": 1400,
            "minor_win": 1700,
            "min_win": round_down(9000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(10800 / const_obj.icash_winnings_divisor),
            "max_win": round_down(15750 / const_obj.icash_winnings_divisor),
        },
        800: {
            "least_win": 1800,
            "minor_win": 2400,
            "min_win": round_down(11400 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18000 / const_obj.icash_winnings_divisor),
        },
        1250: {
            "least_win": 1250,
            "minor_win": 1950,
            "min_win": round_down(12000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(14000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18750 / const_obj.icash_winnings_divisor),
        },
        1300: {
            "least_win": 1300,
            "minor_win": 1700,
            "min_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(25000 / const_obj.icash_winnings_divisor),
        },
        1400: {
            "least_win": 2000,
            "minor_win": 2666,
            "min_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(18000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(27000 / const_obj.icash_winnings_divisor),
        },
    }

    BLACK_WINNINGS = {
        200: {
            "least_win": 500,
            "minor_win": 600,
            "min_win": 800,
            "mid_win": 1200,
            "max_win": 1500,
        },
        500: {
            "least_win": 1200,
            "minor_win": 1400,
            "min_win": 1500,
            "mid_win": 2000,
            "max_win": 3000,
        },
        400: {
            "least_win": 1200,
            "minor_win": 1400,
            "min_win": 1500,
            "mid_win": 2000,
            "max_win": 3000,
        },
        750: {
            "least_win": 1125,
            "minor_win": 1200,
            "min_win": round_down(9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
            "mid_win": round_down(10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
            "max_win": round_down(15750 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
        },
        800: {
            "least_win": 1802,
            "minor_win": 1801,
            "min_win": 1800,
            "mid_win": 2000,
            "max_win": 3500,
        },
        1250: {
            "least_win": 2500,
            "minor_win": 2350,
            "min_win": 2200,
            "mid_win": 2500,
            "max_win": 3500,
        },
        1300: {
            "least_win": 2750,
            "minor_win": 1800,
            "min_win": 2000,
            "mid_win": 3000,
            "max_win": 4000,
        },
        1400: {
            "least_win": 2002,
            "minor_win": 2001,
            "min_win": 2000,
            "mid_win": 2000,
            "max_win": 3800,
        },
    }

    # {"200": 0, "500": 0, "750": 0, "1000": 0, "1250": 0, "1300": 0, "1500": 0}
    print("           |||              ")
    print("           |||              ")
    print(
        "Stake Amount :::",
        instance.stake_amount,
        instance.number_of_ticket,
        instance.game_play_id,
    )
    print("           |||              ")
    print("           |||              ")

    """
    THE REASON WHY I'M CHANGING THIS IS BEACUSE, 200, 500, 750, 1000, 1250, 1300, 1400
    IS THE EXPECTED STAKE AMOUNT, BUT IN CASES WHERE 200 IS RECORDED MULTIPLE TIMES FOR A GAME PLAY, WE'VE TO CHECK IT AS A SINGLE 200
    NOT THE MULTIPLICATION OF NUMBER OF NUMBER OF TICKETS

    """
    # if instance.stake_amount == 200:
    #     actual_stake_amount = str(int(instance.stake_amount))
    # else:
    actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

    agent = instance.agent_profile

    if const_obj.new_icash2_draw_mode == "LOCAL" and agent.id != 43:
        agent.agent_icash_sold = agent.agent_icash_sold + instance.rtp
        agent.save()

    agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
    global_flavour_list = const_obj.icash_flavour_dict.get(str((actual_stake_amount)), [])

    const_obj = ConstantVariable.objects.all().last()

    if not len(agent_flavour_list):
        agent.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        agent.save()

    if not len(global_flavour_list):
        const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        const_obj.save()

    agent_current_flavour = "BLACK"  # agent.icash_flavour_dict[str((actual_stake_amount))][0]
    global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]

    agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[str((actual_stake_amount))][1:]
    const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

    new_agent_dict = dict(agent.icash_flavour_dict)
    new_global_dict = dict(const_obj.icash_flavour_dict)

    GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")

    # giver_threshold = InstantCashGiverThresholds.objects.first()
    # giver_threshold.GIVER_THRESHOLD_DICT[actual_stake_amount] = F(GIVER_THRESHOLD_DICT[actual_stake_amount]) + 1
    # giver_threshold.save()

    const_obj = ConstantVariable.objects.all().last()

    try:
        global_count_to_giver = const_obj.new_quika_icash_count_to_giver[str(actual_stake_amount)]
        agent.new_quika_icash_count_to_giver[str(actual_stake_amount)]
    except Exception:
        base_global_giver_dict = const_obj.new_quika_icash_count_to_giver
        base_global_giver_dict[str(actual_stake_amount)] = 0

        base_agent_giver_dict = agent.new_quika_icash_count_to_giver
        base_agent_giver_dict[str(actual_stake_amount)] = 0

        ConstantVariable.objects.all().update(new_quika_icash_count_to_giver=base_global_giver_dict)
        agent.new_quika_icash_count_to_giver = base_agent_giver_dict
        agent.save()

        global_count_to_giver = -999

    const_obj = ConstantVariable.objects.all().last()
    GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

    # GIVER_THRESHOLD = GIVER_THRESHOLD_DICT[str(actual_stake_amount)]

    WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

    GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
    AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

    BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
    BONUS_WINNING = WINNINGS_DICT[BONUS_FLAVOUR]
    BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

    print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

    const_obj = ConstantVariable.objects.all().first()

    def get_averages(band):
        values = [band["least_win"]] + [band["minor_win"]] + [band["min_win"]] + [band["mid_win"]] + [band["max_win"]]
        return sum(values) / 5

    print("THIS IS THE ACTUAL RTP.!!!!:: ", actual_rtp)

    if instance.lottery_type == "INSTANT_CASHOUT" or instance.lottery_type == "QUIKA":
        if instance.paid and actual_rtp > 0 and instance.is_agent:
            print("HERE TOO..!!!!!!")

            agent = instance.agent_profile
            agent.icash_sold = agent.icash_sold + 1
            instance.icash_2_counted = True
            instance.icash_counted = True
            instance.save(update_fields=fields_to_update)

            tickets_per_teir = instance.number_of_ticket
            print("GLOBAL_WINNINGS", GLOBAL_WINNINGS)
            band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
            rtp = actual_rtp  # * instance.number_of_ticket

            print(
                "\n BAND Average +++++++++++++++++++++++++++++++++++++++++++++++++>",
                band_average,
                "<+++++++++++++++++++++++++++++++++++++++++++++++++\n",
            )

            num_win_tickets = const_obj.icash_rtps.get(str(int(actual_stake_amount)), actual_rtp) / band_average
            fixed_rtp = const_obj.icash_rtps.get(str(int(instance.stake_amount)), 0)

            if fixed_rtp == 0:
                fixed_rtp = 1

            scaled_rtp = actual_effective_rtp / actual_rtp

            # if fixed_rtp == 1:
            #     scaled_rtp = 1

            # if _winning_amount:  # UPDATE[SET RTP BACK 1TICKET AFTER EVERY WIN BECAUSE OF DROUGHT CONTROL]
            #     scaled_rtp = -1.40

            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(f"WINNING AMOUNT HERE:::::  :::::::::::::::::::::::: {_winning_amount}")
            print("SCALLED RTP :::::", scaled_rtp)
            print("INSTANCE RTP :::::", instance.effective_rtp)
            print("INSTANCE Effective RTP :::::", instance.effective_rtp)
            print("FIXED RTP :::::", fixed_rtp)
            print(
                "FIXED RTP :::::",
                const_obj.icash_rtps.get(str(instance.stake_amount), 0),
            )
            print("Stake amouint :::::", int(instance.stake_amount))
            print("CONST :::::", const_obj.icash_rtps)
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: :::::")

            print(
                "NUM WINNING TICKS ::::",
                num_win_tickets,
                "\n",
                "Inst-RTP::{actual_rtp}\n",
                "Num tiks{instance.number_of_ticket}\n",
                "Tot RTP{rtp}\n",
                band_average,
                "\n",
                GLOBAL_WINNINGS[int(actual_stake_amount)],
            )
            percent_win_ticket = num_win_tickets / instance.number_of_ticket

            count_before = (1 / percent_win_ticket) / instance.number_of_ticket
            # icash_excesses_from_count_before = (instance.number_of_ticket * actual_rtp) * (ceil(count_before) - count_before)
            icash_excesses_from_count_before = (actual_rtp) * (ceil(count_before) - count_before)
            print("EXCESSES FROM CB4 ::::", icash_excesses_from_count_before)
            print(":::::::::::COUNT BEFORE ::::", count_before)

            print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
            print(
                "A-FLAVOUR ::::",
                agent_current_flavour,
                "G-FLAVOUR BEF ::::",
                global_current_flavour,
            )
            print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

            if not agent.icash_sold_dict.get(str(actual_stake_amount)):
                agent.icash_sold_dict[str(actual_stake_amount)] = 0

            if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
                const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

            agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) + scaled_rtp

            const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + scaled_rtp
            const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + scaled_rtp
            )

            win_rank_ratio = ["least_win"] + ["minor_win"] + ["min_win"] + ["mid_win"] + ["max_win"]
            win_rank_choice = random.choice(win_rank_ratio)

            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JINSTANCE GAME ID", instance.game_play_id)
            print("COUNT TO GIVER", global_count_to_giver, "GTHRES", GIVER_THRESHOLD)
            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("                                      ")

            if const_obj.icash_to_use == "NEW ICASH":
                const_obj.save()
                agent.save()

                if global_count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "GLOBAL":
                    # Handle giver for GLOBAL
                    print("HERE 1..!!!")
                    print("RELEASING GIVER")
                    print(
                        "COUNT TO GIVER",
                        global_count_to_giver,
                        "GTHRES",
                        GIVER_THRESHOLD,
                    )
                    print("                                      ")
                    print("                                      ")
                    print("                                      ")
                    print("                                      ")

                    const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold - 1
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                        const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) - 1
                    )
                    const_obj.save()
                    WIN_GIVEN_OUT = True

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"GIVER-G-{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    # const_obj.count_to_giver = 0
                    # const_obj.save()
                    ConstantVariable.update_giver(actual_stake_amount, reset=True)
                    return

                if False:  # OVERIDE LOCAL WINNINGS
                    # if (
                    #         agent_count_to_giver >= GIVER_THRESHOLD
                    #         and const_obj.icash2_draw_mode == "LOCAL"
                    # ):
                    # Handle giver for LOCAL
                    print("HERE 2..!!!")

                    agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
                    agent.save()
                    WIN_GIVEN_OUT = True
                    # Offset the number of tickets sold because of the giver

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        "GIVER-L-{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        agent=agent,
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    # agent.count_to_giver = 0
                    # agent.save()
                    agent.update_giver(actual_stake_amount, reset=True)
                    return

                if (
                    (const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before
                ) and const_obj.icash2_draw_mode == "GLOBAL":
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"G-{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice] / LG_WIN_FACTOR,
                        ),
                        count_before=count_before,
                        flavour=global_current_flavour,
                    )
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
                    const_obj.icash_excesses_from_count_before = const_obj.icash_excesses_from_count_before + icash_excesses_from_count_before

                    const_obj.save()

                    # const_obj.count_to_giver = const_obj.count_to_giver + 1
                    ConstantVariable.update_giver(actual_stake_amount)
                    WIN_GIVEN_OUT = True

                if False:  # OVERIDE LOCAL WINNINGS
                    # if (
                    #         (agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir)
                    #         >= count_before
                    # ) and const_obj.icash2_draw_mode == "LOCAL":
                    print("HERE 6..!!!")
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        "LL-{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][win_rank_choice] / LG_WIN_FACTOR,
                        ),
                        agent=agent,
                        count_before=count_before,
                        flavour=agent_current_flavour,
                    )
                    agent.icash_sold_dict[str(actual_stake_amount)] = 0
                    # agent.count_to_giver = agent.count_to_giver + 1
                    # agent.save()

                    agent.icash_excesses_from_count_before = agent.icash_excesses_from_count_before + icash_excesses_from_count_before
                    agent.save()
                    agent.update_giver(actual_stake_amount)
                    WIN_GIVEN_OUT = True

                    # if (
                    #     agent.icash2_draw_mode == "LOCAL"
                    #     and const_obj.icash2_draw_mode == "GLOBAL"
                    # ):
                    """New implementation for local winning"""

                # TURN OFF LOCAL DRAW
                if False:  # OVERIDE LOCAL WINNINGS
                    # if (
                    #         agent.icash2_draw_mode == "LOCAL"
                    #         and const_obj.icash2_draw_mode == "GLOBAL"
                    # ):
                    # if False:

                    """New implementation for local winning"""

                    icash_sold_game_ids = list(agent.icash_sold_game_ids) + [instance.game_play_id]
                    Agent.objects.filter(id=instance.agent_profile.id).update(
                        icash_sold_game_ids=icash_sold_game_ids,
                        icash_sold_amount=F("icash_sold_amount") + actual_rtp,  # noqa
                    )

                    const_obj = ConstantVariable.objects.all().last()
                    win_tiers = const_obj.icash2_quika_giveout_tier

                    if win_tiers:
                        win_rank_choice = win_tiers[0]
                    else:
                        win_tiers = [
                            "least_win",
                            "minor_win",
                            "min_win",
                            "mid_win",
                            "least_win",
                            "minor_win",
                            "min_win",
                            "mid_win",
                            "max_win",
                        ]
                        win_rank_choice = win_tiers[0]

                    winning = AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][win_rank_choice]

                    agent = Agent.objects.get(id=instance.agent_profile.id)

                    tickets_sold_amount = agent.icash_sold_amount
                    agent.unique_tickets_sold

                    difference_after_payout = tickets_sold_amount - winning

                    print("**************")
                    print("**************")
                    print("**************")
                    print("              ")
                    print("              ")
                    print("AGENT ::,", instance.agent_profile)
                    print("POSSIBLE WINNING::::", winning)
                    print("WINNING RANK::::", win_rank_choice)
                    print("ACTIVE FLAVOUR::::", agent_current_flavour)
                    print("ACTUAL STAKE AMOUNT::::", actual_stake_amount)
                    print("AMOUNT SOLD::::", agent.icash_sold_amount)
                    print("I GOT HERE NOW..!!!")
                    print("              ")
                    print("              ")
                    print("**************")
                    print("**************")
                    print("**************")

                    if difference_after_payout >= 0:
                        if len(set(agent.icash_sold_game_ids)) <= 7 and actual_stake_amount == 200:
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
                            print(
                                "SALES :::",
                                len(set(agent.icash_sold_game_ids)),
                                "@@@@@@@@@@@",
                            )
                            print("@@@@@@@@@                    @@@@@@@@@@@")
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")

                        else:
                            ConstantVariable.objects.all().update(icash2_quika_giveout_tier=win_tiers[1:])

                            Agent.objects.filter(id=instance.agent_profile.id).update(
                                icash_sold_game_ids=[],
                                icash_sold_amount=difference_after_payout,
                            )
                            agent = Agent.objects.get(id=instance.agent_profile.id)

                            InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                                "LA{datetime.now()}",
                                (
                                    win_rank_choice,
                                    actual_stake_amount,
                                    winning / LG_WIN_FACTOR,
                                ),
                                agent=agent,
                                count_before=count_before,
                                flavour=agent_current_flavour,
                            )
                            agent.icash_sold_dict[str(actual_stake_amount)] = 0

                            agent.icash_excesses_from_count_before = agent.icash_excesses_from_count_before + icash_excesses_from_count_before
                            agent.save()
                            agent.update_giver(actual_stake_amount)
                            WIN_GIVEN_OUT = True

                    else:
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")

            ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
            number_of_single_tickets_in_largest_ticket = random.randint(int(ticket_min), int(ticket_max))

            if WIN_GIVEN_OUT:
                ConstantVariable.objects.all().update(icash_flavour_dict=new_global_dict)
                Agent.objects.filter().update(icash_flavour_dict=new_agent_dict)

            (
                thresholdx,
                thresholdy,
                thresholdz,
            ) = const_obj.excesses_giveout_threshold.split(",")
            # excess_giveout_amount = random.choice(
            #     list(range(int(thresholdx), int(thresholdy), int(thresholdz)))
            # )
            excess_giveout_amount = random.choice([600, 700, 800, 900, 1000])
            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            print("EXCESS AMOUNT :::", excess_giveout_amount)
            BALANCE_AFTER_EXCESS_GIVEOUT = 0

            if const_obj.icash_excesses_from_count_before >= excess_giveout_amount and const_obj.icash2_draw_mode == "GLOBAL":
                InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                    f"G-EXCESS-{datetime.now()}",
                    ("min_win", 200, excess_giveout_amount),
                    count_before=number_of_single_tickets_in_largest_ticket,
                    flavour="CASHBACK",
                )

                BALANCE_AFTER_EXCESS_GIVEOUT = const_obj.icash_excesses_from_count_before - excess_giveout_amount
                ConstantVariable.objects.all().update(icash_excesses_from_count_before=BALANCE_AFTER_EXCESS_GIVEOUT)

            if False:  # OVERIDE LOCAL WINNINGS
                # if (
                #         agent.icash_excesses_from_count_before >= excess_giveout_amount
                #         and const_obj.icash2_draw_mode == "LOCAL"
                # ):
                InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                    "L-EXCESS-{datetime.now()}",
                    ("min_win", 200, excess_giveout_amount),
                    agent,
                    count_before=number_of_single_tickets_in_largest_ticket,
                    flavour="CASHBACK",
                )

                BALANCE_AFTER_EXCESS_GIVEOUT = agent.icash_excesses_from_count_before - excess_giveout_amount
                agent.icash_excesses_from_count_before = BALANCE_AFTER_EXCESS_GIVEOUT
                agent.save()

            agent.icash_sold / number_of_single_tickets_in_largest_ticket

            if False:  # OVERIDE LOCAL WINNINGS
                # if (
                #         tickets_to_bonus >= const_obj.icash2_local_bonus_threshold
                #         and instance.lottery_type == "INSTANT_CASHOUT"
                # ):
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")

                if agent_tickets.exists():
                    [int(amount * qty) for amount, qty in list(agent_tickets)]
                    bonus_band = 200  # int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_local_bonus_available = const_obj.icash2_local_bonus_available - BLACK_WINNINGS[bonus_band][BONUS_TIER]

                    if not const_obj.icash2_local_bonus_available <= 0:
                        const_obj.save()
                        agent.icash_sold = 0
                        agent.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            "BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )

                    agent.icash2_bonus_left = agent.icash2_bonus_left - BLACK_WINNINGS[bonus_band][BONUS_TIER]

                    if not agent.icash2_bonus_left <= 0:
                        agent.icash_sold = 0
                        agent.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            "BONUS-LOC-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )

            if (
                const_obj.global_agent_icash_sold / number_of_single_tickets_in_largest_ticket
            ) >= const_obj.icash2_local_bonus_threshold and instance.lottery_type == "INSTANT_CASHOUT":
                print("HERE 9..!!!")
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")
                if agent_tickets.exists():
                    [int(amount * qty) for amount, qty in list(agent_tickets)]

                    bonus_band = 200  # int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_global_bonus_available = const_obj.icash2_global_bonus_available - BONUS_WINNING[bonus_band][BONUS_TIER]
                    const_obj.global_agent_icash_sold = 0

                    if not const_obj.icash2_global_bonus_available < 0:
                        const_obj.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            f"GLOB-BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )


@shared_task
def quika_new_icash_telco(ticket_id):
    from django.db.models import F

    from main.models import (
        Agent,
        ConstantVariable,
        InstantCashoutPendingWinning,
        LottoTicket,
    )

    icash_to_use = ConstantVariable.objects.all().last().icash_to_use

    if icash_to_use == "OLD ICASH":
        return "OLD ICASH ACTIVE"

    instance: LottoTicket = LottoTicket.objects.get(id=ticket_id)
    print(f"THIS IS TELCO MAYBE NOT PAID :: {instance.paid} :: {instance.phone}")

    if instance.number_of_ticket > 1 and ConstantVariable.objects.all().last().icash_to_use == "MIXED ICASH":
        print("PASSSING CANT LOG THIS")
        print("PASSSING CANT LOG THIS")
        print(f"TICKET NUBER {instance.number_of_ticket}")
        print("PASSSING CANT LOG THIS")
        return

    fields_to_exclude = {
        "system_generated_num",
    }
    # automatically populate a list with all fields, except the ones you want to exclude
    fields_to_update = [f.name for f in LottoTicket._meta.get_fields() if f.name not in fields_to_exclude and not f.auto_created]

    from math import ceil

    from django.conf import settings

    if settings.DEBUG:
        DEFAULT_AGENT_FOR_WEB = 43
        # DEFAULT_AGENT_FOR_WEB = 1

    else:
        # DEFAULT_AGENT_FOR_WEB = settings.DEFAULT_AGENT_ID
        DEFAULT_AGENT_FOR_WEB = 43

    if not instance.paid:
        return

    if not instance.agent_profile:
        instance.is_agent = True
        agent_profile = Agent.objects.filter(id=DEFAULT_AGENT_FOR_WEB)
        instance.agent_profile = agent_profile.last()
        instance.save()

    print("::::::::::::::1::::::::::::::")
    if instance.icash_2_counted or not instance.is_agent:
        return

    print("::::::::::::::2::::::::::::::")
    print("LOTTERY TYPE", instance.lottery_type)
    if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA", "VIRTUAL_SOCCER"]:
        return
    print("::::::::::::::3::::::::::::::")

    const_obj: ConstantVariable = ConstantVariable.objects.all().last()

    # if const_obj.icash_to_use != "NEW ICASH":
    #     return

    print("::::::::::::::4::::::::::::::")
    # def round_down(num):
    #     return num//100 * 100

    WHITE_WINNINGS = {
        200: {
            "least_win": 600,
            "minor_win": 800,
            "min_win": 1000,
            "mid_win": 1200,
            "max_win": 2500,
        },
        500: {
            "least_win": 1500,
            "minor_win": 1800,
            "min_win": 2000,
            "mid_win": 2500,
            "max_win": 4500,
        },
        750: {
            "least_win": 1400,
            "minor_win": 1700,
            "min_win": round_down(9000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(10800 / const_obj.icash_winnings_divisor),
            "max_win": round_down(15750 / const_obj.icash_winnings_divisor),
        },
        800: {
            "least_win": 1800,
            "minor_win": 2400,
            "min_win": round_down(11400 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18000 / const_obj.icash_winnings_divisor),
        },
        1250: {
            "least_win": 1250,
            "minor_win": 1950,
            "min_win": round_down(12000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(14000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18750 / const_obj.icash_winnings_divisor),
        },
        1300: {
            "least_win": 1300,
            "minor_win": 1700,
            "min_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(25000 / const_obj.icash_winnings_divisor),
        },
        1400: {
            "least_win": 2000,
            "minor_win": 2666,
            "min_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(18000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(27000 / const_obj.icash_winnings_divisor),
        },
    }

    BLACK_WINNINGS = {
        200: {
            "least_win": 600,
            "minor_win": 800,
            "min_win": 1000,
            "mid_win": 1200,
            "max_win": 1500,
        },
        500: {
            "least_win": 1200,
            "minor_win": 1400,
            "min_win": 1500,
            "mid_win": 2000,
            "max_win": 3000,
        },
        750: {
            "least_win": 1125,
            "minor_win": 1200,
            "min_win": round_down(9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
            "mid_win": round_down(10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
            "max_win": round_down(15750 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
        },
        800: {
            "least_win": 1802,
            "minor_win": 1801,
            "min_win": 1800,
            "mid_win": 2000,
            "max_win": 3500,
        },
        1250: {
            "least_win": 2500,
            "minor_win": 2350,
            "min_win": 2200,
            "mid_win": 2500,
            "max_win": 3500,
        },
        1300: {
            "least_win": 2750,
            "minor_win": 1800,
            "min_win": 2000,
            "mid_win": 3000,
            "max_win": 4000,
        },
        1400: {
            "least_win": 2002,
            "minor_win": 2001,
            "min_win": 2000,
            "mid_win": 2000,
            "max_win": 3800,
        },
    }

    # {"200": 0, "500": 0, "750": 0, "1000": 0, "1250": 0, "1300": 0, "1500": 0}
    print("           |||              ")
    print("           |||              ")
    print(
        "Stake Amount :::",
        instance.stake_amount,
        instance.number_of_ticket,
        instance.game_play_id,
    )
    print("           |||              ")
    print("           |||              ")
    actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

    agent = instance.agent_profile

    agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
    global_flavour_list = const_obj.icash_flavour_dict.get(str((actual_stake_amount)), [])

    const_obj = ConstantVariable.objects.all().last()

    if not len(agent_flavour_list):
        agent.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        agent.save()

    if not len(global_flavour_list):
        const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        const_obj.save()

    agent_current_flavour = agent.icash_flavour_dict[str((actual_stake_amount))][0]
    global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]

    agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[str((actual_stake_amount))][1:]
    const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

    dict(agent.icash_flavour_dict)
    dict(const_obj.icash_flavour_dict)

    GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")

    # giver_threshold = InstantCashGiverThresholds.objects.first()
    # giver_threshold.GIVER_THRESHOLD_DICT[actual_stake_amount] = F(GIVER_THRESHOLD_DICT[actual_stake_amount]) + 1
    # giver_threshold.save()

    const_obj = ConstantVariable.objects.all().last()

    try:
        global_count_to_giver = const_obj.new_quika_icash_count_to_giver[str(actual_stake_amount)]
        agent.new_quika_icash_count_to_giver[str(actual_stake_amount)]
    except Exception:
        base_global_giver_dict = const_obj.new_quika_icash_count_to_giver
        base_global_giver_dict[str(actual_stake_amount)] = 0

        base_agent_giver_dict = agent.new_quika_icash_count_to_giver
        base_agent_giver_dict[str(actual_stake_amount)] = 0

        ConstantVariable.objects.all().update(new_quika_icash_count_to_giver=base_global_giver_dict)
        agent.new_quika_icash_count_to_giver = base_agent_giver_dict
        agent.save()

        global_count_to_giver = -999

    const_obj = ConstantVariable.objects.all().last()
    GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

    # GIVER_THRESHOLD = GIVER_THRESHOLD_DICT[str(actual_stake_amount)]

    WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

    GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
    AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

    BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
    WINNINGS_DICT[BONUS_FLAVOUR]
    BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

    print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

    const_obj = ConstantVariable.objects.all().first()

    def get_averages(band):
        values = [band["least_win"]] + [band["minor_win"]] + [band["min_win"]] + [band["mid_win"]] + [band["max_win"]]
        return sum(values) / 5

    if instance.lottery_type == "INSTANT_CASHOUT" or instance.lottery_type == "QUIKA":
        if instance.paid and instance.rtp > 0 and instance.is_agent:
            print(f"THIS IS TELCO MAYBE NOT PAID :: {instance.paid} :: {instance.phone}")
            print("HERE TOO..!!!!!!")

            agent = instance.agent_profile
            agent.icash_sold = agent.icash_sold + 1
            instance.icash_2_counted = True
            instance.icash_counted = True
            instance.save(update_fields=fields_to_update)

            instance.number_of_ticket

            band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
            rtp = instance.rtp * instance.number_of_ticket

            print(
                "\n BAND Average +++++++++++++++++++++++++++++++++++++++++++++++++>",
                band_average,
                "<+++++++++++++++++++++++++++++++++++++++++++++++++\n",
            )

            num_win_tickets = rtp / band_average
            print(
                "NUM WINNING TICKS ::::",
                num_win_tickets,
                "\n",
                "Inst-RTP::{instance.rtp}\n",
                "Num tiks{instance.number_of_ticket}\n",
                "Tot RTP{rtp}\n",
                band_average,
                "\n",
                GLOBAL_WINNINGS[int(actual_stake_amount)],
            )
            percent_win_ticket = num_win_tickets / instance.number_of_ticket

            count_before = (1 / percent_win_ticket) / instance.number_of_ticket
            icash_excesses_from_count_before = (instance.number_of_ticket * instance.rtp) * (ceil(count_before) - count_before)
            print("EXCESSES FROM CB4 ::::", icash_excesses_from_count_before)

            print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
            print(
                "A-FLAVOUR ::::",
                agent_current_flavour,
                "G-FLAVOUR BEF ::::",
                global_current_flavour,
            )
            print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

            if not agent.icash_sold_dict.get(str(actual_stake_amount)):
                agent.icash_sold_dict[str(actual_stake_amount)] = 0

            if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
                const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

            agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) + 1

            const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + 1
            const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + 1
            )

            win_rank_ratio = ["least_win"] + ["minor_win"] + ["min_win"] + ["mid_win"] + ["max_win"]
            win_rank_choice = random.choice(win_rank_ratio)

            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JUST BEFORE THE FIRST RETURN STATEMENT")
            print("JINSTANCE GAME ID", instance.game_play_id)
            print("COUNT TO GIVER", global_count_to_giver, "GTHRES", GIVER_THRESHOLD)
            print("                                      ")
            print("                                      ")
            print("                                      ")
            print("                                      ")

            if const_obj.icash_to_use == "NEW ICASH":
                const_obj.save()
                agent.save()

                # if (
                #         global_count_to_giver >= GIVER_THRESHOLD
                #         and const_obj.icash2_draw_mode == "GLOBAL"
                # ):
                #     # Handle giver for GLOBAL
                #     print("HERE 1..!!!")
                #     print("RELEASING GIVER")
                #     print("COUNT TO GIVER", global_count_to_giver, "GTHRES", GIVER_THRESHOLD)
                #     print("                                      ")
                #     print("                                      ")
                #     print("                                      ")
                #     print("                                      ")

                #     const_obj.global_agent_icash_sold = (
                #             const_obj.global_agent_icash_sold - 1
                #     )
                #     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                #             const_obj.global_agent_icash_sold_dict.get(
                #                 str(actual_stake_amount)
                #             )
                #             - 1
                #     )
                #     const_obj.save()
                #     WIN_GIVEN_OUT = True

                #     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                #         f"GIVER-G-{datetime.now()}",
                #         (
                #             "min_win",
                #             actual_stake_amount,
                #             actual_stake_amount,
                #         ),
                #         count_before=GIVER_THRESHOLD * 10,
                #         flavour="CASHBACK",
                #     )
                #     # const_obj.count_to_giver = 0
                #     # const_obj.save()
                #     ConstantVariable.update_giver(actual_stake_amount, reset=True)
                #     return

                # if (
                #         agent_count_to_giver >= GIVER_THRESHOLD
                #         and const_obj.icash2_draw_mode == "LOCAL"
                # ):
                #     # Handle giver for LOCAL
                #     print("HERE 2..!!!")

                #     agent.icash_sold_dict[str(actual_stake_amount)] = (
                #             agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
                #     )
                #     agent.save()
                #     WIN_GIVEN_OUT = True
                #     # Offset the number of tickets sold because of the giver

                #     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                #         f"GIVER-L-{datetime.now()}",
                #         (
                #             "min_win",
                #             actual_stake_amount,
                #             actual_stake_amount,
                #         ),
                #         agent=agent,
                #         count_before=GIVER_THRESHOLD * 10,
                #         flavour="CASHBACK",
                #     )
                #     # agent.count_to_giver = 0
                #     # agent.save()
                #     agent.update_giver(actual_stake_amount, reset=True)
                #     return

                # if (
                #         (
                #                 const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)]
                #                 / tickets_per_teir
                #         )
                #         >= count_before
                # ) and const_obj.icash2_draw_mode == "GLOBAL":

                #     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                #         f"G-{datetime.now()}",
                #         (
                #             win_rank_choice,
                #             actual_stake_amount,
                #             GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice],
                #         ),
                #         count_before=count_before,
                #         flavour=global_current_flavour,
                #     )
                #     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
                #     const_obj.icash_excesses_from_count_before = const_obj.icash_excesses_from_count_before + icash_excesses_from_count_before
                #     const_obj.save()

                #     # const_obj.count_to_giver = const_obj.count_to_giver + 1
                #     ConstantVariable.update_giver(actual_stake_amount)
                #     WIN_GIVEN_OUT = True

                # if (
                #         (agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir)
                #         >= count_before
                # ) and const_obj.icash2_draw_mode == "LOCAL":
                #     print("HERE 6..!!!")
                #     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                #         f"LL-{datetime.now()}",
                #         (
                #             win_rank_choice,
                #             actual_stake_amount,
                #             AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][
                #                 win_rank_choice
                #             ],
                #         ),
                #         agent=agent,
                #         count_before=count_before,
                #         flavour=agent_current_flavour,
                #     )
                #     agent.icash_sold_dict[str(actual_stake_amount)] = 0
                #     # agent.count_to_giver = agent.count_to_giver + 1
                #     # agent.save()

                #     agent.icash_excesses_from_count_before = agent.icash_excesses_from_count_before + icash_excesses_from_count_before
                #     agent.save()
                #     agent.update_giver(actual_stake_amount)
                #     WIN_GIVEN_OUT = True

                # TURN OFF LOCAL DRAW
                if agent.icash2_draw_mode == "LOCAL" and const_obj.icash2_draw_mode == "GLOBAL":
                    """New implementation for local winning"""

                    icash_sold_game_ids = list(agent.icash_sold_game_ids) + [instance.game_play_id]
                    Agent.objects.filter(id=instance.agent_profile.id).update(
                        icash_sold_game_ids=icash_sold_game_ids,
                        icash_sold_amount=F("icash_sold_amount") + instance.rtp,
                    )

                    const_obj = ConstantVariable.objects.all().last()
                    win_tiers = const_obj.icash2_quika_giveout_tier

                    if win_tiers:
                        win_rank_choice = win_tiers[0]
                    else:
                        win_tiers = [
                            "least_win",
                            "minor_win",
                            "min_win",
                            "mid_win",
                            "least_win",
                            "minor_win",
                            "min_win",
                            "mid_win",
                            "max_win",
                        ]
                        win_rank_choice = win_tiers[0]

                    winning = AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][win_rank_choice]

                    agent = Agent.objects.get(id=instance.agent_profile.id)

                    tickets_sold_amount = agent.icash_sold_amount
                    agent.unique_tickets_sold

                    difference_after_payout = tickets_sold_amount - winning

                    print("**************")
                    print("**************")
                    print("**************")
                    print("              ")
                    print("              ")
                    print("AGENT ::,", instance.agent_profile)
                    print("POSSIBLE WINNING::::", winning)
                    print("WINNING RANK::::", win_rank_choice)
                    print("ACTIVE FLAVOUR::::", agent_current_flavour)
                    print("ACTUAL STAKE AMOUNT::::", actual_stake_amount)
                    print("AMOUNT SOLD::::", agent.icash_sold_amount)
                    print("I GOT HERE NOW..!!!")
                    print("              ")
                    print("              ")
                    print("**************")
                    print("**************")
                    print("**************")

                    if difference_after_payout >= 0:
                        if len(set(agent.icash_sold_game_ids)) <= 7 and actual_stake_amount == 200:
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
                            print(
                                "SALES :::",
                                len(set(agent.icash_sold_game_ids)),
                                "@@@@@@@@@@@",
                            )
                            print("@@@@@@@@@                    @@@@@@@@@@@")
                            print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")

                        else:
                            ConstantVariable.objects.all().update(icash2_quika_giveout_tier=win_tiers[1:])

                            Agent.objects.filter(id=instance.agent_profile.id).update(
                                icash_sold_game_ids=[],
                                icash_sold_amount=difference_after_payout,
                            )
                            agent = Agent.objects.get(id=instance.agent_profile.id)

                            InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                                "TELCO-LA{datetime.now()}",
                                (
                                    win_rank_choice,
                                    actual_stake_amount,
                                    winning,
                                ),
                                agent=agent,
                                count_before=count_before,
                                flavour=agent_current_flavour,
                            )
                            agent.icash_sold_dict[str(actual_stake_amount)] = 0

                            agent.icash_excesses_from_count_before = agent.icash_excesses_from_count_before + icash_excesses_from_count_before
                            agent.save()
                            agent.update_giver(actual_stake_amount)

                    else:
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
                        print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")

            ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
            random.randint(int(ticket_min), int(ticket_max))

            # if WIN_GIVEN_OUT:
            #     ConstantVariable.objects.all().update(icash_flavour_dict=new_agent_dict)
            #     Agent.objects.filter().update(icash_flavour_dict=new_global_dict)

            # (
            #     thresholdx,
            #     thresholdy,
            #     thresholdz,
            # ) = const_obj.excesses_giveout_threshold.split(",")
            # # excess_giveout_amount = random.choice(
            # #     list(range(int(thresholdx), int(thresholdy), int(thresholdz)))
            # # )
            # excess_giveout_amount = random.choice([600, 700, 800, 900, 1000])
            # print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            # print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            # print("EXCESS AMOUNT :::", excess_giveout_amount)
            # BALANCE_AFTER_EXCESS_GIVEOUT = 0

            # if (
            #         const_obj.icash_excesses_from_count_before >= excess_giveout_amount
            #         and const_obj.icash2_draw_mode == "GLOBAL"
            # ):
            #     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
            #         f"G-EXCESS-{datetime.now()}",
            #         ("min_win", 200, excess_giveout_amount),
            #         count_before=number_of_single_tickets_in_largest_ticket,
            #         flavour="CASHBACK",
            #     )

            #     BALANCE_AFTER_EXCESS_GIVEOUT = (
            #             const_obj.icash_excesses_from_count_before - excess_giveout_amount
            #     )
            #     ConstantVariable.objects.all().update(
            #         icash_excesses_from_count_before=BALANCE_AFTER_EXCESS_GIVEOUT
            #     )

            # if (
            #         agent.icash_excesses_from_count_before >= excess_giveout_amount
            #         and const_obj.icash2_draw_mode == "LOCAL"
            # ):
            #     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
            #         f"L-EXCESS-{datetime.now()}",
            #         ("min_win", 200, excess_giveout_amount),
            #         agent,
            #         count_before=number_of_single_tickets_in_largest_ticket,
            #         flavour="CASHBACK",
            #     )

            #     BALANCE_AFTER_EXCESS_GIVEOUT = (
            #             agent.icash_excesses_from_count_before - excess_giveout_amount
            #     )
            #     agent.icash_excesses_from_count_before = BALANCE_AFTER_EXCESS_GIVEOUT
            #     agent.save()

            # tickets_to_bonus = (
            #         agent.icash_sold / number_of_single_tickets_in_largest_ticket
            # )

            # if tickets_to_bonus >= const_obj.icash2_local_bonus_threshold and instance.lottery_type=="INSTANT_CASHOUT":
            #     agent_tickets = agent.lottoticket_set.filter(
            #         lottery_type="INSTANT_CASHOUT",
            #         paid=True,
            #         date__date=datetime.now().date(),
            #     ).values_list("stake_amount", "number_of_ticket")

            #     if agent_tickets.exists():
            #         real_play_bands = [
            #             int(amount * qty) for amount, qty in list(agent_tickets)
            #         ]
            #         bonus_band = 200 # int(random.choice(real_play_bands))

            #         const_obj = ConstantVariable.objects.all().first()
            #         const_obj.icash2_local_bonus_available = (
            #                 const_obj.icash2_local_bonus_available
            #                 - BLACK_WINNINGS[bonus_band][BONUS_TIER]
            #         )

            #         if not const_obj.icash2_local_bonus_available <= 0:
            #             const_obj.save()
            #             agent.icash_sold = 0
            #             agent.save()

            #             InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
            #                 f"BONUS-{datetime.now()}",
            #                 (
            #                     BONUS_TIER,
            #                     bonus_band,
            #                     BONUS_WINNING[bonus_band][BONUS_TIER],
            #                 ),
            #                 agent,
            #                 count_before=number_of_single_tickets_in_largest_ticket,
            #                 flavour=BONUS_FLAVOUR,
            #             )

            #         agent.icash2_bonus_left = agent.icash2_bonus_left - BLACK_WINNINGS[bonus_band][BONUS_TIER]

            #         if not agent.icash2_bonus_left <= 0:
            #             agent.icash_sold = 0
            #             agent.save()

            #             InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
            #                 f"BONUS-LOC-{datetime.now()}",
            #                 (BONUS_TIER, bonus_band, BONUS_WINNING[bonus_band][BONUS_TIER]),
            #                 agent,
            #                 count_before=number_of_single_tickets_in_largest_ticket,
            #                 flavour=BONUS_FLAVOUR
            #             )

            # if (
            #         const_obj.global_agent_icash_sold
            #         / number_of_single_tickets_in_largest_ticket
            # ) >= const_obj.icash2_local_bonus_threshold and instance.lottery_type=="INSTANT_CASHOUT":
            #     print("HERE 9..!!!")
            #     agent_tickets = agent.lottoticket_set.filter(
            #         lottery_type="INSTANT_CASHOUT",
            #         paid=True,
            #         date__date=datetime.now().date(),
            #     ).values_list("stake_amount", "number_of_ticket")
            #     if agent_tickets.exists():
            #         real_play_bands = [
            #             int(amount * qty) for amount, qty in list(agent_tickets)
            #         ]

            #         bonus_band = 200 #int(random.choice(real_play_bands))

            #         const_obj = ConstantVariable.objects.all().first()
            #         const_obj.icash2_global_bonus_available = (
            #                 const_obj.icash2_global_bonus_available
            #                 - BONUS_WINNING[bonus_band][BONUS_TIER]
            #         )
            #         const_obj.global_agent_icash_sold = 0

            #         if not const_obj.icash2_global_bonus_available < 0:
            #             const_obj.save()

            #             InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
            #                 f"GLOB-BONUS-{datetime.now()}",
            #                 (
            #                     BONUS_TIER,
            #                     bonus_band,
            #                     BONUS_WINNING[bonus_band][BONUS_TIER],
            #                 ),
            #                 agent,
            #                 count_before=number_of_single_tickets_in_largest_ticket,
            #                 flavour=BONUS_FLAVOUR,
            #             )


@shared_task
def handle_scheduled_bonuses_release():
    from main.models import BalanceBonus

    return BalanceBonus.seed_bonuses()


@shared_task
def handle_scheduled_bonuses_release_for_telco():
    from django.db.models import F

    from .models import ConstantVariable, InstantCashoutPendingWinning, LottoTicket

    last_constant = ConstantVariable.objects.last()

    if last_constant:
        last_ticket_count = last_constant.last_lotto_ticket_count
    else:
        last_ticket_count = 0

        # Get the start and end of the current day
    today_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = today_start + timezone.timedelta(days=1)

    # Filter LottoTicket objects by the specified lottery types
    relevant_tickets = LottoTicket.objects.filter(
        paid=True,
        lottery_type__in=["INSTANT_CASHOUT", "QUIKA"],
        date__range=(today_start, today_end),
    )

    current_ticket_count = relevant_tickets.count()

    ticket_difference = current_ticket_count - last_ticket_count

    giveout_amount = 600
    last_constant.refresh_from_db()

    if ticket_difference >= last_constant.telco_bonus_give_threshold and last_constant.telco_bonus_purse_value >= giveout_amount:
        InstantCashoutPendingWinning.create_pending_winning(
            "TELCO-BONUS-{datetime.now()}",
            giveout_amount,
            "min_win",
            200,
            is_avialable=True,
            is_available_for_pos=False,
            is_for_pos_only=False,
            is_for_telco_only=True,
            bonus_target_agent=None,
            count_before=ticket_difference,
            flavour="BLACK",
        )

        # Update the value of last_lotto_ticket_count
        if last_constant:
            last_constant.telco_bonus_purse_value = F("telco_bonus_purse_value") - giveout_amount
            last_constant.last_lotto_ticket_count = current_ticket_count
            last_constant.save()

    return ticket_difference


@shared_task
def create_balance_bonus(game_type, transaction_type, bonus_amount, deployed, narration):
    from main.models import BalanceBonus

    bonus_obj = BalanceBonus(
        game_type=game_type,
        transaction_type=transaction_type,
        bonus_amount=bonus_amount,
        deployed=deployed,
        narration=narration,
    )

    bonus_obj.save()

    return True


# @shared_task
def switch_rtp(log=False, amount=0, rto=0):
    """IF LOG IS TRUE, WILL JUST INCREASE THE SUMS OF RTPs ELSE ATTEMPT TO CHANGE RTP"""

    from django.db.models import F

    from main.models import ConstantVariable, LottoTicket

    const_obj: ConstantVariable
    HIGH_RTP = 90
    LOW_RTP = 90
    HIGH_SHAVE = 0.90
    LOW_SHAVE = 0.90
    new_sold = 0

    if log is True:
        ConstantVariable.objects.update(overpay_threshold=F("overpay_threshold") - rto)

        const_obj = ConstantVariable.objects.all().last()

        if const_obj.overpay_threshold < -1 and const_obj.overpay_state == "HIGH_RTP":
            ConstantVariable.objects.update(overpay_state="LOW_RTP", rtp=LOW_RTP, shave_percent=LOW_SHAVE)

        return

    const_obj: ConstantVariable = ConstantVariable.objects.all().last()

    target = LottoTicket.objects.filter(updated_at__gte=const_obj.last_overpay_switch_time).aggregate(Sum("rtp"))
    rto = LottoTicket.objects.filter(updated_at__gte=const_obj.last_overpay_switch_time).aggregate(Sum("rto"))
    alter_rto = dict(rto=str(rto))

    overpay_threshold_percent = const_obj.overpay_threshold_percent
    new_sold = target.get("rtp__sum")
    rto = rto.get("rto__sum")

    if new_sold:
        pass
    else:
        new_sold = 0

    if rto:
        pass
    else:
        rto = 0

    if const_obj.overpay_state == "LOW_RTP":
        new_sold = rto - (new_sold * overpay_threshold_percent)
        ConstantVariable.objects.update(
            overpay_threshold=new_sold,
            last_overpay_switch_time=datetime.now(),
            overpay_state="HIGH_RTP",
            rtp=HIGH_RTP,
            shave_percent=HIGH_SHAVE,
        )

    return str(target), amount, [rto, alter_rto, (new_sold, overpay_threshold_percent)]


# @shared_task
def house_play_switch_rtp():
    """PLAY GAME WITH CLIENTS AND TRY TO KEEP THE HOUSE WINNING"""

    from django.db.models import Sum
    from django.utils import timezone

    from main.models import ConstantVariable, InstantCashoutPendingWinning, LottoTicket

    icash_winnings = InstantCashoutPendingWinning.objects.filter(
        created_at__date=timezone.now().strftime("%Y-%m-%d"), suppresed_white=False
    ).aggregate(Sum("amount"))
    lottery_tickets = LottoTicket.objects.filter(
        date__date=timezone.now().strftime("%Y-%m-%d"),
        paid=True,
        lottery_type__in=["INSTANT_CASHOUT", "VIRTUAL_SOCCER", "QUIKA"],
    ).aggregate(Sum("amount_paid"))

    commissions = 0.15
    target_profit = 0.05
    max_giveout = 0.35

    winnings = icash_winnings.get("amount__sum", 2000)
    all_tickets = lottery_tickets.get("amount_paid__sum", 0)
    tickets = all_tickets

    upper_limit = 1.0 + commissions + target_profit
    lower_limit = 1.0 + commissions + target_profit - max_giveout

    profit = (tickets * (1 - commissions)) - winnings

    ratio = tickets / winnings

    if ratio > upper_limit:
        ConstantVariable.objects.all().update(rtp=190)

    if ratio < lower_limit:  # EXCCESS BY 10 %
        ConstantVariable.objects.all().update(rtp=90)

    if ratio > 2.0:
        ConstantVariable.objects.all().update(rtp=70)

    print("================================")
    print("================================")
    print("================================")
    print(icash_winnings, lottery_tickets, ratio)
    print(f"PROFIT:: {profit}")
    print(f"SALES:: {all_tickets}")
    print(f"UPPER:: {upper_limit}")
    print(f"LOWER:: {lower_limit}")

    return "DONE"


@shared_task
def handle_scheduled_pending_win_release():
    import random

    from main.models import ConstantVariable, InstantCashoutPendingWinning, LottoTicket

    constant_obj = ConstantVariable.objects.all().last()
    min_plays, max_plays = constant_obj.plays_before_icash.split(",")
    plays_before_payout = random.randint(int(min_plays), int(max_plays) + 1)

    plays = LottoTicket.objects.filter(paid=True, icash_counted=False, lottery_type="INSTANT_CASHOUT", is_agent=False)[:plays_before_payout]

    print(f"WEB factor {plays_before_payout} & count {plays}")
    if len(plays) >= plays_before_payout:
        print("ENOUGH WEB TO REACT")
        pending_winnings = InstantCashoutPendingWinning.objects.filter(pending=True, is_for_pos_only=False).order_by("-id")

        if pending_winnings.exists():
            print("#AVAILING WEB PENDING WINNING @#")
            print(pending_winnings)
            pending_winning = pending_winnings.last()
            pending_winning.released_at = int(plays_before_payout)
            pending_winning.pending = False
            pending_winning.save()
            LottoTicket.objects.filter(id__in=[play.id for play in plays]).update(icash_counted=True)

        else:
            print("#NO WEB PENDING WINNINGTO AVAIL @#")

    else:
        print("NOT WEB ENOUGH TO REACT")

    #### POS PLAYS LEG
    plays = LottoTicket.objects.filter(paid=True, icash_counted=False, lottery_type="INSTANT_CASHOUT", is_agent=True)[:plays_before_payout]

    print(f"POS factor {plays_before_payout} & count {plays}")
    if len(plays) >= plays_before_payout:
        print("ENOUGH POS TO REACT")
        pending_winnings = InstantCashoutPendingWinning.objects.filter(pending=True, is_for_pos_only=True).order_by("-id")

        if pending_winnings.exists():
            print("#AVAILING POS PENDING WINNING @#")
            print(pending_winnings)
            pending_winning = pending_winnings.last()
            pending_winning.released_at = int(plays_before_payout)
            pending_winning.pending = False
            pending_winning.save()
            LottoTicket.objects.filter(id__in=[play.id for play in plays]).update(icash_counted=True)

        else:
            print("#NO PENDING POS WINNINGTO AVAIL @#")

    else:
        print("NOT POS ENOUGH TO REACT")


@shared_task
def create_collection_details_for_lottery_player(phone, amount, bank_code, stake, lottery_type=None, game_play_id=None):
    # print("Creating collection details for player")
    from main.models import (
        ConstantVariable,
        PaymentCollectionDetail,
        UserProfile,
        WovenAccountDetail,
    )
    from wallet_app.models import UserWallet
    from wyse_ussd.models import (
        CoralpayTransactions,
        CoralpayUserCode,
        UssdLotteryPayment,
        UssdPayment,
    )

    print({"amount": amount, "stake": stake})

    if settings.DEBUG is True:
        return True

    lottery_player = UserProfile.objects.filter(phone_number=phone).last()

    # Watupay, coralpay or redbiller
    if ConstantVariable.get_constant_variable().get("ussd_bank_payment_method") == "WATU_PAY":
        try:
            ussd_payment_code = generate_ussd_collection_code(bank_code=bank_code, amount=amount)

        except Exception:
            ussd_payment_code = ""
    elif ConstantVariable.get_constant_variable().get("ussd_bank_payment_method") == "CORAL_PAY":  # coralpay
        coral_pay_user_code = CoralpayUserCode.objects.create(user=lottery_player)
        CoralpayTransactions.objects.create(
            ussd_code_extension=coral_pay_user_code,
            amount=amount,
            customer_name=lottery_player.first_name,
            customer_phone=lottery_player.phone_number,
        )

        ussd_payment_code = (
            "*966*000*{ConstantVariable.get_constant_variable().get('coralpay_biller_code')}+{coral_pay_user_code.code}+{int(amount)}#"
        )

    elif ConstantVariable.get_constant_variable().get("ussd_bank_payment_method") == "REDBILLER":
        ussd_payment_code = UssdPayment.initiate_transaction(
            user=lottery_player,
            amount=amount,
            payment_for="LOTTERY_PAYMENT",
            source="REDBILLER",
        )

    else:
        ussd_payment_code = None
    print("ussd_payment_code", ussd_payment_code)

    # Pabbly
    check_out_gateway = create_pabbly_plan(phone=phone, amount=amount)
    print(check_out_gateway)
    if check_out_gateway is not None:
        pabbly_plan_id = check_out_gateway["plan_id"]
        pabbly_link = check_out_gateway["plan_checkout_link"]
        # check_bitly_checkout_link = pabbly_link
        check_bitly_checkout_link = link_shortener(pabbly_link)
        print(check_bitly_checkout_link)

        if check_bitly_checkout_link is None:
            bitly_checkout_link = pabbly_link
        else:
            bitly_checkout_link = check_bitly_checkout_link

    else:
        pabbly_link = None
        pabbly_plan_id = None
        bitly_checkout_link = None

    collection_detail = PaymentCollectionDetail.objects.create(
        lottery_player=lottery_player,
        ussd_code=ussd_payment_code,
        pabbly_link=pabbly_link,
        pabbly_plan_id=pabbly_plan_id,
        bitly_checkout_link=bitly_checkout_link,
    )

    # sleep(5)

    # Woven
    get_old_woven = WovenAccountDetail.objects.filter(Q(phone_number=phone) & Q(is_active=True) & Q(wallet_tag="USSD")).last()
    if get_old_woven:
        # check if accout details exist
        woven_account_number = get_old_woven.vnuban
        woven_bank_name = get_old_woven.bank_name
        woven_account_name = get_old_woven.acct_name
        woven_account_reference = get_old_woven.account_ref

    else:
        check_woven_account = generate_woven_collection_account_number(phone_number=phone, func_count=3, tag="USSD")
        if check_woven_account is not None:
            woven_account_number = check_woven_account["vnuban"]
            woven_bank_name = check_woven_account["bank_name"]

            # Use this to fill model
            woven_account_name = check_woven_account["account_name"]
            woven_account_reference = check_woven_account["account_reference"]
            check_woven_account["woven_payload"]

        else:
            woven_account_number = None
            woven_bank_name = None
            woven_account_name = None
            woven_account_reference = None

    _woven = WovenAccountDetail.objects.filter(Q(phone_number=phone) & Q(is_active=True) & Q(wallet_tag="USSD")).last()

    # update or create user_wallet
    user_profile = UserProfile.objects.filter(phone_number=phone).last()
    user_wallet = UserWallet.objects.filter(user__id=user_profile.id, wallet_tag="USSD").last()

    if user_wallet:
        user_wallet.woven_account = _woven
        user_wallet.account_ref = _woven.account_ref
        user_wallet.save()

    else:
        UserWallet.objects.create(
            user=user_profile,
            woven_account=_woven,
            account_ref=_woven.account_ref,
            wallet_tag="USSD",
        )

    # Update Collection Details
    collection_detail.vnuban = woven_account_number
    collection_detail.bank_name = woven_bank_name
    collection_detail.acct_name = woven_account_name
    collection_detail.woven_account_ref = woven_account_reference
    collection_detail.save()

    # create UssdLotteryPayment record
    if game_play_id is not None:
        if lottery_type == "SOCCER_CASH":
            UssdLotteryPayment.objects.create(
                user=lottery_player,
                amount=amount,
                game_play_id=game_play_id,
                game_type="SOCCER",
                lottery_type="SOCCER_CASH",
            )
        else:
            UssdLotteryPayment.objects.create(user=lottery_player, amount=amount, game_play_id=game_play_id)

    # Send Collection Message to players
    send_sms_for_payment_collection(
        phone_number=phone,
        amount=amount,
        stake=stake,
        watupay_ussd_code=ussd_payment_code,
        account_number=woven_account_number,
        bank_name=woven_bank_name,
        checkout_link=bitly_checkout_link,
        lottery_type=lottery_type,
    )


# @shared_task
def send_task_to_lotto_decisioning(batch_instance):
    lottery_queryset = batch_instance.lottery_players.filter(paid=True)
    game = Game()
    result = game.run(lottery_queryset)
    pprint.pprint(result)
    send_out_winners_result_data(
        received_winners_data=result,
        batch=batch_instance,
        run_batch_id=f"RUN-{str(uuid.uuid4())[0:5]}",
    )

    print(batch_instance)

    # Initiate Disbursement

    # get_winners = batch_instance.batch_lottery_winners.all()

    # print(len(get_winners))

    # for winner in get_winners:
    # phone_number = winner.phone_number
    # print(phone_number)

    # check_account = LotteryWinnersTable.get_user_account_details_if_any(phone_number)
    # if check_account is not None:
    #     send_congrats_sms_to_winners_with_account_num(
    #         phone_number=phone_number,
    #         share=winner.share,
    #         # account_num=check_account["account_num"],
    #         # bank_name=check_account["bank_name"]
    #     )
    # else:
    #     send_congrats_sms_to_winners_without_account_num(
    #         phone_number=phone_number,
    #         share=winner.share,
    #     )


@shared_task
def handle_non_icash_bonus():
    import datetime
    import pprint
    import random

    from django.db.models import Sum

    from main.models import (
        ConstantVariable,
        LotteryModel,
        LotteryWinnersTable,
        LottoTicket,
        LottoWinners,
    )
    from pos_app.models import Agent, AgentBonus

    const_obj: ConstantVariable = ConstantVariable.objects.all().first()
    max_bonus_amount = const_obj.pos_bonus_wysecash_max_amount
    max_bonus_qty = const_obj.pos_bonus_wysecash_max_qty

    target_agents = Agent.objects.filter(wyse_bonuses_received__lt=max_bonus_qty, agent_type="LOTTO_AGENT")
    now = datetime.date.today()

    all_s4l_tickets = []
    all_wyse_tickets = []

    print(f"TARGET AGENTS : {target_agents}")

    for agent in target_agents:
        print(f"FOR AGENT WITH ID {agent.id}")

        s4l_bonuses_today = AgentBonus.objects.filter(
            agent=agent,
            lottery_type="SALARY_FOR_LIFE",
            created_at__month=now.month,
            created_at__day=now.day,
        ).count()
        wysecash_bonuses_today = AgentBonus.objects.filter(
            agent=agent,
            lottery_type="WYSECASH",
            created_at__month=now.month,
            created_at__day=now.day,
        ).count()
        print(f"WYSE CASH BONUSES {wysecash_bonuses_today}")

        total_s4l = LottoTicket.objects.filter(
            paid=True,
            lottery_type="SALARY_FOR_LIFE",
            agent_profile=agent,
            date__month=now.month,
            date__day=now.day,
        ).aggregate(Sum("expected_amount"))
        total_wyse = LotteryModel.objects.filter(paid=True, agent_profile=agent, date__month=now.month, date__day=now.day).aggregate(
            Sum("expected_amount")
        )

        total_sales = (total_s4l.get("expected_amount__sum", 0) or 0) + (total_wyse.get("expected_amount__sum", 0) or 0)

        if total_sales > const_obj.pos_bonus_cutoff_amount:
            print("PASSING")
            continue

        else:
            s4l = LottoTicket.objects.filter(
                paid=True,
                lottery_type="SALARY_FOR_LIFE",
                agent_profile=agent,
                date__month=now.month,
                date__day=now.day,
            )
            wyse = LotteryModel.objects.filter(paid=True, agent_profile=agent, date__month=now.month, date__day=now.day)

            print(f"S4L :: {s4l}")
            print(f"WYSE :: {wyse}")

            try:
                random_s4l = random.choice(list(s4l))

                all_s4l_tickets.append(random_s4l)
            except IndexError:
                random_s4l = []

            try:
                random_wyse = random.choice(list(wyse))

                all_wyse_tickets.append(random_wyse)
            except IndexError:
                random_wyse = []

            print(f"RANDOM S4L :: {random_s4l}")
            print(f"RANDOM WYSE :: {random_wyse}")
            run_batch_id = str(datetime.datetime.now())

            if s4l_bonuses_today < 1:
                import random

                winning_number = random.sample(list(range(1, 51)), 5)
                x, y = random.sample(list(range(1, 5)), 2)

                player_number = winning_number.copy()

                less_pop_nums = list(range(1, 51))

                less_pop_nums.pop(less_pop_nums.index(player_number[x]))
                less_pop_nums.pop(less_pop_nums.index(player_number[y]))

                player_number[x] = random.choice(less_pop_nums)
                player_number[y] = random.choice(less_pop_nums)

                player_number_str = [str(num) for num in player_number]
                winning_number_str = [str(num) for num in winning_number]

                identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                for s4lticket in all_s4l_tickets:
                    AgentBonus.create_bonus(
                        agent=agent,
                        lotto_type="SALARY_FOR_LIFE",
                        icash_bonus_left=0,
                        amount=max_bonus_amount,
                        tickets_at_time=s4l.count(),
                        sales_at_bonus=total_s4l.get("expected_amount__sum", 0) or 0,
                    )

                    ticket = LottoTicket.objects.create(
                        user_profile=s4lticket.user_profile,
                        agent_profile=s4lticket.agent_profile,
                        phone=s4lticket.phone,
                        stake_amount=150,
                        expected_amount=150,
                        potential_winning=max_bonus_amount,
                        paid=True,
                        number_of_ticket=1,
                        channel="SYSTEM_BONUS",
                        game_play_id=s4lticket.game_play_id,
                        lottery_type="SALARY_FOR_LIFE",
                        ticket=",".join(player_number_str),
                        batch=s4lticket.batch,
                        win_combo=",".join(winning_number_str),
                        lottery_source="BONUS",
                        identity_id=identity_id,
                    )

                    if LottoWinners.objects.filter(game_play_id__iexact=s4lticket.game_play_id).exists():
                        pass
                    else:
                        LottoWinners.create_lotto_winner_obj(
                            lottery=ticket,
                            batch=s4lticket.batch,
                            phone_number=ticket.user_profile.phone_number,
                            ticket=player_number_str,
                            win_type="ORDINARY_WINNER",
                            lotto_type="SALARY_FOR_LIFE",
                            game_play_id=s4lticket.game_play_id,
                            stake_amount=s4lticket.stake_amount,
                            earning=1500,
                            channel_played_from="SYSTEM_BONUS",
                            run_batch_id=run_batch_id,
                            played_via_telco_channel=ticket.played_via_telco_channel,
                        )

            identity_id = f"{uuid.uuid4()}{int(time.time())}"

            if wysecash_bonuses_today < 1:
                print(f"I GOT IN HERE {all_wyse_tickets}")
                for wyseticket in all_wyse_tickets:
                    AgentBonus.create_bonus(
                        agent=agent,
                        lotto_type="WYSECASH",
                        icash_bonus_left=0,
                        amount=max_bonus_amount,
                        tickets_at_time=wyse.count(),
                        sales_at_bonus=total_s4l.get("expected_amount__sum", 0) or 0,
                    )

                    lucky_number = Utility.generate_lucky_number()

                    ticket = LotteryModel.objects.create(
                        phone=wyseticket.phone,
                        user_profile=wyseticket.user_profile,
                        unique_id=wyseticket.unique_id,
                        band=wyseticket.band,
                        stake_amount=100,
                        lucky_number=lucky_number,
                        batch=wyseticket.batch,
                        pool=wyseticket.pool,
                        instance_number=1,
                        channel="SYSTEM_BONUS",
                        agent_profile=agent,
                        is_agent=True,
                        identity_id=identity_id,
                    )

                    LotteryWinnersTable.objects.create(
                        lottery=ticket,
                        batch=ticket.batch,
                        run_batch_id=run_batch_id,
                        phone_number=ticket.user_profile.phone_number,
                        playyer_id=ticket.user_profile.id,
                        unnique_id=ticket.unique_id,
                        game_play_id=ticket.game_play_id,
                        ticket=ticket.lucky_number,
                        win_type="ORDINARY_WINNER",
                        pool=ticket.pool,
                        stake_amount=ticket.stake_amount,
                        earning=1500,
                        lottery_source_tag=ticket.channel,
                        identity_id=identity_id,
                    )

        all_s4l_tickets = []
        all_wyse_tickets = []

    pprint.pprint(all_s4l_tickets)
    pprint.pprint(all_wyse_tickets)
    return ("total_s4l: ", total_s4l, "total_wyse: ", total_wyse)


@shared_task
def handle_disbursement_engine(phone_num_with_accounts_list, phone_num_without_accounts_list):
    from main.models import ConstantVariable

    ConstantVariable.get_constant_variable()


@shared_task
def break_large_icash_tiers():
    from main.models import ConstantVariable

    ConstantVariable.get_constant_variable()


@shared_task
def celery_send_whatsapp_payment_notification_admin(phone_number, batch_id, amount, paid_via, agent_played_for_self=False):
    from main.models import LotteryBatch

    sleep(5)

    if settings.DEBUG:
        print("DEBUG MODE")

        try:
            payment_receipt_sms(phone_number=phone_number, amount=amount)
        except Exception:
            pass
        return {"message": "DEBUG MODE"}

    if batch_id is None or batch_id == "":
        try:
            payment_receipt_sms(phone_number=phone_number, amount=amount)
        except Exception:
            pass

        return {"message": "celery_send_whatsapp_payment_notification_admin batch is None"}

    # print("batch_id", batch_id)
    # print("type of batch_id", type(batch_id))

    batch = LotteryBatch.objects.filter(batch_uuid=batch_id).last()
    total_amount_paid_for_batch = LotteryBatch.batch_paid_amount(batch=batch)

    # print(batch)
    # print("Printed Batch")
    total_amount_paid_for_batch_today = LotteryBatch.batch_paid_amount_today(batch=batch)

    trigger_whatsapp_admin_on_player_payment(
        phone_number=phone_number,
        batch_id=batch_id,
        amount=amount,
        paid_via=paid_via,
        total_amount_paid_for_batch=total_amount_paid_for_batch,
        total_amount_paid_for_batch_today=total_amount_paid_for_batch_today,
    )

    try:
        if agent_played_for_self == False:
            trigger_payment_receipt_to_player = payment_receipt_sms(phone_number=phone_number, amount=amount)

    except Exception:
        pass


@shared_task
def celery_send_whatsapp_payment_notification_admin_for_soccer_payment(phone_number, amount, paid_via):
    if settings.DEBUG:
        print("DEBUG MODE")

        payment_receipt_sms(phone_number=phone_number, amount=amount)
        return {"message": "DEBUG MODE"}

    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    whatsapp_payload = {
        "token": f"{settings.PICKY_ASSIST_TOKEN}",
        "group_id": "120363043759006323",
        "application": "10",
        "mention_numbers": [
            "2348077469471",
            "2348031346306",
            "2347039115243",
            "2347039516293",
        ],
        "globalmessage": f"@2348077469471, @2348031346306, @2347039115243, @2347039516293, Dear Admins, \n\nA player with phone_number {phone_number} has made payment of *{amount}* via {paid_via} for soccer prediction \n\nThanks.",
    }

    whatsapp_Headers = {"Content-type": "application/json"}

    whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

    payment_receipt_sms(phone_number=phone_number, amount=amount)

    return whatsapp_response.text


@shared_task
def celery_send_whatsapp_retail_sells_notification(phone_number, amount, agent_name, agent_phone, game_type):
    if settings.DEBUG:
        print("DEBUG MODE")

        payment_receipt_sms(phone_number=phone_number, amount=amount)
        return {"message": "DEBUG MODE"}

    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    whatsapp_payload = {
        "token": f"{settings.PICKY_ASSIST_TOKEN}",
        "group_id": "120363043759006323",
        "application": "10",
        "mention_numbers": [
            "2348077469471",
            "2348031346306",
            "2347039115243",
            "2347039516293",
        ],
        "globalmessage": f"@2348077469471, @2348031346306, @2347039115243, @2347039516293, Dear Admins, \n\nA player with phone_number {phone_number} bought retail ticket of *{amount}* via POS AGENT ({agent_name}, {agent_phone}) for {game_type} games \n\nThanks.",
    }

    whatsapp_Headers = {"Content-type": "application/json"}

    whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

    payment_receipt_sms(phone_number=phone_number, amount=amount)

    return whatsapp_response.text


@shared_task
def celery_update_winners_pool_on_loandisk(**kwargs):
    update_pool_on_loandisk(**kwargs)
    return kwargs


@shared_task(bind=True)
def celery_tokenize_paystack_card(self, user, data):
    from main.models import CreditCard

    CreditCard.card_tokenization(user, data)

    return {"message": "tokenization successful"}


@shared_task
def celery_reward_referral(user_id):
    pass


@shared_task
def engage_user(user_id):
    User = get_user_model()
    user_ins = User.objects.filter(id=user_id).last()
    # print(user_ins)

    STRING_VALUE = f"{settings.ENGAGE_API_KEY}:{settings.ENGAGE_SECRET_KEY}"
    AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

    url = "https://api.engage.so/v1/users"

    mobile_num = user_ins.phone
    phone_no = mobile_num if "+" not in mobile_num else mobile_num.replace("+", "")

    first_name = "Champion" if user_ins.first_name == "" else f"{user_ins.first_name}"
    last_name = f"{phone_no}" if user_ins.last_name == "" else f"{user_ins.last_name}"

    uuid = f"LIBPAY-{user_id}"

    user_ins.engage_uuid = uuid
    user_ins.save()

    payload = json.dumps(
        {
            "id": uuid,
            "first_name": first_name,
            "last_name": last_name,
            "email": f"{user_ins.email}",
            "number": f"{phone_no}",
            # "meta": {
            #     "plan": "pro",
            #     "age": 34
            # },
            "created_at": f"{user_ins.date_joined}",
        }
    )

    # print(payload)

    headers = {
        "Authorization": f"Basic {AUTH_TOKEN}",
        "Content-Type": "application/json",
    }

    # print(headers)

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)

    return "User Created on Engage"


@shared_task
def celery_instant_Cashout_draw():
    # instant_cashout_draw()
    return "Instant Cashout Draw Completed"


@shared_task
def pos_celery_instant_Cashout_draw():
    # instant_cashout_draw_pos()
    return "Instant POS Cashout Draw Completed"


@shared_task
def celery_sms_for_wyse_lost(batch_id):
    from main.models import LotteryBatch, LotteryModel, LotteryWinnersTable
    from pos_app.pos_helpers import machine_number_serializer
    from wyse_ussd.tasks import (
        salary_for_life_lost_sms_on_telco,
        wyse_cash_lost_sms_on_telco,
    )

    lottery_batch = LotteryBatch.objects.filter(id=batch_id).last()

    lottery_winners_qs = LotteryWinnersTable.objects.filter(
        batch=lottery_batch,
    )

    system_pick_num = machine_number_serializer(lottery_batch.lottery_winner_ticket_number)
    machine_number = None

    if len(system_pick_num) > 1:
        for index, num in enumerate(system_pick_num):
            if index == 0:
                machine_number = ""
            else:
                machine_number += f"{num},\n"

    winning_numbers = system_pick_num[0] if len(system_pick_num) > 0 else None

    if lottery_winners_qs.exists():
        print("lottery_winners_qs.exists")
        # total amount
        total_amount = lottery_winners_qs.aggregate(Sum("earning"))["earning__sum"]

        # none winners
        non_winners = LotteryModel.objects.filter(
            paid=True,
            batch__is_active=False,
            batch__id=batch_id,
            channel="USSD",
        ).distinct("game_play_id")
        print(lottery_winners_qs.values_list("phone_number", flat=True))

        print("non_winners", non_winners)

        for player in non_winners:
            if LotteryWinnersTable.objects.filter(game_play_id=player.game_play_id).exists():
                continue
            else:
                if player.played_via_telco_channel is False:
                    send_sms_for_wyse_lost(
                        phone_number=player.user_profile.phone_number,
                        # phone_number="2349076262454",
                        total_amount_won=total_amount,
                        num_of_winners=lottery_winners_qs.count(),
                        lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                        draw_date=f"{player.batch.last_updated.date()}",
                    )
                else:
                    wyse_cash_lost_sms_on_telco(
                        phone_number=player.user_profile.phone_number,
                        total_amount_won=total_amount,
                        num_of_winners=lottery_winners_qs.count(),
                        lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                        draw_date=f"{player.batch.last_updated.date()}",
                    )

    else:
        pontentail_wiinings = [10000, 20000, 30000, 40000, 50000, 50000, 100000, 200000]
        winners_count = random.randint(1, 20)

        total_amount = random.choice(pontentail_wiinings)

        non_winners = LotteryModel.objects.filter(paid=True, batch__is_active=False, batch__id=batch_id, channel="USSD").distinct("game_play_id")

        if non_winners.exists():
            for player in non_winners:
                if LotteryWinnersTable.objects.filter(game_play_id=player.game_play_id).exists():
                    continue
                else:
                    if player.played_via_telco_channel is False:
                        send_sms_for_wyse_lost(
                            phone_number=player.user_profile.phone_number,
                            total_amount_won=total_amount,
                            num_of_winners=lottery_winners_qs.count(),
                            lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                            draw_date=f"{player.batch.last_updated.date()}",
                        )
                    else:
                        salary_for_life_lost_sms_on_telco(
                            phone_number=player.user_profile.phone_number,
                            total_amount_won=total_amount,
                            num_of_winners=winners_count,
                            lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                            draw_date=f"{player.batch.draw_date.date() if player.batch.draw_date else player.batch.last_updated.date() if player.batch.last_updated else datetime.now().date()}",
                            winning_no=winning_numbers,
                        )


@shared_task
def celery_sms_for_wyse_winners(batch_id):
    from main.models import LotteryBatch, LotteryWinnersTable
    from wyse_ussd.tasks import salary_for_life_and_instant_cashout_won_sms_on_telco

    lottery_batch = LotteryBatch.objects.filter(id=batch_id).last()

    lottery_winners_qs = LotteryWinnersTable.objects.filter(
        batch=lottery_batch,
    )

    if lottery_winners_qs.exists():
        for winner in lottery_winners_qs:
            if winner.lottery.played_via_telco_channel is False:
                send_sms_for_wyse_winners(
                    phone_number=winner.phone_number,
                    amount_won=winner.earning,
                    ticket=(winner.ticket),
                    lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                    draw_date=f"{winner.date_won.date()}",
                )
            else:
                salary_for_life_and_instant_cashout_won_sms_on_telco(
                    phone=winner.lottery.user_profile.phone_number,
                    ticket_num=(winner.ticket),
                    amount=winner.earning,
                    game_play_id=winner.lottery.game_play_id,
                    lottery_type="WYSE CASH",
                )

        return "SMS for Wyse Winners sent successfully"
    else:
        return "No Winners"


@shared_task
def celery_sms_for_s4lgame_lost(batch_id):
    from main.models import LotteryBatch, LottoTicket, LottoWinners
    from wyse_ussd.tasks import salary_for_life_lost_sms_on_telco

    lottery_batch = LotteryBatch.objects.filter(id=batch_id).last()

    lottery_winners_qs = LottoWinners.objects.filter(
        batch=lottery_batch,
    )

    non_winners = LottoTicket.objects.filter(
        paid=True,
        lottery_type="SALARY_FOR_LIFE",
        batch__id=batch_id,
        batch__is_active=False,
    ).distinct("game_play_id")

    if lottery_winners_qs.exists():
        for player in non_winners:
            if LottoWinners.objects.filter(game_play_id=player.game_play_id).exists():
                pass
            else:
                # total amount
                total_amount = lottery_winners_qs.aggregate(Sum("earning"))["earning__sum"]

                system_number_picks = lottery_batch.lottery_winner_ticket_number.split(",")[1:]

                system_number_picks = ",".join(system_number_picks)

                # system_number_picks = [
                #     x.replace(" (", "").replace(")", "").strip()
                #     for x in system_number_picks.split("),")
                # ]

                # for x in range(len(system_number_picks)):

                #     system_number_picks[x] = tuple(
                #         [y for y in system_number_picks[x].split(",")]
                #     )

                # for x in system_number_picks:
                #     system_generated_s4lgame_ticket.append(x)

                print(system_number_picks)

                if player.played_via_telco_channel is False:
                    send_sms_for_s4fgame_lost(
                        phone_number=player.user_profile.phone_number,
                        total_amount_won=total_amount,
                        num_of_winners=lottery_winners_qs.count(),
                        lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                        draw_date=f"{player.batch.last_updated.date()}",
                        winning_no=system_number_picks,
                    )
                else:
                    salary_for_life_lost_sms_on_telco(
                        phone_number=player.user_profile.phone_number,
                        total_amount_won=total_amount,
                        num_of_winners=lottery_winners_qs.count(),
                        lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                        draw_date=f"{player.batch.draw_date.date() if player.batch.draw_date else player.batch.last_updated.date() if player.batch.last_updated else datetime.now().date()}",
                        winning_no=system_number_picks,
                    )

    else:
        pontentail_wiinings = [10000, 20000, 30000, 40000, 50000, 50000, 100000, 200000]
        winners_count = random.randint(1, 20)

        total_amount = random.choice(pontentail_wiinings)

        non_winners = LottoTicket.objects.filter(paid=True, batch__is_active=False, batch__id=batch_id, channel="USSD").distinct("game_play_id")

        if non_winners.exists():
            for player in non_winners:
                if LottoWinners.objects.filter(game_play_id=player.game_play_id).exists():
                    continue
                else:
                    if player.played_via_telco_channel is False:
                        send_sms_for_wyse_lost(
                            phone_number=player.user_profile.phone_number,
                            total_amount_won=total_amount,
                            num_of_winners=lottery_winners_qs.count(),
                            lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                            draw_date=f"{player.batch.last_updated.date()}",
                        )
                    else:
                        salary_for_life_lost_sms_on_telco(
                            phone_number=player.user_profile.phone_number,
                            total_amount_won=total_amount,
                            num_of_winners=winners_count,
                            lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                            draw_date=f"{player.batch.draw_date.date() if player.batch.draw_date else player.batch.last_updated.date() if player.batch.last_updated else datetime.now().date()}",  # noqa
                            winning_no=player.batch.lottery_winner_ticket_number,
                        )
    return "SMS for Wyse Lost sent successfully"


@shared_task
def celery_sms_for_instant_cash_winners(
    phone_number,
    amount_Won,
    game_play_id,
    lottery_type="INSTANT_CASHOUT",
    winning_no=None,
):
    from main.models import LotteryBatch

    # from main.models import LotteryBatch, LottoTicket, LottoWinners
    # lottery_instance = LottoTicket.objects.filter(gameplay_id=gameplay_id).last()
    # sleep for 5 mins celery_sms_for_instant_cash_winners(phone_number = "2349076262454", amount_Won = 90, winning_no = "21,36,6,25")
    # sleep(300)

    redis_db = RedisStorage(redis_key="_celery_sms_for_instant_cash_winners")

    redis_db_data = redis_db.get_data()

    if redis_db_data is None or redis_db_data == "":
        redis_db_data = ""
    else:
        redis_db_data = redis_db_data.decode("utf-8")

    redis_game_ids = str(redis_db_data).split(",")

    if game_play_id in redis_game_ids:
        return "SMS ALREADY SENT"

    redis_game_ids.append(game_play_id)
    redis_db.set_data(",".join(redis_game_ids))

    if lottery_type == "INSTANT_CASHOUT":
        whisper_url = "https://whispersms.xyz/transactional/send"
        whisper_payload = {
            "receiver": f"{phone_number}",
            "template": "dcc51c3a-9f74-4699-9ed5-74348cdba329",
            "place_holders": {
                "Won Amount": f"{Utility.currency_formatter(amount_Won)}",
                "ticket": winning_no,
                "Draw_date": f"{datetime.now().date()}",
            },
        }

    elif lottery_type == "SALARY_FOR_LIFE":
        print("salary for life game")

        lottery_batch = LotteryBatch.objects.filter(lottery_type=lottery_type, is_active=False).last()

        print(lottery_batch.lottery_winner_ticket_number)

        system_number_picks = lottery_batch.lottery_winner_ticket_number.split(",")[1:]

        # system_number_picks = ",".join(system_number_picks)

        # system_number_picks = [
        #     x.replace(" (", "").replace(")", "").strip()
        #     for x in system_number_picks.split("),")
        # ]

        # for x in range(len(system_number_picks)):

        #     system_number_picks[x] = tuple(
        #         [y for y in system_number_picks[x].split(",")]
        #     )

        # for x in system_number_picks:
        #     system_generated_s4lgame_ticket.append(x)

        game = "salary for life game"

        whisper_url = "https://whispersms.xyz/transactional/send"
        whisper_payload = {
            "receiver": f"{phone_number}",
            "template": "141b4eb2-7bea-4ef7-a983-472ac7ed6661",
            "place_holders": {
                "Won Amount": f"{Utility.currency_formatter(amount_Won)}",
                # "no_of_Winners": f"{num_of_winners}",
                "Game": f"{game}",
                "Draw_date": f"{datetime.now().date()}",
                "winning_no": system_number_picks,
            },
        }

    else:
        game = str(lottery_type).replace("_", " ").title()
        whisper_url = "https://whispersms.xyz/transactional/send"

        whisper_payload = {
            "receiver": f"{phone_number}",
            "template": "d128c5cc-9eb9-4880-be1c-038afe7843c8",
            "place_holders": {
                "messages": f"Hello. your {game} ticket with this game id {game_play_id} just won. {Utility.currency_formatter(amount_Won)}",
            },
        }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text

    print(response)
    return response


@shared_task
def celery_sms_for_instant_cash_lost_ticket(phone_number, ticket, game_play_id):
    # sleep for 5 mins
    # sleep(300)

    redis_db = RedisStorage(redis_key="_celery_sms_for_instant_cash_lost_ticket")
    if redis_db.get_data() is None:
        redis_db_data = ""
    else:
        redis_db_data = redis_db.get_data().decode("utf-8")

    redis_game_ids = str(redis_db_data).split(",")

    if game_play_id in redis_game_ids:
        return "SMS ALREADY SENT"

    redis_game_ids.append(game_play_id)
    redis_db.set_data(",".join(redis_game_ids))

    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": "71b6c6be-dd47-42c3-8ecd-bf2011875599",
        "place_holders": {"ticket": ticket},
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


@shared_task
def celery_salary_for_rtp_check():
    from account.models import EmailHandler
    from main.models import ConstantVariable, DrawData

    const_obj = ConstantVariable.objects.all().last()
    data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE").last()

    last_rtp = ""

    try:
        print(data.factor4)
        print(data.factor2)
        winner_emerged = data.factor4.get("filterd_winners", {})

        if winner_emerged:
            last_rtp = data.factor2["running_balance"]
            print(f"LAST RTP::: {last_rtp}")

            if const_obj.salary_4_life_running_balance == last_rtp.running_balance:
                EmailHandler("<EMAIL>").salary4life_rtp_check_email("BAD SALARY FOR LIFE RTP")
                ConstantVariable.objects.all().update(salary_4_life_running_balance=0)
    except Exception:
        pass

    EmailHandler("<EMAIL>").salary4life_rtp_check_email("NOTHING TO SEE HERE")
    return "Salary for Life Draw Completed"


@shared_task
def celery_salary_for_life_draw_original():
    from account.models import EmailHandler
    from main.models import ConstantVariable, DrawData, LottoTicket

    const_obj = ConstantVariable.objects.all().last()
    data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE").last()

    last_rtp = ""

    try:
        print(data.factor4)
        print(data.factor2)
        winner_emerged = data.factor4.get("filterd_winners", {})

        if winner_emerged:
            last_rtp = data.factor2["running_balance"]
            print(f"LAST RTP::: {last_rtp}")

            if const_obj.salary_4_life_running_balance == last_rtp:
                EmailHandler("<EMAIL>").salary4life_rtp_check_email("BAD SALARY FOR LIFE RTP")
                ConstantVariable.objects.all().update(salary_4_life_running_balance=0)
                return
    except Exception:
        pass

    EmailHandler("<EMAIL>").salary4life_rtp_check_email("NOTHING TO SEE HERE")

    LottoTicket.salary_for_life_draw_original()
    return "Salary for Life Draw Completed"


@shared_task
def celery_salary_for_life_draw_original_multipro():
    from main.models import LottoTicket, ConstantVariable
    import random

    # Get the telco tickets inclusion ratio from constants
    const_obj = ConstantVariable.objects.all().last()
    telco_ratio = const_obj.s4l_telco_tickets_inclusion_ratio

    # Parse the ratio (e.g., "3:4" means include telco tickets in 3 out of 7 draws)
    try:
        include_count, total_count = map(int, telco_ratio.split(':'))

        # Create a list with the appropriate distribution of True/False values
        choices = [True] * include_count + [False] * total_count

        # Randomly select whether to include telco tickets based on the ratio
        include_telco_tickets = random.choice(choices)

        print(f"Ratio {telco_ratio} - Include telco tickets: {include_telco_tickets}")

    except (ValueError, AttributeError):
        # Default to including telco tickets if there's an error parsing the ratio
        include_telco_tickets = True
        print("Error parsing telco inclusion ratio. Defaulting to include telco tickets.")

    # Call the draw function with the include_telco_tickets parameter
    LottoTicket.salary_for_life_draw_original_multiprocessing(include_telco_tickets=include_telco_tickets)
    return "Salary for Life Draw Completed"


# @shared_task
# def celery_salary_for_life_draw_original2():
#     from account.models import EmailHandler
#     from main.models import ConstantVariable, DrawData, LottoTicket

#     const_obj = ConstantVariable.objects.all().last()
#     data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE").last()

#     last_rtp = ""

#     try:
#         print(data.factor4)
#         print(data.factor2)
#         winner_emerged = data.factor4.get("filterd_winners", {})

#         if winner_emerged:
#             last_rtp = data.factor2["running_balance"]
#             print(f"LAST RTP::: {last_rtp}")
#             print(f"RUNINING BAL ::: {const_obj.salary_4_life_running_balance}")

#             if const_obj.salary_4_life_running_balance == last_rtp:
#                 print("ENTERED HERE")
#                 EmailHandler("<EMAIL>").salary4life_rtp_check_email(
#                     "BAD SALARY FOR LIFE RTP"
#                 )
#                 EmailHandler("<EMAIL>").salary4life_rtp_check_email(
#                     "BAD SALARY FOR LIFE RTP. VALUE RESET"
#                 )
#                 ConstantVariable.objects.all().update(salary_4_life_running_balance=0)
#                 return
#     except SyntaxError:
#         pass

#     EmailHandler("<EMAIL>").salary4life_rtp_check_email(
#         "NOTHING TO SEE HERE"
#     )


# @shared_task
# def celery_salary_for_life_draw():
#     from django.db.models import F

#     from account.models import EmailHandler
#     from main.models import ConstantVariable, DrawData, LottoTicket

#     const_obj = ConstantVariable.objects.all().last()
#     data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE").last()

#     last_rtp = ""

#     try:
#         print(data.factor4)
#         print(data.factor2)
#         winner_emerged = data.factor4.get("filterd_winners", {})

#         if winner_emerged:
#             last_rtp = data.factor2["running_balance"]
#             print(f"LAST RTP::: {last_rtp}")

#             if const_obj.salary_4_life_running_balance == last_rtp:
#                 EmailHandler("<EMAIL>").salary4life_rtp_check_email(
#                     "BAD SALARY FOR LIFE RTP. VALUE RESET"
#                 )
#                 EmailHandler("<EMAIL>").salary4life_rtp_check_email(
#                     "BAD SALARY FOR LIFE RTP. VALUE RESET"
#                 )
#                 ConstantVariable.objects.all().update(salary_4_life_running_balance=0)
#                 return
#     except Exception:
#         pass

#     LottoTicket.salary_for_life_draw()

#     return "Salary for Life Draw Completed"


# @shared_task
# def celery_salary_for_life_draw_pos():
#     from account.models import EmailHandler
#     from main.models import ConstantVariable, DrawData, LottoTicket

#     const_obj = ConstantVariable.objects.all().last()
#     data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE").last()

#     last_rtp = ""

#     try:
#         print(data.factor4)
#         print(data.factor2)
#         winner_emerged = data.factor4.get("filterd_winners", {})

#         if winner_emerged:
#             last_rtp = data.factor2["running_balance"]
#             print(f"LAST RTP::: {last_rtp}")

#             if const_obj.salary_4_life_running_balance == last_rtp:
#                 EmailHandler("<EMAIL>").salary4life_rtp_check_email(
#                     "BAD SALARY FOR LIFE RTP. VALUE RESET"
#                 )
#                 EmailHandler("<EMAIL>").salary4life_rtp_check_email(
#                     "BAD SALARY FOR LIFE RTP. VALUE RESET"
#                 )
#                 ConstantVariable.objects.all().update(salary_4_life_running_balance=0)
#                 return
#     except Exception:
#         pass

#     try:
#         LottoTicket.salary_for_life_draw_pos()
#         return "Salary for Life Draw Completed"
#     except TypeError:
#         print("FAILED THIS TIME. RETRYING")
#         LottoTicket.salary_for_life_draw_pos()
#         return "Salary for Life Draw Completed"


@shared_task
def celery_wyse_cash_draw():
    from main.models import LotteryModel

    LotteryModel.wyse_cash_game_draw()
    return "Wyse Cash Draw Completed"


@shared_task
def verify_woven_payout():
    from main.models import PayoutTransactionTable

    PayoutTransactionTable.payout_verification()
    return "Payout Verification Completed"


# @shared_task
# def share_soccer_payment_across(phone, amount, game_play_id):
#     from wyse_ussd.models import SoccerPrediction
#     soccer_prediction_obj = SoccerPrediction.objects.filter(game_id = game_play_id).last()

#     if soccer_prediction_obj is None:
#         return


@shared_task
def update_engage_user(user_id):
    sleep(3)

    from account.models import User

    user_instance = User.objects.filter(id=user_id).last()

    payload = {
        "first_name": user_instance.first_name,
        "last_name": user_instance.last_name,
        "email": user_instance.email,
        "number": user_instance.phone,
        "created_at": f"{user_instance.created_at.date()}",
    }

    print(payload)

    url = f"https://api.engage.so/v1/users/{user_instance.engage_uuid}"
    print(url)

    STRING_VALUE = f"{settings.ENGAGE_API_KEY}:{settings.ENGAGE_SECRET_KEY}"
    AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

    headers = {
        "Authorization": f"Basic {AUTH_TOKEN}",
        "Content-Type": "application/json",
    }

    response = requests.request("PUT", url, headers=headers, json=payload)

    print("user_id", user_instance.engage_uuid)
    print(response.text)
    return "User Updated on Engage"


@shared_task
def get_list_of_engage_users():
    url = "https://api.engage.so/v1/users/LIBPAY-12"
    print(url)

    STRING_VALUE = f"{settings.ENGAGE_API_KEY}:{settings.ENGAGE_SECRET_KEY}"
    AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

    headers = {
        "Authorization": f"Basic {AUTH_TOKEN}",
        "Content-Type": "application/json",
    }

    response = requests.request("GET", url, headers=headers)
    print(response.text)
    return response.json()


@shared_task
def winner_engange_event(user_id, event_name, is_user_profile_id=False):
    from account.models import User

    user_instance = User.objects.filter(id=user_id).last()

    payload = {
        "event": event_name,
        "timestamp": timezone.now(),
        # "user_id": user_instance.engage_uuid,
    }

    print(payload)
    try:
        url = f"https://api.engage.so/v1/users/{user_instance.engage_id}events"
        print(url)

        STRING_VALUE = f"{settings.ENGAGE_API_KEY}:{settings.ENGAGE_SECRET_KEY}"
        AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

        headers = {
            "Authorization": f"Basic {AUTH_TOKEN}",
            "Content-Type": "application/json",
        }

        response = requests.request("POST", url, headers=headers, json=payload)

        print("user_id", user_instance.engage_uuid)
        print(response.text)
        return "Event Added on Engage"
    except Exception:
        pass


@shared_task
def lottery_play_engange_event(user_id, is_user_profile_id=False, **kwargs):
    # from account.models import User
    # from main.models import UserProfile

    # if is_user_profile_id is True:
    #     user_profile = UserProfile.objects.filter(id=user_id).last()
    #     user_instance = User.objects.filter(phone=user_profile.phone_number).last()
    #     if user_instance is None:
    #         return {"status": "failed", "message": "User not found"}

    # else:
    #     user_instance = User.objects.filter(id=user_id).last()
    #     if user_instance is None:
    #         return {"status": "failed", "message": "User not found"}

    # payload = kwargs
    # payload["timestamp"] = f"{timezone.now()}"

    # # payload = {
    # #     "event": event_name,
    # #     "timestamp": timezone.now(),
    # #     # "user_id": user_instance.engage_uuid,
    # # }

    # print(payload)

    # url = f"https://api.engage.so/v1/users/{user_instance.engage_uuid}/events"
    # print(url)

    # STRING_VALUE = f"{settings.ENGAGE_API_KEY}:{settings.ENGAGE_SECRET_KEY}"
    # AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

    # headers = {
    #     "Authorization": f"Basic {AUTH_TOKEN}",
    #     "Content-Type": "application/json",
    # }

    # response = requests.request("POST", url, headers=headers, json=payload)

    # print("user_id", user_instance.engage_uuid)
    # print(response.text)
    # return "Event Added on Engage"
    pass


@shared_task
def send_daily_report_mail():
    from main.models import LotteryModel, LotteryWinnersTable, LottoTicket, LottoWinners
    from pos_app.models import PosLotteryWinners

    today = timezone.now().date()
    # today = "2022-11-08"

    lotto_tickets = LottoTicket.objects.filter(date__date=today).values()

    if lotto_tickets.count() > 0:
        lotto_ticket_df = pd.DataFrame(lotto_tickets)
        lotto_ticket_df["date"] = lotto_ticket_df["date"].astype(str)

    else:
        lotto_ticket_df = None

    lottery_models = LotteryModel.objects.filter(date__date=today).values()

    if lottery_models.count() > 0:
        lottery_model_df = pd.DataFrame(lottery_models)
        lottery_model_df["date"] = lottery_model_df["date"].astype(str)
        lottery_model_df["last_updated"] = lottery_model_df["last_updated"].astype(str)

    else:
        lottery_model_df = None

    pos_lottery_winner = PosLotteryWinners.objects.filter(date_created__date=today).values()

    if pos_lottery_winner.count() > 0:
        pos_lottery_winner_df = pd.DataFrame(pos_lottery_winner)
        pos_lottery_winner_df["date_created"] = pos_lottery_winner_df["date_created"].astype(str)
        pos_lottery_winner_df["date_updated"] = pos_lottery_winner_df["date_updated"].astype(str)

    else:
        pos_lottery_winner_df = None

    lotto_winners = LottoWinners.objects.filter(date_won__date=today).values()

    if lotto_winners.count() > 0:
        lotto_winners_df = pd.DataFrame(lotto_winners)
        lotto_winners_df["date_won"] = lotto_winners_df["date_won"].astype(str)
        lotto_winners_df["last_updated"] = lotto_winners_df["last_updated"].astype(str)

    else:
        lotto_winners = None

    lottery_winners = LotteryWinnersTable.objects.filter(date_won__date=today).values()

    if lottery_winners.count() > 0:
        lottery_winners_df = pd.DataFrame(lottery_winners)
        lottery_winners_df["date_won"] = lottery_winners_df["date_won"].astype(str)
        lottery_winners_df["last_updated"] = lottery_winners_df["last_updated"].astype(str)

    else:
        lottery_winners_df = None

    try:
        with pd.ExcelWriter("dailyReport.xlsx") as writer:
            if lotto_winners is not None:
                lotto_winners.to_excel(writer, sheet_name="Lotto Winner", index=False)

            else:
                pass

            if lottery_winners_df is not None:
                lottery_winners_df.to_excel(writer, sheet_name="Lottery Winner", index=False)

            else:
                pass

            if pos_lottery_winner_df is not None:
                pos_lottery_winner_df.to_excel(writer, sheet_name="Pos Lottery Winner", index=False)

            else:
                pass

            if lottery_model_df is not None:
                lottery_model_df.to_excel(writer, sheet_name="Lottery model", index=False)

            else:
                pass

            if lotto_ticket_df is not None:
                lotto_ticket_df.to_excel(writer, sheet_name="Lotto Ticket", index=False)

            else:
                pass

        file_path = os.path.abspath("dailyReport.xlsx")
        # print(file_path)

        email_list = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]

        for user_email in email_list:
            try:
                response = requests.post(
                    "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
                    auth=("api", f"{settings.MAILGUN_API_KEY}"),
                    data={
                        "from": "Whysecash <<EMAIL>>",
                        "to": f"{user_email}",
                        "subject": "Daily Report",
                        # "html": "",
                        "text": "Whysecash Daily Report",
                    },
                    files=[
                        (
                            "attachment",
                            ("winwise_report.xlsx", open(str(file_path), "rb").read()),
                        )
                    ],
                )

                print(response.text)
                print("EMAIL SENT")
            except Exception as e:
                print("failed to send email", e)
                print("EMAIL FAILED")

    except IndexError:
        print("CURRENT DATA IS NONE")
        return "CURRENT DATA IS NONE"


@shared_task
def low_balance_notification(amount_request, phone, wallet_bal):
    return general_withdraw_wallet_bal_notification(user_phone=phone, amount_requested=amount_request, wallet_balance=wallet_bal)


@shared_task
def notify_agents_on_lottery_batch_draw(batch_id, lottery_type, phone_number=None, draw_date=None):
    from main.models import (
        ConstantVariable,
        DrawData,
        LotteryBatch,
        LotteryWinnersTable,
        LottoTicket,
        LottoWinners,
        UserProfile,
    )
    from pos_app.pos_helpers import machine_number_serializer

    datetime.now()
    if draw_date is None:
        get_current_time = ((datetime.now() + timedelta(minutes=0)).time()).strftime("%H:%M")
        current_time = datetime.strptime(str(get_current_time), "%H:%M")
        current_time = current_time.strftime("%I:%M %p")
    else:
        current_time = draw_date

    # if batch_id == 15345:
    #     return pos_salary_for_life_game_draw_to_whatsapp(batch_id)

    lottery_batch = LotteryBatch.objects.get(id=batch_id)

    # ----------------------- CHECKING IF BANKER DRAW OR SALARY FOR LIFE DRAW DOESN'T HAVE WINNER ----------------------- #
    if lottery_batch.lottery_winner_ticket_number is None or lottery_batch.lottery_winner_ticket_number == "":
        if lottery_batch.lottery_type in ["SALARY_FOR_LIFE", "BANKER"]:
            lottery_tickets = LottoTicket.objects.filter(batch__batch_uuid=lottery_batch)

            if lottery_tickets.exists():
                user_ticket_numbers = []
                for ticket in lottery_tickets:
                    user_ticket_pick = str(ticket.ticket).split(",")
                    _pick = [int(i) for i in user_ticket_pick]
                    user_ticket_numbers.append(_pick)

            try:
                random_system_pick_num = LottoTicket.generate_random_system_pick_number_for_sal_4_life_and_banker(
                    numbers=user_ticket_numbers, matches_required=1
                )

                lottery_batch.lottery_winner_ticket_number = f",{random_system_pick_num}"
                lottery_batch.save()
            except Exception:
                pass

            draw_date = lottery_batch.draw_date
            if draw_date is None:
                draw_date = lottery_batch.last_updated
                if draw_date is None:
                    draw_date = datetime.now()

    # ----------------------- END OF CHECKING IF BANKER DRAW OR SALARY FOR LIFE DRAW DOESN'T HAVE WINNER ----------------------- #

    if settings.DEBUG is True or settings.DEBUG:
        return {"status": "success", "message": "DEBUG MODE"}

    if lottery_type == "SALARY_FOR_LIFE" or lottery_type == "INSTANT_CASHOUT" or lottery_type == "BANKER":
        if lottery_type == "SALARY_FOR_LIFE":
            if ConstantVariable().is_salary_for_life_draw_merge() is True:
                pass
            else:
                if lottery_batch.is_pos_batch is False:
                    return {"message": "POST ONLY POS BATCH DRAW WHEN MERGE IS OFF"}

            lottery_winners_qs = LottoWinners.objects.filter(batch__id=batch_id)
            game_type = lottery_type.replace("_", " ").title()

            # print("lottery_winners_qs", not lottery_winners_qs.exists())

            system_pick_num = machine_number_serializer(lottery_batch.lottery_winner_ticket_number)
            machine_number = None

            if len(system_pick_num) > 1:
                for index, num in enumerate(system_pick_num):
                    if index == 0:
                        machine_number = ""
                    else:
                        machine_number += f"{num},\n"

            winning_numbers = system_pick_num[0] if len(system_pick_num) > 0 else None

            # print("winning_numbers", winning_numbers)

            if machine_number is None:
                if winning_numbers is None:
                    if lottery_batch.is_pos_batch is True:
                        draw_meta_data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE_POS").last()
                    else:
                        draw_meta_data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE").last()

                    # print("draw_meta_data", draw_meta_data)
                    factor_3 = draw_meta_data.factor3
                    # print("factor_3", factor_3)
                    best_match_combo = factor_3.get("best_match_combo")
                    if best_match_combo is None:
                        return {"message": "no best_match_combo"}

                    else:
                        # print("best_match_combo", best_match_combo)

                        comma_separated_str = str(best_match_combo[0])
                        for i in range(1, len(best_match_combo)):
                            comma_separated_str += "," + str(best_match_combo[i])

                        whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                        whatsapp_payload = {
                            "token": f"{settings.PICKY_ASSIST_TOKEN}",
                            "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                            "application": "10",
                            "mention_numbers": [],
                            "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{comma_separated_str}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                        }

                        whatsapp_Headers = {"Content-type": "application/json"}
                        whatsapp_response = requests.post(
                            whatsapp_url,
                            json=whatsapp_payload,
                            headers=whatsapp_Headers,
                        )

                        # whatsapp_url = "https://pickyassist.com/app/api/v2/push"

                        # message = f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{comma_separated_str}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------"

                        # phones = ["2348077469471", "2347039115243", "2347039516293"]

                        # for phone_number in phones:

                        #     whatsapp_payload = json.dumps(
                        #         {
                        #             "token": f"{settings.PICKY_ASSIST_TOKEN}",
                        #             "priority ": "0",
                        #             "application": "10",
                        #             "data": [
                        #                 {
                        #                     "number": f"{phone_number}",
                        #                     "message": message,
                        #                 }
                        #             ],
                        #         }
                        #     )
                        #     whatsapp_Headers = {"Content-type": "application/json"}

                        #     try:
                        #         whatsapp_response = requests.request(
                        #             "POST",
                        #             whatsapp_url,
                        #             headers=whatsapp_Headers,
                        #             data=whatsapp_payload,
                        #         )
                        #         whatsapp_response = whatsapp_response

                        #         print("whatsapp_response", whatsapp_response)

                        #     except requests.exceptions.RequestException as err:
                        #         whatsapp_response = {
                        #             "status": "error",
                        #             "message": err,
                        #         }

                whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                whatsapp_payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                    "application": "10",
                    "mention_numbers": [],
                    "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                }

                whatsapp_Headers = {"Content-type": "application/json"}

                whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

                # whatsapp_url = "https://pickyassist.com/app/api/v2/push"

                # message = f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------"

                # phones = ["2348077469471", "2347039115243", "2347039516293"]

                # for phone_number in phones:

                #     whatsapp_payload = json.dumps(
                #         {
                #             "token": f"{settings.PICKY_ASSIST_TOKEN}",
                #             "priority ": "0",
                #             "application": "10",
                #             "data": [
                #                 {"number": f"{phone_number}", "message": message}
                #             ],
                #         }
                #     )
                #     whatsapp_Headers = {"Content-type": "application/json"}

                #     try:
                #         whatsapp_response = requests.request(
                #             "POST",
                #             whatsapp_url,
                #             headers=whatsapp_Headers,
                #             data=whatsapp_payload,
                #         )
                #         whatsapp_response = whatsapp_response

                #         print("whatsapp_response", whatsapp_response)

                #     except requests.exceptions.RequestException as err:
                #         whatsapp_response = {"status": "error", "message": err}

            elif winning_numbers is not None:
                whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                whatsapp_payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                    "application": "10",
                    "mention_numbers": [],
                    "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\nRe run:\n{machine_number}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                }

                whatsapp_Headers = {"Content-type": "application/json"}

                whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

                # whatsapp_url = "https://pickyassist.com/app/api/v2/push"

                # message = f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\nRe run:\n{machine_number}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------"

                # phones = ["2348077469471", "2347039115243", "2347039516293"]

                # for phone_number in phones:

                #     whatsapp_payload = json.dumps(
                #         {
                #             "token": f"{settings.PICKY_ASSIST_TOKEN}",
                #             "priority ": "0",
                #             "application": "10",
                #             "data": [
                #                 {"number": f"{phone_number}", "message": message}
                #             ],
                #         }
                #     )
                #     whatsapp_Headers = {"Content-type": "application/json"}

                #     try:
                #         whatsapp_response = requests.request(
                #             "POST",
                #             whatsapp_url,
                #             headers=whatsapp_Headers,
                #             data=whatsapp_payload,
                #         )
                #         whatsapp_response = whatsapp_response
                #         print(
                #             f"""

                #                 whatsapp_response: {whatsapp_response}

                #             """
                #         )

                #     except requests.exceptions.RequestException as err:
                #         whatsapp_response = {"status": "error", "message": err}

            else:
                if lottery_batch.is_pos_batch is True:
                    draw_meta_data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE_POS").last()
                    # print("draw_meta_data", draw_meta_data)
                    factor_3 = draw_meta_data.factor3
                    # print("factor_3", factor_3)
                    best_match_combo = factor_3.get("best_match_combo")
                    if best_match_combo is not None:
                        # print("best_match_combo", best_match_combo)

                        comma_separated_str = str(best_match_combo[0])
                        for i in range(1, len(best_match_combo)):
                            comma_separated_str += "," + str(best_match_combo[i])

                        whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                        whatsapp_payload = {
                            "token": f"{settings.PICKY_ASSIST_TOKEN}",
                            "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                            "application": "10",
                            "mention_numbers": [],
                            "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{comma_separated_str}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                        }

                        whatsapp_Headers = {"Content-type": "application/json"}
                        whatsapp_response = requests.post(
                            whatsapp_url,
                            json=whatsapp_payload,
                            headers=whatsapp_Headers,
                        )

                    #     whatsapp_url = "https://pickyassist.com/app/api/v2/push"

                    #     message = f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{comma_separated_str}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------"

                    #     phones = ["2348077469471", "2347039115243", "2347039516293"]

                    #     for phone_number in phones:

                    #         whatsapp_payload = json.dumps(
                    #             {
                    #                 "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    #                 "priority ": "0",
                    #                 "application": "10",
                    #                 "data": [
                    #                     {
                    #                         "number": "2347039115243",
                    #                         "message": message,
                    #                     }
                    #                 ],
                    #             }
                    #         )
                    #         whatsapp_Headers = {"Content-type": "application/json"}

                    #         try:
                    #             whatsapp_response = requests.request(
                    #                 "POST",
                    #                 whatsapp_url,
                    #                 headers=whatsapp_Headers,
                    #                 data=whatsapp_payload,
                    #             )
                    #             whatsapp_response = whatsapp_response

                    #             print(
                    #                 f"""

                    #                 whatsapp_response: {whatsapp_response}
                    #             """
                    #             )

                    #         except requests.exceptions.RequestException as err:
                    #             whatsapp_response = {
                    #                 "status": "error",
                    #                 "message": err,
                    #             }

                    # else:
                    #     print("best_match_combo is None")
                    #     return {"message": "best_match_combo is None"}

            return f"Whatsapp Message sent to agent on game draw. Response: {whatsapp_response.text}"
            # return f"Whatsapp Message sent to agent on game draw. Response:"

        elif lottery_type == "BANKER":
            game_type = lottery_type.replace("_", " ").title()

            system_pick_num = machine_number_serializer(lottery_batch.lottery_winner_ticket_number)

            machine_number = None

            if len(system_pick_num) > 1:
                for index, num in enumerate(system_pick_num):
                    if index == 0:
                        machine_number = ""
                    else:
                        machine_number += f"{num},\n"

            winning_numbers = system_pick_num[0] if len(system_pick_num) > 0 else None

            if machine_number is None:
                if winning_numbers is None:
                    return "No winning numbers"

                if "[" in winning_numbers:
                    lottery_tickets = LottoTicket.objects.filter(batch__batch_uuid=lottery_batch, paid=True)

                    if lottery_tickets.exists():
                        user_ticket_numbers = []
                        for ticket in lottery_tickets:
                            user_ticket_pick = str(ticket.ticket).split(",")
                            _pick = [int(i) for i in user_ticket_pick]
                            user_ticket_numbers.append(_pick)

                        # user_num_picks = list(user_ticket_numbers)

                        random_system_pick_num = LottoTicket.generate_random_system_pick_number_for_sal_4_life_and_banker(
                            numbers=user_ticket_numbers, matches_required=1
                        )

                        lottery_batch.lottery_winner_ticket_number = f",{random_system_pick_num}"
                        lottery_batch.save()

                        whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                        whatsapp_payload = {
                            "token": f"{settings.PICKY_ASSIST_TOKEN}",
                            "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                            "application": "10",
                            "mention_numbers": [],
                            "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{random_system_pick_num}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                        }

                        # message = f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{random_system_pick_num}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------"

                        # whatsapp_payload = {
                        #     "token": f"{settings.PICKY_ASSIST_TOKEN}",
                        #     "priority ": "0",
                        #     "application": "10",
                        #     "data": [{"number": f"2347039115243", "message": message}],
                        # }

                        whatsapp_Headers = {"Content-type": "application/json"}

                        whatsapp_response = requests.post(
                            whatsapp_url,
                            json=whatsapp_payload,
                            headers=whatsapp_Headers,
                        )

                    else:
                        random_system_pick_num = []
                        while len(random_system_pick_num) < 5:
                            random_system_pick_num.append(random.randint(1, 50))

                        lottery_ticket_numbers = ",".join([str(num) for num in random_system_pick_num])

                        lottery_batch.lottery_winner_ticket_number = f",{lottery_ticket_numbers}"
                        lottery_batch.save()

                        whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                        whatsapp_payload = {
                            "token": f"{settings.PICKY_ASSIST_TOKEN}",
                            "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                            "application": "10",
                            "mention_numbers": [],
                            "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{lottery_ticket_numbers}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",  # noqa
                        }

                        # message = f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{lottery_ticket_numbers}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------"

                        # whatsapp_payload = {
                        #     "token": f"{settings.PICKY_ASSIST_TOKEN}",
                        #     "priority ": "0",
                        #     "application": "10",
                        #     "data": [{"number": f"2347039115243", "message": message}],
                        # }

                        whatsapp_Headers = {"Content-type": "application/json"}

                        whatsapp_response = requests.post(
                            whatsapp_url,
                            json=whatsapp_payload,
                            headers=whatsapp_Headers,
                        )

                    return

                whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                whatsapp_payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                    "application": "10",
                    "mention_numbers": [],
                    "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                }

                # message = f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------"

                # whatsapp_payload = {
                #     "token": f"{settings.PICKY_ASSIST_TOKEN}",
                #     "priority ": "0",
                #     "application": "10",
                #     "data": [{"number": f"2347039115243", "message": message}],
                # }

                whatsapp_Headers = {"Content-type": "application/json"}

                whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

                return f"Whatsapp Message sent to agent on game draw. Response: {whatsapp_response.text}"

            elif winning_numbers is not None:
                whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                whatsapp_payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                    "application": "10",
                    "mention_numbers": [],
                    "globalmessage": "Hello Agents,",
                }

                # message = f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\nRe run:\n{machine_number}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------"

                # whatsapp_payload = {
                #     "token": f"{settings.PICKY_ASSIST_TOKEN}",
                #     "priority ": "0",
                #     "application": "10",
                #     "data": [{"number": f"2347039115243", "message": message}],
                # }

                whatsapp_Headers = {"Content-type": "application/json"}

                whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

                return f"Whatsapp Message sent to agent on game draw. Response: {whatsapp_response.text}"

            else:
                return "No winning numbers"

        else:
            return {"message": "INSTANT CASHOUT GAME DRAW NOTIFICATION HAS BEEN DISABLED"}

            lottery_winners_qs = LottoWinners.objects.filter(batch__id=batch_id, lottery__user_profile__phone_number=phone_number)
            if not lottery_winners_qs.exists():
                return None

            # winning_tickets = [i.ticket for i in lottery_winners_instance]
            # winning_numbers = ",\n".join(winning_tickets)

            lottery_winners_instance = lottery_winners_qs.last()

            lotto_ticket_instance = LottoTicket.objects.filter(
                user_profile__phone_number=phone_number,
                game_play_id=lottery_winners_instance.game_play_id,
            ).last()

            if lotto_ticket_instance is not None:
                winning_numbers = lotto_ticket_instance.system_generated_num
                if winning_numbers is None:
                    winning_numbers = lottery_winners_instance.ticket

            else:
                winning_numbers = lottery_winners_instance.ticket

            game_type = lottery_type.replace("_", " ").title()

            whatsapp_url = "https://pickyassist.com/app/api/v2/push"
            whatsapp_payload = {
                "token": f"{settings.PICKY_ASSIST_TOKEN}",
                "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                "application": "10",
                "mention_numbers": [],
                "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
            }

            whatsapp_Headers = {"Content-type": "application/json"}

            whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

            # print(
            #     f"""

            #               whatsapp_payload: {whatsapp_payload},
            #               \n\n\n\n\n\n\n\n

            #               """
            # )

            # return f"Whatsapp Message sent to agent on game draw. Response:"
            return f"Whatsapp Message sent to agent on game draw. Response: {whatsapp_response.text}"

    elif lottery_type == "WYSE_CASH":
        lottery_winners_qs = LotteryWinnersTable.objects.filter(batch__id=batch_id)
        print(
            """
              lottery_winners_qs: {lottery_winners_qs}
              """
        )
        game_type = lottery_type.replace("_", " ").title()
        if lottery_winners_qs is None:
            print("lottery_winners_qs is None")
            winning_numbers = None
        else:
            print("lottery_winners_qs is not None")
            lottery_winners_qs.count()
            lottery_winners_qs.aggregate(Sum("earning"))["earning__sum"]
            winning_tickets = [i.ticket for i in lottery_winners_qs]
            winning_numbers = ",\n".join(winning_tickets)

        if winning_numbers is None or winning_numbers == "":
            number_of_random_ticket = random.randint(1, 5)
            user_profiles = UserProfile.objects.all()
            user_profile = random.choice(user_profiles)
            wyse_cash_ticket = generate_lucky_number(user_profile, from_pos=True)
            winning_tickets = []
            bands = [key for key in wyse_cash_ticket.get("data", {}).get("bands")]
            print(f"bands: {bands}", "\n\n\n")
            if len(bands) > 0:
                for i in range(number_of_random_ticket):
                    band = random.choice(bands)
                    lottery_data = wyse_cash_ticket.get("data", {}).get("lottery_data", {})
                    band_ticket = lottery_data.get(band)
                    if band_ticket is not None:
                        inner_band_ticket_dict = band_ticket[0]
                        for key in inner_band_ticket_dict:
                            print(f"key: {key}")
                            if key == "number":
                                if inner_band_ticket_dict.get("number") is not None and inner_band_ticket_dict.get("number") not in winning_tickets:
                                    winning_tickets.append(inner_band_ticket_dict.get("number"))
                                    break

                    else:
                        print("band_ticket is None")

            winning_numbers = ",\n".join(winning_tickets)
            if winning_numbers is None or winning_numbers == "":
                return None

        whatsapp_url = "https://pickyassist.com/app/api/v2/push"
        whatsapp_payload = {
            "token": f"{settings.PICKY_ASSIST_TOKEN}",
            "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
            "application": "10",
            "mention_numbers": [],
            "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {current_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
        }

        whatsapp_Headers = {"Content-type": "application/json"}

        whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

        # print(
        #     f"""

        # whatsapp_payload: {whatsapp_payload},

        # """
        # )

        return f"Whatsapp Message sent to agent on game draw. Response: {whatsapp_response.text}"
        # return f"Whatsapp Message sent to agent on game draw. Response:"

    else:
        return f"Lottery type {lottery_type} not supported"


def notify_admin_whatsapp_on_float_low_balance(current_bal):
    phones = ["2348077469471", "2347039115243", "2347039516293"]

    whatsapp_res = None

    for phone_number in phones:
        whatsapp_url = "https://pickyassist.com/app/api/v2/push"

        message = f"WHISPAKONNECT ALERT!!! BALANCE IS LOW.\nCurrent Balance is - {current_bal}\nPlease Fund as soon as possible"

        whatsapp_payload = json.dumps(
            {
                "token": f"{settings.PICKY_ASSIST_TOKEN}",
                "priority ": "0",
                "application": "10",
                "data": [{"number": f"{phone_number}", "message": message}],
            }
        )
        whatsapp_Headers = {"Content-type": "application/json"}

        try:
            whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
            whatsapp_res = whatsapp_response

        except requests.exceptions.RequestException as err:
            whatsapp_res = {"status": "error", "message": err}

    return whatsapp_res


@shared_task
def check_vfd_float_account_and_notify_admin():
    pass

    # vfd_float_wallet = FloatWallet().get_float_wallet(source="VFD")
    # if vfd_float_wallet.amount < 50000:
    #     # notify_admin_whatsapp_on_float_low_balance(vfd_float_wallet.amount)
    #     amount = currency_formatter(vfd_float_wallet.amount)
    #     template_dir = os.path.join(
    #         settings.BASE_DIR, "templates/float_wallet_norification.html"
    #     )
    #     with open(template_dir) as temp_file:
    #         template = temp_file.read()
    #         temp_file.close()
    #     template = Template(template).safe_substitute(amount=amount)
    #     emails = ["<EMAIL>", "<EMAIL>"]
    #     for email in emails:
    #         data = {
    # <AUTHOR> <EMAIL>",
    #             "to": email,
    #             "subject": "LOW FLOAT WALLET BALANCE",
    #             "html": template,
    #         }
    #         message = requests.post(
    #             "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
    #             auth=("api", f"{settings.MAILGUN_API_KEY}"),
    #             data=data,
    #         )
    # return f"VFD Float Wallet balance is {vfd_float_wallet.amount}"


def pos_salary_for_life_game_draw_to_whatsapp(batch_id, batch_draw_time="9:00am"):
    """
    This function sends a whatsapp message of salary for life game draw to failed to post initially
    """

    from main.models import ConstantVariable, DrawData, LotteryBatch, LottoWinners
    from pos_app.pos_helpers import machine_number_serializer

    lottery_batch = LotteryBatch.objects.get(id=batch_id)

    if ConstantVariable().is_salary_for_life_draw_merge() is True:
        pass
    else:
        if lottery_batch.is_pos_batch is False:
            return {"message": "POS ONLY POS BATCH DRAW WHEN MERGE IS OFF"}

        lottery_winners_qs = LottoWinners.objects.filter(batch__id=batch_id)
        game_type = "SALARY_FOR_LIFE"
        if lottery_winners_qs is None:
            machine_number = None

            if lottery_batch.is_pos_batch is True:
                draw_meta_data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE_POS").last()
            else:
                draw_meta_data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE").last()

            # print("draw_meta_data", draw_meta_data)
            factor_3 = draw_meta_data.factor3
            # print("factor_3", factor_3)
            best_match_combo = factor_3.get("best_match_combo")
            if best_match_combo is None:
                return {"message": "no best_match_combo"}

            else:
                # print("best_match_combo", best_match_combo)

                comma_separated_str = str(best_match_combo[0])
                for i in range(1, len(best_match_combo)):
                    comma_separated_str += "," + str(best_match_combo[i])

                whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                whatsapp_payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                    "application": "10",
                    "mention_numbers": [],
                    "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {batch_draw_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{comma_separated_str}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                }

                whatsapp_Headers = {"Content-type": "application/json"}
                whatsapp_response = requests.post(
                    whatsapp_url,
                    json=whatsapp_payload,
                    headers=whatsapp_Headers,
                )

        else:
            system_pick_num = machine_number_serializer(lottery_batch.lottery_winner_ticket_number)
            machine_number = None

            if len(system_pick_num) > 1:
                for index, num in enumerate(system_pick_num):
                    if index == 0:
                        machine_number = ""
                    else:
                        machine_number += f"{num},\n"

            winning_numbers = system_pick_num[0] if len(system_pick_num) > 0 else None

            # print("winning_numbers", winning_numbers)

            if machine_number is None:
                if winning_numbers is None:
                    if lottery_batch.is_pos_batch is True:
                        draw_meta_data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE_POS").last()
                    else:
                        draw_meta_data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE").last()

                    # print("draw_meta_data", draw_meta_data)
                    factor_3 = draw_meta_data.factor3
                    # print("factor_3", factor_3)
                    best_match_combo = factor_3.get("best_match_combo")
                    if best_match_combo is None:
                        return {"message": "no best_match_combo"}

                    else:
                        # print("best_match_combo", best_match_combo)

                        comma_separated_str = str(best_match_combo[0])
                        for i in range(1, len(best_match_combo)):
                            comma_separated_str += "," + str(best_match_combo[i])

                        whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                        whatsapp_payload = {
                            "token": f"{settings.PICKY_ASSIST_TOKEN}",
                            "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                            "application": "10",
                            "mention_numbers": [],
                            "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {batch_draw_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{comma_separated_str}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                        }

                        whatsapp_Headers = {"Content-type": "application/json"}
                        whatsapp_response = requests.post(
                            whatsapp_url,
                            json=whatsapp_payload,
                            headers=whatsapp_Headers,
                        )

                whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                whatsapp_payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                    "application": "10",
                    "mention_numbers": [],
                    "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {batch_draw_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                }

                whatsapp_Headers = {"Content-type": "application/json"}

                whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

                # print(
                #     f"""

                #       whatsapp_payload: {whatsapp_payload},

                #       """
                # )
            elif winning_numbers is not None:
                whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                whatsapp_payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                    "application": "10",
                    "mention_numbers": [],
                    "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {batch_draw_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{winning_numbers}\n\nRe run:\n{machine_number}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",  # noqa
                }

                whatsapp_Headers = {"Content-type": "application/json"}

                whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

                # print(
                #     f"""

                #       whatsapp_payload: {whatsapp_payload},
                #       """
                # )
            else:
                if lottery_batch.is_pos_batch is True:
                    draw_meta_data = DrawData.objects.filter(game_type="SALARY_FOR_LIFE_POS").last()
                    # print("draw_meta_data", draw_meta_data)
                    factor_3 = draw_meta_data.factor3
                    # print("factor_3", factor_3)
                    best_match_combo = factor_3.get("best_match_combo")
                    if best_match_combo is not None:
                        # print("best_match_combo", best_match_combo)

                        comma_separated_str = str(best_match_combo[0])
                        for i in range(1, len(best_match_combo)):
                            comma_separated_str += "," + str(best_match_combo[i])

                        whatsapp_url = "https://pickyassist.com/app/api/v2/push"
                        whatsapp_payload = {
                            "token": f"{settings.PICKY_ASSIST_TOKEN}",
                            "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
                            "application": "10",
                            "mention_numbers": [],
                            "globalmessage": f"Hello Agents,\n\n----------------------------------------\n\n {game_type} {batch_draw_time} Lottery Draw has been completed.\n\n Batch ID: {lottery_batch.batch_uuid}\n\nWinning numbers are:\n{comma_separated_str}\n\n\nBe Wise, be Lucky.\nWinWise\n--------------------------------------------------------",
                        }

                        whatsapp_Headers = {"Content-type": "application/json"}
                        whatsapp_response = requests.post(
                            whatsapp_url,
                            json=whatsapp_payload,
                            headers=whatsapp_Headers,
                        )

                        # print(
                        #     f"""

                        #     whatsapp_payload: {whatsapp_payload},
                        #     """
                        # )
                    else:
                        print("best_match_combo is None")
                        return {"message": "best_match_combo is None"}

        return f"Whatsapp Message sent to agent on game draw. Response: {whatsapp_response.text}"


@shared_task
def run_banker_draw():
    from main.models import LottoTicket

    LottoTicket.banker_draw()
    return "Running banker draw....................."


@shared_task
def check_virtual_soccer_winnings_with_150_stake_amount():
    from main.models import LottoTicket, LottoWinners

    winners_qs = LottoWinners.objects.filter(lotto_type="VIRTUAL_SOCCER", stake_amount=150)

    data_header = [
        "phone",
        "name",
        "game_play_id",
        "earning",
        "channel",
        "user_stake_amount",
        "date",
    ]

    data = []

    if winners_qs:
        for winner in winners_qs:
            _loop_data = [
                winner.lottery.user_profile.phone_number,
                (winner.lottery.agent_profile.full_name if winner.lottery.agent_profile else winner.lottery.user_profile.first_name),
                winner.lottery.game_play_id,
                winner.earning,
                winner.lottery.channel,
                LottoTicket.objects.filter(game_play_id=winner.lottery.game_play_id).aggregate(Sum("amount_paid"))["amount_paid__sum"],
                winner.lottery.date.strftime("%Y-%m-%d %H:%M:%S"),
            ]

            data.append(_loop_data)

    whatsapp_res = None

    if data:
        with open(
            "pickyfolder/virtual_soccer_winnings.csv",
            "w",
            encoding="UTF-8",
            newline="",
        ) as f:
            writer = csv.writer(f)
            writer.writerow(data_header)
            writer.writerows(data)

            BASE_DIR_SET = Path(__file__).resolve().parent.parent
            file_path = os.path.join(BASE_DIR_SET, "pickyfolder")

            new_file_path = os.path.join(file_path, "pickyfolder/virtual_soccer_winnings.csv")
            if os.stat(new_file_path).st_size == 0:
                return "NO data"

            host_server_site = settings.WINWISE_LOTTO_BACKEND_URL

            url = f"{host_server_site}/liberty/download_media_file/virtual_soccer_winnings.csv"

            picky_url = "https://pickyassist.com/app/api/v2/push"

            phones = ["2347039115243"]

            for phone_number in phones:
                payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "priority ": "0",
                    "application": "10",
                    "data": [
                        {
                            "number": phone_number,
                            "message": f"Link: {url}\n\n--------------------------------------",
                            "media_file": "pos_lottery_winners_payout.csv",
                            "globalmedia": url,
                        }
                    ],
                }

                headers = {"Content-type": "application/json"}

                try:
                    whatsapp_response = requests.request("POST", picky_url, headers=headers, json=payload)
                    whatsapp_res = whatsapp_response

                except requests.exceptions.RequestException as err:
                    whatsapp_res = {"status": "error", "message": err}

            return f"Whatsapp Message sent to admin response {whatsapp_res}"


@shared_task
def update_failed_paid_ticket():
    from main.models import LottoTicket

    tickets = LottoTicket.objects.filter(id__gt=595000, number_of_ticket=2, lottery_type="INSTANT_CASHOUT", paid=True)
    for ticket in tickets:
        try:
            failed_ticket = LottoTicket.objects.get(game_play_id=ticket.game_play_id, paid=False)
        except LottoTicket.DoesNotExist:
            continue
        failed_ticket.paid = True
        failed_ticket.save()

    return "UPDATED FAILED TICKET"


@shared_task
def cascade_jackpot_to_one_pool():
    from django.db.models import F

    from main.models import Jackpot

    jumbo_active_jackpot = Jackpot.objects.filter(is_active=True, jackpot_type="CROWN_JUMBO_JACKPOT").last()
    mega_active_jackpot = Jackpot.objects.filter(is_active=True, jackpot_type="MEGA_JACKPOT").last()

    jackpot = 0

    if jumbo_active_jackpot:
        jackpot += jumbo_active_jackpot.contributed_amount
        jumbo_active_jackpot.contributed_amount = 0
        jumbo_active_jackpot.save()

    if mega_active_jackpot:
        jackpot += mega_active_jackpot.contributed_amount
        mega_active_jackpot.contributed_amount = 0
        mega_active_jackpot.save()

    Jackpot.objects.filter(is_active=True, jackpot_type="SUPER_JACKPOT").update(contributed_amount=F("contributed_amount") + jackpot, alltime_contributed_amount=F("alltime_contributed_amount") + jackpot)

    return "Cascaded contributed amounts"


@shared_task
def create_wema_acct_for_existing_users():
    from main.models import UserProfile, WovenAccountDetail

    all_users = UserProfile.objects.all()

    for user in all_users:
        print(user.phone_number)
        create_account = WovenAccountDetail.create_wema_account_system(phone_number=user.phone_number)
        print(create_account)

    return f"Created successfully {len(all_users)}"


@shared_task
def create_wema_collection_account(phone_number):
    from main.models import WovenAccountDetail

    WovenAccountDetail.create_wema_account_system(phone_number=phone_number)

    return "CREATED WEMA ACCOUNT SUCCESSFULLY"


# @shared_task
# def send_email(recipient: str, subject: str, template_dir: str, **substitute):  # noqa
#     TEMPLATE_DIR = os.path.join("templates", f"{template_dir}")
#     html_temp = os.path.abspath(TEMPLATE_DIR)

#     with open(html_temp) as temp_file:
#         template = temp_file.read()

#     template = Template(template).safe_substitute(substitute)

#     try:
#         response = requests.post(
#             "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
#             auth=("api", settings.MAILGUN_API_KEY),
#             data={
# <AUTHOR> <EMAIL>",
#                 "to": f"{recipient}",
#                 "subject": subject,
#                 "html": f"""{template}""",
#             },
#         )
#         # print(response.text)
#         return response.text
#     except Exception as e:
#         return str(e)


@shared_task
def move_rto_balance_to_agency_banking_rto_account():
    from main.models import PayoutTransactionTable
    from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry
    from wallet_app.models import DebitCreditRecord, RtoWallet

    return None

    rto_wallet = RtoWallet.objects.last().amount

    if rto_wallet < 30:
        return "RTO WALLET BALANCE IS LESS THAN 30"

    # check float wallet balance
    vfd_enquiries = liberty_pay_vfd_account_enquiry()

    if isinstance(vfd_enquiries, dict):
        float_available_balance = vfd_enquiries.get("available_balance", 0)

        if float_available_balance > 0:
            # get default rto wallet

            if rto_wallet > float_available_balance:
                amount_to_move = float_available_balance
            else:
                amount_to_move = rto_wallet

            # remove from rto wallet
            RtoWallet().deduct_wallet(amount=amount_to_move)

            # add to agency banking rto account
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number="*************",
                amount=amount_to_move,
                channel="REQUEST",
                reference=f"rto-{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="DEBIT",
            )

            payload = {
                "from_wallet_type": "COLLECTION",
                "to_wallet_type": "COLLECTION",
                "data": [
                    {
                        "buddy_phone_number": "*************",
                        "amount": float(amount_to_move),
                        "narration": "RTO DISBURSEMENT",
                        "is_beneficiary": "False",
                        "save_beneficiary": "True",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                    }
                ],
            }

            _withdraw_table_instance = PayoutTransactionTable.objects.create(
                source="BUDDY",
                amount=float(amount_to_move),
                disbursement_unique_id=debit_credit_record.reference,
                phone="*************",
                payout_trans_ref=debit_credit_record.reference,
                name="WHISPAKONNECT RTO ",
                channel="REQUEST",
                game_play_id=None,
                payout_payload=payload,
                balance_before=rto_wallet,
                balance_after=rto_wallet - amount_to_move,
                joined_since="UNKNOWN",
            )

            payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN

            vfd_disbursement_helper = VfdDisbursementHelperFunc()
            payout_response = vfd_disbursement_helper.liberty_agency_payout("*************", **payload)

            _withdraw_table_instance.source_response_payload = payout_response
            _withdraw_table_instance.save()

            if isinstance(payout_response, dict):
                if payout_response.get("message") == "success":
                    _withdraw_table_instance.disbursed = True
                    _withdraw_table_instance.is_verified = True
                    _withdraw_table_instance.save()


@shared_task
def celery_mtn_batch_campaign(file, sender_id, initiator_phone_number, file_type, message_body):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    io_string = io.StringIO(file)

    if file_type == "csv":
        df = pd.read_csv(io_string, dtype={"phone": str})
    elif file_type == "xlsx":
        df = pd.read_excel(io_string, dtype={"phone": str})
    else:
        return "Invalid file type"

    broad_base_helper = BBCTelcoAggregator()

    counter = 0

    for index, row in df.iterrows():
        if counter == 0:
            # send sms to the initiator
            sms_payload = {
                "phone_number": initiator_phone_number,
                "sender_name": sender_id,
                "message": "SMS CAMAPIGN, YOU'LL GET ANOTHER NOTIFICATION WHEN IT'S DONE",
            }

            counter += 1

            sms_payload["use_json_format"] = True
            broad_base_helper.bbc_send_sms(**sms_payload)

        phone_number = str(row["phone_number"]).replace(".0", "").replace(".00", "")
        # print("phone_number", phone_number)

        sms_payload = {
            "phone_number": phone_number,
            "sender_name": sender_id,
            "message": message_body,
        }

        sms_payload["use_json_format"] = True
        broad_base_helper.bbc_send_sms(**sms_payload)

    sms_payload = {
        "phone_number": initiator_phone_number,
        "sender_name": sender_id,
        "message": "SMS CAMAPIGN, DONE",
    }

    counter += 1

    sms_payload["use_json_format"] = True
    broad_base_helper.bbc_send_sms(**sms_payload)


@shared_task
def process_chunk(chunk_dict, sender_id, initiator_phone_number, message_body):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    # Convert the dictionary back to a DataFrame
    chunk = pd.DataFrame.from_dict(chunk_dict)

    broad_base_helper = BBCTelcoAggregator()

    counter = 0

    for index, row in chunk.iterrows():
        if counter == 0:
            # send sms to the initiator
            initiator_phone_number = str(initiator_phone_number).split(",")
            for in_ in initiator_phone_number:
                sms_payload = {
                    "phone_number": in_,
                    "sender_name": sender_id,
                    "message": "SMS CAMAPIGN, YOU'LL GET ANOTHER NOTIFICATION WHEN IT'S DONE",
                }

            counter += 1

            sms_payload["use_json_format"] = True
            broad_base_helper.bbc_send_sms(**sms_payload)

        phone_number = str(row["phone_number"]).replace(".0", "").replace(".00", "")
        # print("phone_number", phone_number)

        sms_payload = {
            "phone_number": phone_number,
            "sender_name": sender_id,
            "message": message_body,
        }

        sms_payload["use_json_format"] = True
        broad_base_helper.bbc_send_sms(**sms_payload)

    initiator_phone_number = str(initiator_phone_number).split(",")
    for in_ in initiator_phone_number:
        sms_payload = {
            "phone_number": in_,
            "sender_name": sender_id,
            "message": "SMS CAMAPIGN, DONE",
        }

    sms_payload["use_json_format"] = True
    broad_base_helper.bbc_send_sms(**sms_payload)


@shared_task
def celery_mtn_batch_campaign2(file, sender_id, initiator_phone_number, file_type, message_body):
    # Function to split DataFrame
    def split_dataframe(df, chunk_size):
        num_chunks = ceil(len(df) / chunk_size)
        return np.array_split(df, num_chunks)

    # Load the file into a DataFrame
    io_string = io.StringIO(file)

    if file_type == "csv":
        df = pd.read_csv(io_string, dtype={"phone": str})
    elif file_type == "xlsx":
        df = pd.read_excel(io_string, dtype={"phone": str})
    else:
        raise ValueError("Invalid file type")

    # Split DataFrame into chunks of 10,000 rows each (you can adjust the chunk size)
    chunk_size = 5000
    chunks = split_dataframe(df, chunk_size)

    # Distribute the tasks
    tasks = []
    for chunk in chunks:
        tasks.append(
            process_chunk.apply_async(
                args=(chunk.to_dict(), sender_id, initiator_phone_number, message_body),
                queue="mtn_campaign",
            )
        )

    # Optionally, wait for all tasks to complete
    for task in tasks:
        task.wait()


# @shared_task()
# def mtn_campaign_sms_trigger():
#     from broad_base_communication.bbc_helper import BBCTelcoAggregator
#     from wyse_ussd.models import TelcoUsers

#     broad_base_helper = BBCTelcoAggregator()


#     # originators_phone_number = ["07039115243", "08077469471"]
#     originators_phone_number = ["07039115243", "08038705895", "08077469471"]

#     queryset = TelcoUsers.objects.using("external").filter(network = "MTN")
#     phone_numbers = list(originators_phone_number) + list(queryset.values_list("phone_number", flat=True))


#     # read a stored csv file and get the phone numbers and check if the number you're about to send the sms to already exist there
#     csv_file = "mtn_sms_campaign_records.csv"

#     sent_numbers = set()
#     if os.path.exists(csv_file):
#         with open(csv_file, 'r') as file:
#             reader = csv.DictReader(file)
#             for row in reader:
#                 sent_numbers.add(row['phone_number'])


#     for notify in originators_phone_number:
#         sms_payload = {
#             "phone_number": notify,
#             "sender_name": "winwise",
#             "message": "campaign started",
#         }

#         broad_base_helper.bbc_send_sms(**sms_payload)


#     content = "Don't miss out! On Nov 8th, one lucky winner will secure Salary for Life with N12m! Will it be you? Stay Subscribed *20144*1*1#!"


#     file_exists = os.path.exists(csv_file)
#     with open(csv_file, 'a', newline='') as file:
#         fieldnames = ['phone_number', 'sent_date', 'campaign_content']
#         writer = csv.DictWriter(file, fieldnames=fieldnames)

#         # Write header if file is new
#         if not file_exists:
#             writer.writeheader()


#     for phone in phone_numbers:
#         if phone not in list(sent_numbers):
#             sms_payload = {
#                 "phone_number": phone,
#                 "sender_name": "winwise",
#                 "message": content
#             }

#             broad_base_helper.bbc_send_sms(**sms_payload)


#             # save the number in a csv file
#             # Record the sent SMS
#             writer.writerow({
#                 'phone_number': phone,
#                 'sent_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
#                 'campaign_content': content
#             })


#     for notify in originators_phone_number:
#         sms_payload = {
#             "phone_number": notify,
#             "sender_name": "winwise",
#             "message": "campaign ended",
#         }

#         broad_base_helper.bbc_send_sms(**sms_payload)


@shared_task()
def mtn_campaign_sms_trigger():
    import csv
    import os
    from concurrent.futures import ThreadPoolExecutor
    from datetime import datetime

    from broad_base_communication.bbc_helper import BBCTelcoAggregator
    from wyse_ussd.models import TelcoUsers

    broad_base_helper = BBCTelcoAggregator()

    originators_phone_number = ["07039115243", "08038705895", "08077469471"]
    csv_file = "mtn_sms_campaign_records.csv"

    # Read existing numbers - only once
    sent_numbers = set()
    if os.path.exists(csv_file):
        with open(csv_file, "r") as file:
            reader = csv.DictReader(file)
            sent_numbers.update(row["phone_number"] for row in reader)

    # Get all phone numbers at once
    queryset = TelcoUsers.objects.using("external2").filter(network="MTN")
    phone_numbers = list(originators_phone_number) + list(queryset.values_list("phone_number", flat=True))

    # Filter new numbers
    new_numbers = [phone for phone in phone_numbers if phone not in sent_numbers]

    # Notify campaign start
    # content = "Don't miss out! On Nov 8th, one lucky winner will secure Salary for Life with N12m! Will it be you? Stay Subscribed *20144*1*1#!"

    def send_notification(phone):
        sms_payload = {"phone_number": phone, "sender_name": "20144", "message": content}

        sleep(5)
        sms_payload["use_json_format"] = True
        return broad_base_helper.bbc_send_sms(**sms_payload)

    # Prepare CSV file
    file_exists = os.path.exists(csv_file)
    csv_file_handle = open(csv_file, "a", newline="")
    writer = csv.DictWriter(csv_file_handle, fieldnames=["phone_number", "sent_date", "campaign_content", "api_response"])
    if not file_exists:
        writer.writeheader()

    try:
        # Send initial notifications to originators
        for phone in originators_phone_number:
            sms_payload = {"phone_number": phone, "sender_name": "winwise", "message": "campaign started"}

            sms_payload["use_json_format"] = True
            broad_base_helper.bbc_send_sms(**sms_payload)

        # Process SMS sending in parallel
        with ThreadPoolExecutor(max_workers=10) as executor:
            # Send SMS in batches
            batch_size = 1000
            for i in range(0, len(new_numbers), batch_size):
                batch = new_numbers[i : i + batch_size]

                contents = [
                    "Get ready for the big reveal! On Nov 8th, we will announce our lucky winner who will be walking away with N12m as Salary for Life! Could it be you? Stay tuned and keep your fingers crossed! *20144*1*1#",
                    "The countdown is on! Just 4 days left until our Salary for Life jackpot winner is announced on Nov 8th! Will you be the one to claim N12m? Stay subscribed for more updates! *20144*1*1#",
                    "Dreaming of a life-changing sum? Don't miss your chance! Join us on Nov 8th to see if you are the lucky winner of N12m Salary for Life! Stay connected and be part of this exciting moment! *20144*1*1#",
                    "Are you ready to secure your future? Our Salary for Life winner will be revealed on Nov 8th! One person will win N12m! Don't miss out—stay subscribed for all the latest news! *20144*1*1#",
                ]

                content = random.choice(contents)

                # Send SMS in parallel
                futures = [executor.submit(send_notification, phone) for phone in batch]

                # Record successful sends
                for phone, future in zip(batch, futures):
                    try:
                        result = future.result()
                        # Record only if SMS was sent successfully
                        writer.writerow(
                            {
                                "phone_number": phone,
                                "sent_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                "campaign_content": content,
                                "api_response": str(result),  # Raw API response
                            }
                        )
                    except Exception as e:
                        print(f"Failed to send SMS to {phone}: {str(e)}")

        # Send completion notifications
        for phone in originators_phone_number:
            sms_payload = {
                "phone_number": phone,
                "sender_name": "winwise",
                "message": "campaign ended",
            }

            sms_payload["use_json_format"] = True
            broad_base_helper.bbc_send_sms(**sms_payload)

    finally:
        csv_file_handle.close()

    return f"Campaign completed. Processed {len(new_numbers)} new numbers."


@shared_task
def temp_task_for_data_extraction():
    import pandas as pd

    from wallet_app.models import WalletTransaction

    # Define the chunk size
    chunk_size = 10000  # Adjust this size based on your memory capacity
    offset = 0
    total_records = WalletTransaction.objects.count()

    # Create a Pandas Excel writer using XlsxWriter as the engine
    with pd.ExcelWriter("static/wallet_transactions.xlsx", engine="openpyxl") as writer:
        while offset < total_records:
            # Fetch a chunk of data
            transactions_chunk = WalletTransaction.objects.all()[offset : offset + chunk_size]

            # Convert the queryset to a DataFrame
            df = pd.DataFrame(list(transactions_chunk.values()))

            # Convert timezone-aware datetime columns to timezone-unaware
            for col in df.select_dtypes(include=["datetime64[ns, UTC]", "datetime64[ns]"]):
                df[col] = df[col].dt.tz_localize(None)  # Remove timezone information

            # Write the DataFrame to the Excel file
            df.to_excel(writer, sheet_name=f"Transactions_{offset // chunk_size + 1}", index=False)

            # Increment the offset
            offset += chunk_size

    print("Data export completed successfully!")


@shared_task
def send_email(  # noqa
    email: str,
    subject: str,
    template_dir: str,
    use_template=True,
    body=None,
    **substitute,
):

    if use_template:
        TEMPLATE_DIR = os.path.join("templates", f"{template_dir}")
        print(TEMPLATE_DIR)
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitute)
    else:
        template = None

    response = requests.post(
        "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
        auth=("api", f"{settings.MAILGUN_API_KEY}"),
        data={
            "from": "Whysecash <<EMAIL>>",
            "to": f"{email}",
            "subject": f"{subject}",
            "html": f"""{template}""" if use_template else None,
            "text": body if not use_template else None,
        },
    )

    if response.status_code == 200 or response.status_code == 201:
        print(f"MAILGUN RESPONSE: {response.json()}")
        print(f"MAILGUN RESPONSE: {response.text}")
        return "EMAIL SENT"
    else:
        print(f"MAILGUN FAILED WITH RESPONSE >>> {response.status_code} : {response.text}")
        return "EMAIL FAILED!"


@shared_task
def kill_pending_digital_ocean_droplets():
    """
    Kill digital ocean droplets that are yet
    to be killed.
    """

    from main.models import DigitalOceanDroplet

    # Fetch droplets older than 1 day but still alive
    _1_day_ago = timezone.now() - timedelta(days=1)

    pending_droplets = DigitalOceanDroplet.objects.filter(created_at__lte=_1_day_ago, is_killed=False)

    for droplet in pending_droplets:
        # Killed pending droplet
        DigitalOceanDroplet.kill_digital_ocean_droplet(droplet_id=droplet.id)

    return "Lingering droplets killed"


@shared_task
def seed_global_tickets():
    return
    from datetime import datetime

    from django.utils import timezone

    from main.models import LottoTicket

    from .seeder import SeederService

    try:
        logger.info("Seed Global Tickets Task is running")
        seeder: SeederService = SeederService("GLOBAL", "INSTANT_GAMES")
        if seeder.agent_constant.rapid_fire_only or seeder._check_rapid_fire_without_seeder():
            seeder_instance = seeder.rapid_fire_instance
            logger.info(f"Seeder instance for rapid fire only: {seeder_instance}")
        else:
            seeder_instance = seeder.seeder_instance
            logger.info(f"Seeder instance: {seeder_instance}")

        if seeder_instance:
            start_datetime = timezone.make_aware(datetime.combine(seeder_instance.date, seeder_instance.seed_time))
            lotto_tickets = LottoTicket.objects.filter(
                date__range=[start_datetime, timezone.now()],
                played_via_telco_channel=False,
                seeder_status="PENDING",
                lottery_type__in=["INSTANT_CASHOUT", "QUIKA", "VIRTUAL_SOCCER"],
                drawn_for="GLOBAL",
                paid=True,
            )
            if lotto_tickets.exists():
                # lotto_tickets.update(seeder_status="PROCESSING")
                for ticket in lotto_tickets:
                    logger.info(f"Processed ticket ID: {ticket.id}")
                    seeder.calculate_effective_rtp_for_ticket(ticket)
                    logger.info(f"Processed ticket ID: {ticket.id}")
            else:
                logger.info(f"No {seeder.game_type} tickets found for GLOBAL level.")
        else:
            logger.info(f"No {seeder.game_type} seeder instance found for GLOBAL level.")
    except Exception as e:
        logger.exception("An error occurred while seeding global tickets: %s", e)


@shared_task
def seed_global_banker_tickets():
    return
    from datetime import datetime

    from django.utils import timezone

    from main.models import LottoTicket

    from .seeder import SeederService

    try:
        logger.info("Seed Global Tickets Task is running")
        seeder = SeederService("GLOBAL", "BANKER")
        if seeder.seeder_instance:
            start_datetime = timezone.make_aware(datetime.combine(seeder.seeder_instance.date, seeder.seeder_instance.seed_time))
            lotto_tickets = LottoTicket.objects.filter(
                date__range=[start_datetime, timezone.now()],
                played_via_telco_channel=False,
                seeder_status="PENDING",
                lottery_type__in=["BANKER"],
                drawn_for="GLOBAL",
                paid=True,
            )
            if lotto_tickets.exists():
                # lotto_tickets.update(seeder_status="PROCESSING")
                for ticket in lotto_tickets:
                    logger.info(f"Processed ticket ID: {ticket.id}")
                    seeder.calculate_effective_rtp_for_ticket(ticket)
                    logger.info(f"Processed ticket ID: {ticket.id}")
            else:
                logger.info(f"No {seeder.game_type} tickets found for GLOBAL level.")
        else:
            logger.info(f"No {seeder.game_type} seeder instance found for GLOBAL level.")
    except Exception as e:
        logger.exception("An error occurred while seeding global tickets: %s", e)


@shared_task
def seed_global_kenya_tickets():
    return
    from datetime import datetime

    from django.utils import timezone

    from africa_lotto.models import AfricaLotto

    from .seeder import SeederService

    try:
        logger.info("Seed Global Tickets Task is running")
        seeder = SeederService("GLOBAL", "KENYA")
        if seeder.seeder_instance:
            start_datetime = timezone.make_aware(datetime.combine(seeder.seeder_instance.date, seeder.seeder_instance.seed_time))
            africa_lotto_tickets = AfricaLotto.objects.filter(
                game_type__in=["KENYA_LOTTO"],
                created_at__range=[start_datetime, timezone.now()],
                drawn_for="GLOBAL",
                seeder_status="PENDING",
                paid=True,
            )
            if africa_lotto_tickets.exists():
                for ticket in africa_lotto_tickets:
                    logger.info(f"Processed ticket ID: {ticket.id}")
                    seeder.calculate_effective_rtp_for_ticket(ticket)
                    logger.info(f"Processed ticket ID: {ticket.id}")
            else:
                logger.info(f"No {seeder.game_type} tickets found for GLOBAL level.")
        else:
            logger.info(f"No {seeder.game_type} seeder instance found for GLOBAL level.")
    except Exception as e:
        logger.exception("An error occurred while seeding global tickets: %s", e)


@shared_task
def seed_global_kenya_30_tickets():
    return
    from datetime import datetime

    from django.utils import timezone

    from africa_lotto.models import AfricaLotto

    from .seeder import SeederService

    try:
        logger.info("Seed Global Tickets Task is running")
        seeder = SeederService("GLOBAL", "KENYA_30")
        if seeder.seeder_instance:
            start_datetime = timezone.make_aware(datetime.combine(seeder.seeder_instance.date, seeder.seeder_instance.seed_time))
            africa_lotto_tickets = AfricaLotto.objects.filter(
                game_type__in=["KENYA_30_LOTTO"],
                created_at__range=[start_datetime, timezone.now()],
                drawn_for="GLOBAL",
                seeder_status="PENDING",
                paid=True,
            )
            if africa_lotto_tickets.exists():
                for ticket in africa_lotto_tickets:
                    logger.info(f"Processed ticket ID: {ticket.id}")
                    seeder.calculate_effective_rtp_for_ticket(ticket)
                    logger.info(f"Processed ticket ID: {ticket.id}")
            else:
                logger.info(f"No {seeder.game_type} tickets found for GLOBAL level.")
        else:
            logger.info(f"No {seeder.game_type} seeder instance found for GLOBAL level.")
    except Exception as e:
        logger.exception("An error occurred while seeding global tickets: %s", e)


# @shared_task
# def seed_local_tickets():
#     from main.models import LottoTicket
#     from .seeder import SeederService

#     seeder = SeederService("LOCAL")
#     start_datetime = timezone.make_aware(
#         datetime.combine(seeder.seeder_instance.date, seeder.seeder_instance.seed_time)
#     )
#     lotto_tickets = LottoTicket.objects.filter(
#         date__range=[start_datetime, timezone.now()],
#         seeder_status="PENDING",
#         played_via_telco_channel=False,
#         lottery_type__in=["INSTANT_CASHOUT", "QUIKA", "VIRTUAL_SOCCER"],
#         drawn_for="LOCAL"
#     )
#     lotto_tickets.update(seeder_status="PROCESSING")
#     for ticket in lotto_tickets:
#         seeder.calculate_effective_rtp_for_ticket(ticket)


@shared_task
def dispatch_icash_draw():

    import time

    from main.models import LottoTicket

    print("DISPATCHING TICKETS")

    two_minutes_ago = timezone.now() - timedelta(hours=15)
    ticket_ids = LottoTicket.objects.filter(
        paid=True,
        played_via_telco_channel=False,
        lottery_type__in=["INSTANT_CASHOUT", "QUIKA"],
        icash_2_counted=False,
        icash_counted=False,
        instant_cashout_drawn=False,
        date__gte=two_minutes_ago,
        # seeder_status="COMPLETE",
    ).values_list("id", flat=True)

    print(ticket_ids)
    ticket_ids = list(ticket_ids)
    print(ticket_ids)
    LottoTicket.objects.filter(id__in=ticket_ids).update(
        icash_2_counted=True,
        icash_counted=True,
        instant_cashout_drawn=True,
    )
    print(ticket_ids)
    for ticket_id in ticket_ids:
        time.sleep(2)
        print(f"Dispatching ticket ID: {ticket_id}")
        quika_new_icash_local.apply_async(args=(ticket_id,), queue="icash_new")


