﻿<div id="top"></div>
<br />
<div align="center">
    <a href="https://github.com/libertytechx">
        <img src="images/logo.jpeg" alt="Logo" width="80" height="80">
    </a>
    <h3 align="center"># LIBERTY LOTTO (WIN WISE)</h3>
</div>
<br />

<details>
  <summary>Table of Contents</summary>
  <ol>
    <li>
      <a href="#built-with">About The Project</a>
    </li>
    <li><a href="#usage">Usage</a></li>
    <li><a href="#contributing">Contributing</a></li>
    <li><a href="#license">License</a></li>
  </ol>
</details>

# About The Project
If this's your first time here, welcome to Libertytechx. Looking forward to working with you. <br /><br />
Win Wise is a lottery game that allows you to win any amount. They're different types of games and majorify of them are number base game. e.g. picky 6 digit out of random numbers in range of 1,49.
This's a rough sketch explanation of how the game works. The game is a bit more complicated than that.

THIS CODEBASE IS THE BACKEND OF THE GAME. THE FRONTEND IS NOT INCLUDED IN THIS REPOSITORY.
<br /> <br />


# Usage
<ul>
    <li> USSD (*347*800#)</li>
    <li>Web link: https://www.wisewinn.com/</li>
    <li>Android App: https://play.google.com/store/apps/details?id=com.libertytech.org.libertpay</li>
    <li>iSO App: https://apps.apple.com/ng/app/liberty-pay-agency/id1637666052</li>
</ul>
<br /><br />
# Contributing
<br /><br />


1. clone the Project
2. Create and activate virtualenv for this project.

```python
    python -m venv venv
```
```python
    source venv/bin/activate
```
```python
    pip install -r requirements.txt
```
3. Create your Feature Branch (`git checkout -b feature/<your branch name>`)
4.  Install project dependencies:
    ```python pip install -r requirements.txt ```
5.  Install redis. https://github.com/microsoftarchive/redis/releases
to know more about how to use redis or how we use redis on this project, check this repo
``` https://github.com/Josephchinedu/redis_storage ```
6.  Setup postgres database, makemigrations
```python
    python manage.py makemigrations account main ads_tracker pos_app referral_system referral_system sport_app wallet_app web_app wyse_ussd awoof_app banker_lottery resources_app sms_campaign ticket_price
```
7. Migrate
```python
    python manage.py migrate
```
8.  You can now run the development server:
    ```python python manage.py runserver ```
9.  Write test for your feature
10.  Push to your Branch
11.  Open a Pull Request to Development Branch (active_dev)<br /><br />
# Celery
``` celery -A liberty_lotto.celery worker --loglevel=INFO --concurrency 1 -P solo ```
<br /><br />

``` celery -A liberty_lotto.celery worker -Q icash_new -n icash_new --loglevel=INFO --concurrency 1 -P solo ```
<br /><br />
# Celery Beat
Use this to start celery beat from database schedule<br />
``` celery -A liberty_lotto beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler ```
<br /><br />
uvicorn logs
tail -n 50 -f /var/log/uvicorn.log

tail -n 50 -f /var/log/uvicorn_error.log
<br /><br />
# CHECK CELERY TASK RESULT


```python
from celery.result import AsyncResult

task_id = 'your_task_id'
result = AsyncResult(task_id)
print(result.status)

print(result.get())


# asgi
daphne -p 8001 liberty_lotto.asgi:application


https://punchng.com/glo-subscriber-wins-n12million-with-n50-in-glo-winwise-salary4life-using-the-code-2014431/


```
### Developing With Docker
- Copy the `sample.env.txt` to `.env`
  ```sh
    cp sample.env.txt .env
  ```
Or do this
- Update the database credentials with the following
  ```txt
      - POSTGRES_DB=devdb
      - POSTGRES_USER=devuser
      - POSTGRES_PASSWORD=changeme
      - POSTGRES_HOST=corebanking_db
  ```
- Update the `REDIS_HOST` value to `lotto_redis`, like so
  ```
  REDIS_HOST=lotto_redis
  ```
- Spin up your docker container
  - Build the main app first
  ```sh
  docker-compose build app
  ```
  - Run up to start all other services
  ```sh
  docker-compose up
  ```
- Steps to debug if encounter any issue while spinning up the containers
  - verify if `celery`, `celery-beat`, `celery_icash` services in the `docker-compose.yml` has the correct image name
  - run `docker images` to check the lotto_app image name like so
    ```
    (lottovenv) λ MACs-MBP LibertyLotto → λ git ayo/dev* → docker images
    REPOSITORY                                        TAG         IMAGE ID       CREATED             SIZE
    libertylotto-app                                  latest      b9a60584a301   28 minutes ago   1.47GB
    ```
  - if `libertylotto-app` not same as the `image: libertylotto-app` update accordingly and restart up command `docker-compose up`
  Note: This is just reusability here, reusing the main app image for the celery services so we won't have too many redundant image eating up space on our PC.

#### Steps to create a superuser from your container
- Run `docker ps` to check your main app container name
    Mine is `lotto_app` as will yours
    ```sh
        (lottovenv) λ MACs-MBP LibertyLotto → λ git ayo/dev* → docker ps
        CONTAINER ID   IMAGE                COMMAND                   CREATED          STATUS          PORTS                    NAMES
        f650b842c7b7   libertylotto-app     "sh -c '\n      pytho…"   32 minutes ago   Up 13 minutes   0.0.0.0:8000->8000/tcp   lotto_app
    ```
- Run the following command, to start an interactive shell in the container
```sh
docker exec -it lotto_app sh
```
```sh
(lottovenv) λ MACs-MBP LibertyLotto → λ git ayo/dev* → docker exec -it lotto_app sh
/app #
```
- Run the django create superuser command, like so
```sh
/app # python manage.py createsuperuser
Username: ayo
Password:
Password (again):
Superuser created successfully.
```
Boom!, There you have it, you can now start developing with docker

If encounter any Issue and the steps to resolve not here, while setting up the docker environment on your PC, please create an Issue here with probably a tag `chore` and assign to `Ayobami6`. Thank you

<br /><br />
# Testing

## S4L Draw Tests

The `s4l_draw` module includes functionality for lottery draws, and we have created a test suite to ensure its correctness. The test file is located at `flask_app/test_s4l_draw.py`.

### What It Tests
- **`search_number_occurences`**: Verifies that the function correctly counts the number of matches, including multiple occurrences of numbers.
- **`filter_winnings`**: Ensures that the correct winners and winnings are calculated based on the provided combinations and plays.
- **`play`**: Tests the processing of combinations to calculate winnings for each.

### How to Run the Tests
To run the tests, use the following command:
```bash
python -m unittest -v flask_app/test_s4l_draw.py
```
This will execute all the test cases and provide detailed output for each test, including whether it passed or failed.
