import requests
from django.conf import settings


class PaymentGateway:
    """PAYSTACK API GATEWAY"""

    def __init__(self):
        self.base_url = "https://api.paystack.co"
        authorization = f"Bearer {settings.PAYSTACK_BEARER}"
        self.headers = {"Authorization": authorization}

    def paystack_link_request(self, **kwargs):
        base_url = f"{self.base_url}/transaction/initialize/"

        response = requests.post(base_url, json=kwargs, headers=self.headers)
        data = response.json()

        return data.get("data", {"authorization_url": ""})

    def paystack_verify_payment(self, reference):
        base_url = f"{self.base_url}/transaction/verify/{reference}"

        response = requests.get(base_url, headers=self.headers)
        return response.json()

    def fetch_account_name(self, account_number, bank_code):
        """ "
        This function calls another function that triggers a provider to fetch accunt name
        """

        url = f"{self.base_url}/bank/resolve?account_number={account_number}&bank_code={bank_code}"

        headers = self.headers

        response = requests.request("GET", url=url, headers=headers)
        res = response.json()

        print("paystack account number verification: ", {response.text})
        return res

    def charge_reusable_card(self, **kwargs):
        """
        This function charges a reusable card

        kwargs = {
            "amount": 10000,
            "email": "example@<EMAIL>",
            "authorization_code": "pays_**********", # This is the authorization code from the reusable card
            "reference": "**********"
        }
        """

        url = f"{self.base_url}/transaction/charge_authorization"

        headers = self.headers

        response = requests.request("POST", url=url, headers=headers, json=kwargs)
        res = response.json()

        return res

    @staticmethod
    def charges_on_amount_per_trnx(amount):
        if str(amount) == "":
            return 0

        else:
            value = float(amount)
            if value < 2500:
                # 1.5 percent charge of amount
                percent_charge = (1.5 * value) / 100
                return percent_charge

            elif value >= 2500:
                # 1.5 percent charge + 100
                percent_charge = (1.5 * value) / 100
                fee = percent_charge + 100

                if fee > 2000:
                    return 2000
                else:
                    return fee
