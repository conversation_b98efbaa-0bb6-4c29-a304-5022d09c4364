import requests
from celery import shared_task
from django.conf import settings

from main.helpers.woven_manager import generate_woven_collection_account_number
from main.models import UserProfile, WovenAccountDetail
from main.ussd.helpers import Utility
from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry
from wallet_app.models import FloatWallet, UserWallet


@shared_task
def create_woven_virtual_wallet(user_id):
    """
    Create a virtual wallet for a user.
    Args: user instance
    """

    return
    user = UserProfile.objects.filter(id=user_id).last()

    if user is None:
        return None

    if str(user.phone_number).isdigit() is False:
        return None

    # web virtual account
    db_woven_acct_details = WovenAccountDetail.objects.filter(phone_number=user.phone_number, wallet_tag="WEB", is_active=True).last()
    get_user_wallet = UserWallet.objects.filter(user=user, wallet_tag="WEB").last()

    if db_woven_acct_details:
        if not get_user_wallet:
            UserWallet.objects.create(
                user=user,
                woven_account=db_woven_acct_details,
                account_ref=db_woven_acct_details.account_ref,
                wallet_tag="WEB",
            )

            user.has_web_virtual_account = True
            user.save()
        else:
            # check if account details  for the user wallet exist
            if get_user_wallet.woven_account is None and get_user_wallet.account_ref is None:
                get_user_wallet.woven_account = db_woven_acct_details
                get_user_wallet.account_ref = db_woven_acct_details.account_ref
                get_user_wallet.save()

                user.has_web_virtual_account = True
                user.save()
    else:
        # call woven virtual wallet api to create a virtual wallet
        woven_virtual_acct_response = generate_woven_collection_account_number(user.phone_number, func_count=3, tag="WEB")
        if woven_virtual_acct_response is not None:
            new_woven_virtual_account = WovenAccountDetail.objects.filter(phone_number=user.phone_number, wallet_tag="WEB").last()

            if get_user_wallet is None:
                UserWallet.objects.create(
                    user=user,
                    woven_account=new_woven_virtual_account,
                    account_ref=woven_virtual_acct_response["account_reference"],
                    wallet_tag="WEB",
                )
                user.has_web_virtual_account = True
                user.save()
            else:
                if get_user_wallet.woven_account is None and get_user_wallet.account_ref is None:
                    get_user_wallet.woven_account = new_woven_virtual_account
                    get_user_wallet.account_ref = new_woven_virtual_account.account_ref
                    get_user_wallet.save()

                    user.has_web_virtual_account = True
                    user.save()

    db_woven_acct_details = WovenAccountDetail.objects.filter(phone_number=user.phone_number, wallet_tag="USSD", is_active=True).last()
    get_user_wallet = UserWallet.objects.filter(user=user, wallet_tag="USSD").last()

    if db_woven_acct_details:
        if not get_user_wallet:
            UserWallet.objects.create(
                user=user,
                woven_account=db_woven_acct_details,
                account_ref=db_woven_acct_details.account_ref,
                wallet_tag="USSD",
            )
        else:
            # check if account details  for the user wallet exist
            if get_user_wallet.woven_account is None and get_user_wallet.account_ref is None:
                get_user_wallet.woven_account = db_woven_acct_details
                get_user_wallet.account_ref = db_woven_acct_details.account_ref
                get_user_wallet.save()

    else:
        # call woven virtual wallet api to create a virtual wallet
        woven_virtual_acct_response = generate_woven_collection_account_number(user.phone_number, func_count=3, tag="USSD")
        if woven_virtual_acct_response is not None:
            new_woven_virtual_account = WovenAccountDetail.objects.filter(phone_number=user.phone_number, wallet_tag="USSD").last()
            UserWallet.objects.create(
                user=user,
                woven_account=new_woven_virtual_account,
                account_ref=woven_virtual_acct_response["account_reference"],
                wallet_tag="USSD",
            )


@shared_task
def celery_update_float_wallet():
    """
    Update float wallet. This will make enquiries to the float wallet. VFD AND WOVEN
    """
    # VFD enquiries
    vfd_enquiries = liberty_pay_vfd_account_enquiry()

    vfd_float_wallet_instance = FloatWallet().get_float_wallet("AGENT_FUNDING_WALLET")

    if isinstance(vfd_enquiries, dict):
        vfd_enquiries = vfd_enquiries.get("available_balance", 0)
        vfd_float_wallet_instance.amount = vfd_enquiries
        vfd_float_wallet_instance.save()

    vfd_float_wallet_instance = FloatWallet().get_float_wallet("NON_RETAIL_WALLET")
    vfd_enquiries = liberty_pay_vfd_account_enquiry(source="NON_RETAIL_WALLET")
    if isinstance(vfd_enquiries, dict):
        vfd_enquiries = vfd_enquiries.get("available_balance", 0)
        vfd_float_wallet_instance.amount = vfd_enquiries
        vfd_float_wallet_instance.save()

    vfd_float_wallet_instance = FloatWallet().get_float_wallet("RETAIL_RTP_WALLET")
    vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_RTP_WALLET")
    if isinstance(vfd_enquiries, dict):
        vfd_enquiries = vfd_enquiries.get("available_balance", 0)
        vfd_float_wallet_instance.amount = vfd_enquiries
        vfd_float_wallet_instance.save()

    vfd_float_wallet_instance = FloatWallet().get_float_wallet("GHANA_RTP_WALLET")
    vfd_enquiries = liberty_pay_vfd_account_enquiry(source="GHANA_RTP_WALLET")
    if isinstance(vfd_enquiries, dict):
        vfd_enquiries = vfd_enquiries.get("available_balance", 0)
        vfd_float_wallet_instance.amount = vfd_enquiries
        vfd_float_wallet_instance.save()

    vfd_float_wallet_instance = FloatWallet().get_float_wallet("RTO_WALLET")
    vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RTO_WALLET")
    if isinstance(vfd_enquiries, dict):
        vfd_enquiries = vfd_enquiries.get("available_balance", 0)
        vfd_float_wallet_instance.amount = vfd_enquiries
        vfd_float_wallet_instance.save()

    return "Float wallet updated successfully"


@shared_task
def notify_admin_of_user_funding(amount, phone_number, channel):
    if settings.DEBUG is True or settings.DEBUG:
        return None

    whatsapp_url = "https://pickyassist.com/app/api/v2/push"
    whatsapp_payload = {
        "token": f"{settings.PICKY_ASSIST_TOKEN}",
        "group_id": "120363043759006323",
        "application": "10",
        "mention_numbers": [
            "*************",
            "*************",
            "*************",
            "*************",
        ],
        "globalmessage": f"@*************, @*************, @*************, @*************, Dear Admins,\n\n---------------------------------------- \n\nA player with phone_number {phone_number} has funded his player wallet with *{Utility.currency_formatter(amount=amount)}* via {channel}  \n\nThanks.\n\n--------------------------------------------------------",
    }

    whatsapp_Headers = {"Content-type": "application/json"}

    whatsapp_response = requests.post(whatsapp_url, json=whatsapp_payload, headers=whatsapp_Headers)

    return f"Whatsapp Message sent to admin on user funding. Response: {whatsapp_response.text}"
