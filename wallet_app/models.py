import json
import random
import time
import uuid
from collections import OrderedDict
from datetime import datetime

import pytz
from django.conf import settings
from django.db import models, transaction
from django.db.models.signals import post_save
from django.utils import timezone

from main.api.api_lottery_helpers import <PERSON><PERSON><PERSON>
from main.helpers.redis_storage import RedisStorage
from main.models import (
    LotteryBatch,
    LotteryGlobalJackPot,
    LotteryModel,
    LottoTicket,
    UserProfile,
    WovenAccountDetail,
)
from main.tasks import (
    celery_tokenize_paystack_card,
    create_wema_collection_account,
    lottery_play_engange_event,
)
from overide_print import print
from pos_app.models import (
    Agent,
    AgentWallet,
    AgentWalletTransaction,
    GamesDailyActivities,
    LottoAgentRemittanceTable,
    LottoAgentSalesActivity,
)
from referral_system.models import ReferralTransaction
from wallet_app.helpers.helper_functions import standard_str_to_dt
from wallet_app.helpers.payment_gateway import PaymentGateway
from wyse_ussd.models import UssdConstantVariable, UssdLotteryPayment


# Create your model(s) here.
class UserWallet(models.Model):
    CURRENCY_TYPE_CHOICES = [
        ("NGN", "Naira"),
        ("USD", "US Dollar"),
    ]

    WALLET_TAG = [
        ("USSD", "USSD"),
        ("WEB", "WEB"),
    ]

    user = models.ForeignKey(UserProfile, related_name="wallet", on_delete=models.CASCADE)
    woven_account = models.ForeignKey(
        WovenAccountDetail,
        related_name="user_wallet",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    wema_account = models.ForeignKey(
        WovenAccountDetail,
        related_name="wema_user_wallet",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    account_ref = models.CharField(max_length=125, null=True, blank=True)
    wema_account_ref = models.CharField(max_length=125, null=True, blank=True)
    withdrawable_available_balance = models.FloatField(default=0)
    game_available_balance = models.FloatField(default=0)
    telco_wallet_balance = models.FloatField(default=0)
    airtime_wallet_balance = models.FloatField(default=0)
    currency = models.CharField(max_length=3, default="NGN", choices=CURRENCY_TYPE_CHOICES)
    fund_wallet_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    wallet_tag = models.CharField(max_length=5, default="USSD", choices=WALLET_TAG)
    transaction_from = models.CharField(max_length=30, default="Nil", blank=True, null=True)

    # def __init__(self, *args, **kwargs):
    #     super(UserWallet, self).__init__(*args, **kwargs)
    #     self.__original_fund_wallet_count = self.fund_wallet_count
    #     self.__original_withdrawable_ava_balance = self.withdrawable_available_balance
    #     self.__original_game_ava_bal = self.game_available_balance

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "USER WALLET"
        verbose_name_plural = "USER WALLETS"

    def __str__(self) -> str:
        return self.user.__str__()

    # adding this to aid search using phone number
    @property
    def phone_number(self):
        return self.user.phone_number

    def save(self, *args, **kwargs):
        self.withdrawable_available_balance = round(self.withdrawable_available_balance, 2)
        self.game_available_balance = round(self.game_available_balance, 2)

        try:
            return super(UserWallet, self).save(*args, **kwargs)
        except Exception as err:
            print(err)
            # details = f"ALERTTTTTTTTTTT!!\n\nALERTTTTTTTTTTT!!\nUser with email: {self.user.email} tried to save balance in a negative figure and has been suspended."

            # self.user.is_suspended = True
            # self.user.suspension_reason = details
            # self.user.save()

            # notify_admin_group(user=self.user, details=details)

            raise Exception(f"{err}")

    @staticmethod
    def fund_wallet(
        user,
        amount,
        channel,
        transaction_id,
        user_wallet_type,
        from_telco_channel=False,
        **kwargs,
    ):
        """
        USER WALLET TYPES:
        FOR AGENT:
            1. COMMISSION_WALLET
            2. BONUS_WALLET
            3. WINNINGS_WALLET
            4. GAME_PLAY_WALLET

        FOR USER:
            5. GAME_PLAY_WALLET
            6. WINNINGS_WALLET


        CHANNELS:
            1. WEB
            2. POS

        """

        print(
            f"""
        transaction_id: {transaction_id}
        \n\n\n
        """
        )
        print("IN FUNCTION >>> ")
        try:
            with transaction.atomic():
                if channel == "WEB":
                    wallet = UserWallet.objects.filter(user=user, wallet_tag="WEB").first()
                    if wallet is None:
                        wallet = UserWallet.objects.create(user=user, wallet_tag="WEB")
                elif channel == "POS":
                    wallet = AgentWallet.objects.filter(agent=user).first()
                    if wallet is None:
                        wallet = AgentWallet.objects.create(
                            agent=user,
                            agent_name=user.first_name + " " + user.last_name,
                            agent_email=user.email,
                            agent_phone_number=user.phone,
                        )
                else:
                    wallet = None

                transaction_from = kwargs.get("transaction_from", None)

                if wallet:
                    # AGENT WALLET
                    if isinstance(wallet, AgentWallet):
                        if user_wallet_type == "COMMISSION_WALLET":
                            look_out_balance = wallet.commission_bal
                            # transaction_from = "COMMISSION"

                            balance_before = look_out_balance
                            balance_after = balance_before + float(amount)

                            wallet.commission_bal = balance_after

                        elif user_wallet_type == "BONUS_WALLET":
                            look_out_balance = wallet.bonus_bal

                            # transaction_from = "BONUS"

                            balance_before = look_out_balance
                            balance_after = balance_before + float(amount)

                            wallet.bonus_bal = balance_after

                        elif user_wallet_type == "WINNINGS_WALLET":
                            # handle case where user is on recovery debt model
                            if DebtRecovery.get_debt_recovery_instance(phone=wallet.agent.phone) is not None:
                                DebtRecovery.update_debt_recovery_insatnce(phone=wallet.agent.phone, amount=amount, channel="MOBILE")
                                return

                            look_out_balance = wallet.winnings_bal

                            # transaction_from = "WINNINGS"

                            balance_before = look_out_balance
                            balance_after = balance_before + float(amount)

                            wallet.winnings_bal = balance_after

                        elif user_wallet_type == "GAME_PLAY_WALLET":
                            look_out_balance = wallet.game_play_bal
                            # transaction_from = "GAME_PLAY"

                            balance_before = look_out_balance
                            balance_after = balance_before + float(amount)

                            wallet.game_play_bal = balance_after
                            wallet.fund_wallet_count += 1

                        elif user_wallet_type == "REMUNERATION_WALLET":
                            look_out_balance = wallet.remuneration_bal
                            # transaction_from = "GAME_PLAY"

                            balance_before = look_out_balance
                            balance_after = balance_before + float(amount)

                            wallet.remuneration_bal = balance_after

                        else:
                            DebitCreditRecord.objects.filter(reference=transaction_id).update(status="FAILED")
                            DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="FAILED")

                            return {
                                "balance_before": 0,
                                "balance_after": 0,
                                "record": None,
                                "wallet_instance": None,
                            }

                        wallet.save()

                        game_type = kwargs.get("game_type", None)
                        game_play_id = kwargs.get("game_play_id", None)
                        rewarded_commission = wallet.commission_rewarded

                        game_play_balance_plus_winning = wallet.winnings_bal + wallet.game_play_bal

                        expected_remittance = 0
                        excess_amount = 0

                        if user_wallet_type == "GAME_PLAY_WALLET":
                            agent_remittance_update = LottoAgentRemittanceTable.deduct_remittance(agent=wallet.agent, amount=amount)

                            # expected_remittance =

                            if agent_remittance_update is None:
                                expected_remittance = 0
                                excess_amount = 0

                            elif agent_remittance_update.get("has_excess") is False:
                                expected_remittance = agent_remittance_update.get("expected_remittance")
                                excess_amount = agent_remittance_update.get("excess_amount")

                                transaction_from = "REMITTANCE"

                            else:
                                expected_remittance = agent_remittance_update.get("expected_remittance")
                                excess_amount = agent_remittance_update.get("excess_amount")

                                transaction_from = "REMITTANCE"

                                if agent_remittance_update.get("has_remittance") is False:
                                    transaction_from = "REMITTANCE_EXCESS"

                                # else:
                                #     transaction_from = "REMITTANCE_EXCESS"
                                # # transaction_from = "REMITTANCE_EXCESS"
                                # transaction_from = "REMITTANCE"

                        type_of_agent = "OTHER_AGENT"
                        if wallet.agent.is_winwise_staff_agent is True:
                            type_of_agent = "WINWISE_AGENT"

                        # print("transaction_from :: ", transaction_from)
                        # check if game_play_id is none, if it's not none, check if the game id and the transaction from have been recorded for this agent

                        # if game_play_id is not None:
                        #     if AgentWalletTransaction.objects.filter(
                        #         agent_wallet=wallet,
                        #         game_play_id=game_play_id,
                        #         game_type=game_type,
                        #         transaction_from=transaction_from,
                        #     ).exists():
                        #         raise Exception(
                        #             f"Transaction with game_play_id {game_play_id} and transaction_from {transaction_from} already exists for this agent wallet."
                        #         )




                        record = AgentWalletTransaction.objects.create(
                            transaction_reference=transaction_id,
                            unique_transaction_reference=transaction_id,
                            agent_wallet=wallet,
                            transaction_type="CREDIT",
                            amount=amount,
                            status="SUCCESSFUL",
                            transaction_from=transaction_from,
                            bal_before=balance_before,
                            bal_after=balance_after,
                            excess_balance=excess_amount,
                            game_type=game_type,
                            game_play_id=game_play_id,
                            game_play_bal_plus_winnings=game_play_balance_plus_winning,
                            rewarded_commission=rewarded_commission,
                            phone_number=wallet.agent.phone,
                            expected_remittance=expected_remittance,
                            type_of_agent=type_of_agent,
                            type_of_user=wallet.agent.agent_type,
                            agent_name=wallet.agent.first_name + " " + wallet.agent.last_name,
                            agent_phone_number=wallet.agent.phone,
                            agent_email=wallet.agent.email,
                        )

                        # print("record", record)
                        # print("record.id", record.id)

                        if transaction_from == "PRE_FUNDING":
                            agent = Agent.objects.get(id=user.id)
                            agent.has_pre_funding = True
                            agent.save()

                        DebitCreditRecord.objects.filter(reference=transaction_id).update(status="SUCCESS")
                        DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="SUCCESS")

                        

                            

                    elif isinstance(wallet, UserWallet):
                        print("user_wallet_type :: ", user_wallet_type)
                        print("from_telco_channel :: ", from_telco_channel)
                        if user_wallet_type == "GAME_PLAY_WALLET":
                            if from_telco_channel is False:
                                look_out_balance = wallet.game_available_balance

                                balance_before = look_out_balance
                                balance_after = balance_before + float(amount)

                                wallet.game_available_balance = balance_after
                                wallet.fund_wallet_count += 1
                            else:
                                look_out_balance = wallet.telco_wallet_balance

                                balance_before = look_out_balance
                                balance_after = balance_before + float(amount)

                                wallet.telco_wallet_balance = balance_after
                                wallet.fund_wallet_count += 1

                        elif user_wallet_type == "WINNINGS_WALLET":
                            # handle case where user is on recovery debt model
                            if DebtRecovery.get_debt_recovery_instance(phone=wallet.user.phone_number) is not None:
                                DebtRecovery.update_debt_recovery_insatnce(phone=wallet.user.phone_number, amount=amount, channel="WEB")
                                return

                            look_out_balance = wallet.withdrawable_available_balance

                            balance_before = look_out_balance
                            balance_after = balance_before + float(amount)

                            wallet.withdrawable_available_balance = balance_after

                        elif user_wallet_type == "AIRTIME_WALLET":
                            # print("not supposed to be here 3")
                            look_out_balance = wallet.airtime_wallet_balance

                            balance_before = look_out_balance
                            balance_after = balance_before + float(amount)

                            wallet.airtime_wallet_balance = balance_after

                        else:
                            DebitCreditRecord.objects.filter(reference=transaction_id).update(status="FAILED")
                            DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="FAILED")

                            return {
                                "balance_before": 0,
                                "balance_after": 0,
                                "record": None,
                                "wallet_instance": None,
                            }

                        wallet.save()

                        record = WalletTransaction.objects.create(
                            wallet=wallet,
                            transaction_reference=transaction_id,
                            unique_transaction_reference=transaction_id,
                            transaction_type="credit",
                            amount=amount,
                            status="successful",
                            transaction_from=transaction_from,
                            bal_before=balance_before,
                            bal_after=balance_after,
                            withdrawable_wallet_balance=wallet.withdrawable_available_balance,
                            game_play_available_balance=wallet.game_available_balance,
                            airtime_wallet_balance=wallet.airtime_wallet_balance,
                        )

                        DebitCreditRecord.objects.filter(reference=transaction_id).update(status="SUCCESS")
                        DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="SUCCESS")

                    else:
                        DebitCreditRecord.objects.filter(reference=transaction_id).update(status="FAILED")
                        DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="FAILED")

                        return {
                            "balance_before": 0,
                            "balance_after": 0,
                            "record": None,
                            "wallet_instance": None,
                        }

                    DebitCreditRecord.objects.filter(reference=transaction_id).update(status="SUCCESS")
                    DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="SUCCESS")

                    return {
                        "balance_before": balance_before,
                        "balance_after": balance_after,
                        "record": record.id,
                        "wallet_instance": wallet.id,
                    }

        except Exception as e:
            # Update the debit credit records to failed status
            DebitCreditRecord.objects.filter(reference=transaction_id).update(status="FAILED")
            DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="FAILED")
            # correct this
            raise Exception(e)

    @staticmethod
    def deduct_wallet(
        user,
        amount,
        channel,
        transaction_id,
        user_wallet_type,
        from_telco_channel=False,
        show_transaction=True,
        **kwargs,
    ):
        """
        USER WALLET TYPES:
        FOR AGENT:
            1. COMMISSION_WALLET
            2. BONUS_WALLET
            3. WINNINGS_WALLET
            4. GAME_PLAY_WALLET

        FOR USER:
            5. GAME_PLAY_WALLET
            6. WINNINGS_WALLET


        CHANNELS:
            1. WEB
            2. POS

        """

        if channel == "WEB":
            wallet = UserWallet.objects.filter(user=user, wallet_tag="WEB").first()
        elif channel == "POS":
            wallet = AgentWallet.objects.filter(agent=user).first()
        else:
            wallet = None

        transaction_from = kwargs.get("transaction_from", None)
        try:
            with transaction.atomic():
                if wallet:
                    # AGENT WALLET
                    if isinstance(wallet, AgentWallet):
                        if user_wallet_type == "COMMISSION_WALLET":
                            look_out_balance = wallet.commission_bal
                            # transaction_from = "COMMISSION"

                            # if look_out_balance < float(amount):
                            #     return {
                            #         "balance_before": 0,
                            #         "balance_after": 0,
                            #         "record": None,
                            #         "wallet_instance": None,
                            #     }

                            balance_before = look_out_balance
                            balance_after = balance_before - float(amount)

                            wallet.commission_bal = balance_after
                            wallet.commission_rewarded += float(amount)

                        elif user_wallet_type == "BONUS_WALLET":
                            look_out_balance = wallet.bonus_bal

                            # if look_out_balance < float(amount):
                            #     return {
                            #         "balance_before": 0,
                            #         "balance_after": 0,
                            #         "record": None,
                            #         "wallet_instance": None,
                            #     }

                            # transaction_from = "BONUS"

                            balance_before = look_out_balance
                            balance_after = balance_before - float(amount)

                            wallet.bonus_bal = balance_after

                        elif user_wallet_type == "WINNINGS_WALLET":
                            look_out_balance = wallet.winnings_bal

                            # if look_out_balance < float(amount):
                            #     return {
                            #         "balance_before": 0,
                            #         "balance_after": 0,
                            #         "record": None,
                            #         "wallet_instance": None,
                            #     }

                            # transaction_from = "WINNINGS"

                            balance_before = look_out_balance
                            balance_after = balance_before - float(amount)

                            wallet.winnings_bal = balance_after

                        elif user_wallet_type == "GAME_PLAY_WALLET":
                            look_out_balance = wallet.game_play_bal
                            # transaction_from = "GAME_PLAY"

                            balance_before = look_out_balance
                            balance_after = balance_before - float(amount)

                            wallet.game_play_bal = balance_after

                        elif user_wallet_type == "REMUNERATION_WALLET":
                            look_out_balance = wallet.remuneration_bal
                            # transaction_from = "GAME_PLAY"

                            balance_before = look_out_balance
                            balance_after = balance_before - float(amount)

                            wallet.remuneration_bal = balance_after
                            wallet.rewarded_remuneration += float(amount)

                        else:
                            DebitCreditRecord.objects.filter(reference=transaction_id).update(status="FAILED")
                            DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="FAILED")
                            return {
                                "balance_before": 0,
                                "balance_after": 0,
                                "record": None,
                                "wallet_instance": None,
                            }

                        wallet.save()

                        game_type = kwargs.get("game_type", None)
                        game_play_id = kwargs.get("game_play_id", None)
                        rewarded_commission = wallet.commission_rewarded

                        game_play_balance_plus_winning = wallet.winnings_bal + wallet.game_play_bal

                        expected_remittance = 0
                        excess_amount = 0

                        if user_wallet_type == "GAME_PLAY_WALLET":
                            # agent_remittance_update = (
                            #     LottoAgentRemittanceTable.deduct_remittance(
                            #         agent=wallet.agent, amount=amount
                            #     )
                            # )

                            LottoAgentRemittanceTable.add_create_remittance(
                                agent=wallet.agent,
                                amount=amount,
                                agent_wallet_balance=wallet.game_play_bal,
                            )

                            expected_remittance = LottoAgentRemittanceTable.expected_remittance(agent_id=wallet.agent.id)
                            excess_amount = 0

                            # if agent_remittance_update is None:
                            #     has_excess = False
                            #     expected_remittance = 0
                            #     excess_amount = 0

                            # elif agent_remittance_update.get("has_excess") is False:
                            #     has_excess = False
                            #     expected_remittance = agent_remittance_update.get(
                            #         "expected_remittance"
                            #     )
                            #     excess_amount = agent_remittance_update.get("excess_amount")

                            # else:
                            #     has_excess = True
                            #     expected_remittance = agent_remittance_update.get(
                            #         "expected_remittance"
                            #     )
                            #     excess_amount = agent_remittance_update.get("excess_amount")

                        type_of_agent = "OTHER_AGENT"
                        if wallet.agent.is_winwise_staff_agent is True:
                            type_of_agent = "WINWISE_AGENT"

                        record = AgentWalletTransaction.objects.create(
                            transaction_reference=transaction_id,
                            unique_transaction_reference=transaction_id,
                            agent_wallet=wallet,
                            transaction_type="DEBIT",
                            amount=amount,
                            status="SUCCESSFUL",
                            transaction_from=transaction_from,
                            bal_before=balance_before,
                            bal_after=balance_after,
                            excess_balance=excess_amount,
                            game_type=game_type,
                            game_play_id=game_play_id,
                            game_play_bal_plus_winnings=game_play_balance_plus_winning,
                            rewarded_commission=rewarded_commission,
                            phone_number=wallet.agent.phone,
                            expected_remittance=expected_remittance,
                            type_of_agent=type_of_agent,
                            type_of_user=wallet.agent.agent_type,
                            agent_name=wallet.agent.first_name + " " + wallet.agent.last_name,
                            agent_phone_number=wallet.agent.phone,
                            agent_email=wallet.agent.email,
                        )

                        DebitCreditRecord.objects.filter(reference=transaction_id).update(status="SUCCESS")
                        DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="SUCCESS")

                        if user_wallet_type == "GAME_PLAY_WALLET":
                            
                            
                            from_lotto_agent = False
                            if wallet.agent.terminal_id is not None or wallet.agent.terminal_id != "":
                                from_lotto_agent = True
                            GamesDailyActivities.create_record(
                                game_type = game_type,
                                sales = amount,
                                from_lotto_agent = from_lotto_agent
                            )

                    elif isinstance(wallet, UserWallet):
                        if user_wallet_type == "GAME_PLAY_WALLET":
                            if from_telco_channel is False:
                                look_out_balance = wallet.game_available_balance

                                balance_before = look_out_balance
                                balance_after = balance_before - float(amount)

                                wallet.game_available_balance = balance_after
                            else:
                                look_out_balance = wallet.telco_wallet_balance

                                balance_before = look_out_balance
                                balance_after = balance_before - float(amount)

                                wallet.telco_wallet_balance = balance_after

                        elif user_wallet_type == "WINNINGS_WALLET":
                            look_out_balance = wallet.withdrawable_available_balance

                            balance_before = look_out_balance
                            balance_after = balance_before - float(amount)

                            wallet.withdrawable_available_balance = balance_after

                        elif user_wallet_type == "AIRTIME_WALLET":
                            look_out_balance = wallet.airtime_wallet_balance

                            balance_before = look_out_balance
                            balance_after = balance_before - float(amount)

                            wallet.airtime_wallet_balance = balance_after
                        else:
                            DebitCreditRecord.objects.filter(reference=transaction_id).update(status="FAILED")
                            DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="FAILED")

                            return {
                                "balance_before": 0,
                                "balance_after": 0,
                                "record": None,
                                "wallet_instance": None,
                            }

                        # print(
                        #     f"""
                        # wallet :: {wallet}
                        # wallet.withdrawable_available_balance :: {wallet.withdrawable_available_balance}
                        # wallet.game_available_balance :: {wallet.game_available_balance}

                        # \n\n\n\n
                        # """
                        # )

                        wallet.save()

                        record = WalletTransaction.objects.create(
                            wallet=wallet,
                            transaction_reference=transaction_id,
                            unique_transaction_reference=transaction_id,
                            transaction_type="debit",
                            amount=amount,
                            status="successful",
                            transaction_from=transaction_from,
                            bal_before=balance_before,
                            bal_after=balance_after,
                            withdrawable_wallet_balance=wallet.withdrawable_available_balance,
                            game_play_available_balance=wallet.game_available_balance,
                            airtime_wallet_balance=wallet.airtime_wallet_balance,
                        )

                    else:
                        DebitCreditRecord.objects.filter(reference=transaction_id).update(status="FAILED")
                        DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="FAILED")

                        return {
                            "balance_before": 0,
                            "balance_after": 0,
                            "record": None,
                            "wallet_instance": None,
                        }

                    DebitCreditRecord.objects.filter(reference=transaction_id).update(status="SUCCESS")
                    DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="SUCCESS")

                    return {
                        "balance_before": balance_before,
                        "balance_after": balance_after,
                        "record": record.id,
                        "wallet_instance": wallet.id,
                    }

        except Exception as e:
            # Update the debit credit records to failed status
            DebitCreditRecord.objects.filter(reference=transaction_id).update(status="FAILED")
            DebitCreditRecord.objects.filter(debit_credit_record_id=transaction_id).update(status="FAILED")

            raise Exception(e)

    @classmethod
    def fund_user_wallet_winning_wallet_via_phone(
        cls,
        phone_number,
        amount,
        game_type,
        played_via_telco_channel=False,
    ):
        from wyse_ussd.tasks import (
            celery_handle_daily_subscription_analytics_for_winners,
            ussd_airtime_reward,
        )

        user_wallet = cls.objects.filter(user__phone_number=phone_number, wallet_tag="WEB").last()

        if user_wallet is None:
            user_profile = UserProfile.objects.filter(
                phone_number=phone_number,
            ).last()
            user_wallet = cls.objects.create(user=user_profile, wallet_tag="WEB")

        if user_wallet:
            # user_wallet.withdrawable_available_balance += float(amount)
            # user_wallet.transaction_from = transfrom
            # user_wallet.save()

            if game_type == "SALARY_FOR_LIFE":
                transaction_from = "SAL_4_LIFE_GAME_WIN"
            elif game_type == "INSTANT_CASHOUT":
                transaction_from = "INSTANT_CASHOUT_GAME_WIN"
            elif game_type == "QUIKA":
                transaction_from = "QUIKA_GAME_WON"

            elif game_type == "VIRTUAL_SOCCER":
                transaction_from = "VIRTUAL_SOCCER_GAME_WON"

            elif game_type == "BANKER":
                transaction_from = "BANKER_GAME_WON"

            elif game_type == "WYSE_CASH":
                transaction_from = "WYSE_CASH_GAME_WIN"
            else:
                transaction_from = "WINNINGS"

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_wallet.user.phone_number,
                amount=amount,
                channel="WEB",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="CREDIT",
            )

            wallet_payload = {
                "transaction_from": transaction_from,
            }

            print("played_via_telco_channel :::::", played_via_telco_channel)

            TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            games_to_give_airtime = UssdConstantVariable.get_games_to_give_airtime_winning()

            if played_via_telco_channel is False:
                # print("NOT SUPPOSED TO BE HERE")
                UserWallet.fund_wallet(
                    user=user_wallet.user,
                    amount=amount,
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="WINNINGS_WALLET",
                    **wallet_payload,
                )

                _days = f"{TODAY.year}-{TODAY.strftime('%d-%b')}"

                celery_handle_daily_subscription_analytics_for_winners.delay(
                    phone_number=user_wallet.user.phone_number,
                    amount_won=amount,
                    date=_days,
                )

            else:
                if UssdConstantVariable.is_give_airtime_on_game_win() is False:
                    UserWallet.fund_wallet(
                        user=user_wallet.user,
                        amount=amount,
                        channel="WEB",
                        transaction_id=debit_credit_record.reference,
                        user_wallet_type="WINNINGS_WALLET",
                        **wallet_payload,
                    )

                    _days = f"{TODAY.year}-{TODAY.strftime('%d-%b')}"

                    celery_handle_daily_subscription_analytics_for_winners.delay(
                        phone_number=user_wallet.user.phone_number,
                        amount_won=amount,
                        date=_days,
                    )
                else:
                    if (amount < UssdConstantVariable.get_max_telco_winning_airtime_threshold()) and (game_type in games_to_give_airtime):
                        UserWallet.fund_wallet(
                            user=user_wallet.user,
                            amount=amount,
                            channel="WEB",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="AIRTIME_WALLET",
                            from_telco_channel=played_via_telco_channel,
                            **wallet_payload,
                        )

                        ussd_airtime_reward.delay(amount=amount, phone_number=user_wallet.user.phone_number)

                        _days = f"{TODAY.year}-{TODAY.strftime('%d-%b')}"

                        celery_handle_daily_subscription_analytics_for_winners.delay(
                            phone_number=user_wallet.user.phone_number,
                            amount_won=amount,
                            date=_days,
                            is_airtime_reward=True,
                        )

                    else:
                        UserWallet.fund_wallet(
                            user=user_wallet.user,
                            amount=amount,
                            channel="WEB",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="WINNINGS_WALLET",
                            **wallet_payload,
                        )

                        _days = f"{TODAY.year}-{TODAY.strftime('%d-%b')}"

                        celery_handle_daily_subscription_analytics_for_winners.delay(
                            phone_number=user_wallet.user.phone_number,
                            amount_won=amount,
                            date=_days,
                        )

        else:
            # user_profile = UserProfile.objects.filter(
            #     phone_number=phone_number,
            # ).last()
            # if user_profile:
            #     cls.objects.create(
            #         user=user_profile,
            #         withdrawable_available_balance=float(amount),
            #         wallet_tag="WEB",
            #     )

            pass

    @classmethod
    def fund_user_wallet_play_wallet_via_phone(cls, phone_number, amount):
        # user_wallet = cls.objects.filter(
        #     user__phone_number=phone_number, wallet_tag="WEB"
        # ).last()
        # if user_wallet:
        #     user_wallet.game_available_balance += float(amount)
        #     user_wallet.transaction_from = "FUNDING"
        #     user_wallet.save()

        # else:
        #     user_profile = UserProfile.objects.filter(
        #         phone_number=phone_number,
        #     ).last()
        #     if user_profile:
        #         cls.objects.create(
        #             user=user_profile,
        #             game_available_balance=float(amount),
        #             wallet_tag="WEB",
        #         )

        pass

    # @staticmethod
    # def deduct_wallet(user, amount):
    #     wallet = UserWallet.objects.filter(user=user, wallet_tag="WEB").last()
    #     if wallet:
    #         wallet.withdrawable_available_balance -= float(amount)
    #         wallet.save()

    #     return wallet.game_available_balance

    @staticmethod
    def lottery_payment(wallet_instance, amount, lottery_qs):
        """
        This method is used to pay for user lottery ticket
        """

        # check if lottery batch is still active
        lottery_instance = lottery_qs.last()

        # qs_ids = list(map(lambda id_tuple: id_tuple[0], lottery_qs.values_list("id")))

        if amount < lottery_instance.expected_amount:
            data = {
                "status": "failed",
                "message": "please enter the correct amount for this lottery",
            }

            return data

        if lottery_instance.batch.is_active is False:
            # if his batch has ended, create a new batch
            # and add him to the new batch

            if lottery_instance.lottery_type == "SALARY_FOR_LIFE":
                global_jackpot = LotteryGlobalJackPot.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active=True).last()

                new_batch = LotteryBatch.objects.create(
                    lottery_type="SALARY_FOR_LIFE",
                    global_jackpot=global_jackpot,
                )

            elif lottery_instance.lottery_type == "INSTANT_CASHOUT":
                new_batch = LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT")

            elif lottery_instance.lottery_type == "WYSE_CASH":
                new_batch = LotteryBatch.objects.create(
                    lottery_type="WYSE_CASH",
                )

            # update lottery ticket batch
            lottery_qs.update(batch=new_batch)

        # update lottery payment as paid and paid amount
        lottery_qs.update(paid=True, amount_paid=amount)

        lottery_instance = lottery_qs.last()

        trans_from = None
        if lottery_instance.lottery_type == "SALARY_FOR_LIFE":
            trans_from = "SAL_4_LIFE_GAME_PLAY"

        elif lottery_instance.lottery_type == "INSTANT_CASHOUT":
            trans_from = "INSTANT_CASHOUT_GAME_PLAY"

        elif lottery_instance.lottery_type == "WYSE_CASH":
            trans_from = "WYSE_CASH_GAME_PLAY"

        # lottery_instance = LottoTicket.objects.filter(id__in = qs_ids).last()

        # if not lottery_instance:
        #     lottery_instance = LotteryModel.objects.filter(id__in = qs_ids).last()

        # update wallet
        wallet_instance.game_available_balance = wallet_instance.game_available_balance - amount
        wallet_instance.transaction_from = trans_from
        wallet_instance.save()

        data = {"status": "success", "message": "Lottery ticket paid successfully"}
        return data

    @classmethod
    def update_wallet_after_payment(cls, user_profile, wallet_tag, trans_from, amount):
        user_wallet = cls.objects.filter(user=user_profile, wallet_tag=wallet_tag).last()
        print("BALANCE BEFORE --------->", user_wallet.game_available_balance)
        user_wallet.game_available_balance -= amount
        user_wallet.transaction_from = trans_from
        user_wallet.save()
        print("AMOUNT DEBITED :::::::::", amount)
        # print("BALANCE AFTER --------->", cls.objects.filter(user=user_profile, wallet_tag=wallet_tag).last().game_available_balance)

    @classmethod
    def fund_wallet_with_wema_call_back(cls, account_number, amount, reference):
        """
        Fund a Wema Bank wallet associated with a specific account number.

        Args:
            cls: The class (usually a model) to facilitate wallet funding.
            account_number (str): The account number of the Wema Bank wallet.
            amount (float): The amount to be credited to the wallet.
            reference (str): A unique reference for the transaction.

        Returns:
            bool or None: Returns True if the wallet is funded successfully, False if the transaction with the same
            reference already exists, or None if the account number is not found.

        Description:
        - This method is used to fund a Wema Bank wallet associated with a specific account number.
        - It checks if the account number exists in the database.
        - If the account number is found, it proceeds to fund the wallet with the specified amount.
        - It checks for the existence of a transaction with the same reference to prevent duplicates.
        - The transaction is recorded as a credit in the DebitCreditRecord.
        - The wallet is funded by calling the UserWallet's fund_wallet method.
        - The method returns True if the wallet is successfully funded, False if a duplicate transaction is detected,
        and None if the account number is not found.

        Example:
        - You can use this method to credit a user's Wema Bank wallet when a payment is made.

        Usage:
        ```
        account_number = "WEMA123456"
        amount = 100.0
        reference = "TXN12345"

        result = Wallet.fund_wema_wallet_acount_type(account_number, amount, reference)
        if result is True:
            print("Wallet funded successfully.")
        elif result is False:
            print("Duplicate transaction reference detected.")
        else:
            print("Account number not found.")
        ```
        """
        # Check if the account number exists in the database
        acct_instance = WovenAccountDetail.objects.filter(vnuban=account_number).last()

        if acct_instance:
            # Check for the existence of a transaction with the same reference
            txn_exist = WalletTransaction.objects.filter(transaction_reference=reference).exists()
            if txn_exist is True:
                return False  # Duplicate transaction reference detected

            # Retrieve the wallet instance associated with the account
            phone_number = acct_instance.phone_number
            user = UserProfile.objects.get(phone_number=phone_number)
            amount_to_float = float(amount)

            # Create a debit-credit record for the transaction
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=phone_number,
                amount=amount_to_float,
                channel="WEB",
                reference=reference,
                transaction_type="CREDIT",
            )

            # Define the wallet payload
            wallet_payload = {
                "transaction_from": "FUND_WEMA_PLAY_WALLET",
                "game_type": "",
                "game_play_id": "",
            }

            # Fund the wallet using the UserWallet's fund_wallet method
            UserWallet.fund_wallet(
                user=user,
                amount=amount_to_float,
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="GAME_PLAY_WALLET",
                **wallet_payload,
            )

            return True  # Wallet funded successfully
        else:
            return None  # Account number not found

    @classmethod
    def create_update_wallet(cls, user_instance, tag, acct_provider):
        """
        Create or update a user's wallet based on WovenAccountDetail information.

        Args:
            user_instance: An instance of the user model.
            tag: A string representing the wallet tag.
            acct_provider: An object representing the account provider.

        Returns:
            Wallet instance: The created or updated wallet instance associated with the user.
        """
        # Query WovenAccountDetail to find relevant accounts
        wema_accounts = WovenAccountDetail.objects.filter(
            phone_number=user_instance.phone_number,
            acct_provider=acct_provider,
            wallet_tag=tag,
        )

        # Check if WovenAccountDetail records exist
        if wema_accounts.exists():
            # Retrieve the first account instance
            account_instance = wema_accounts.first()

            # Check if there is an existing user wallet without WovenAccountDetail association
            user_wallet = cls.objects.filter(user=user_instance, wema_account__isnull=True, wallet_tag=tag)
            if user_wallet:
                # If user wallet exists, update the associated WovenAccountDetail
                wallet_instance = user_wallet.last()
                wallet_instance.wema_account = account_instance
                wallet_instance.wema_account_ref = account_instance.account_ref
                wallet_instance.save()

                return wallet_instance

            # If there is no existing user wallet with the given WovenAccountDetail association
            elif not cls.objects.filter(wema_account=account_instance, wallet_tag=tag):
                # Create a new user wallet with the association to WovenAccountDetail
                wallet = cls.objects.create(
                    user=user_instance,
                    wallet_tag=tag,
                    wema_account=account_instance,
                    wema_account_ref=account_instance.account_ref,
                )
                return wallet

            else:
                wallet = cls.objects.filter(wema_account=account_instance, wallet_tag=tag).first()
                return wallet
        else:
            # If no WovenAccountDetail records are found, create a Wema collection account
            create_wema_collection_account(phone_number=user_instance.phone_number)

            # Recursive call to create or update the wallet after creating the Wema collection account
            return cls.create_update_wallet(user_instance, tag, acct_provider)


class WalletTransaction(models.Model):
    TRANSACTION_STATUS_CHOICES = [
        ("pending", "Pending"),
        ("successful", "Successful"),
        ("failed", "Failed"),
    ]

    TRANSACTION_TYPE_CHOICES = [
        ("credit", "Credit"),
        ("debit", "Debit"),
        ("REVERSAL", "REVERSAL"),
    ]

    TRANSACTION_FROM = [
        ("WALLET", "WALLET"),
        ("GAME", "GAME"),
        ("WITHDRAWAL", "WITHDRAWAL"),
        ("PLAY_WALLET", "PLAY_WALLET"),
        ("REVERSAL", "REVERSAL"),
        ("WINNINGS", "WINNINGS"),
        ("PAYSTACK_FUNDING", "PAYSTACK_FUNDING"),
        ("PABBLY_PAYSTACK", "PABBLY_PAYSTACK"),
        ("WOVEN_FUNDING", "WOVEN_FUNDING"),
        ("WEMA_FUNDING", "WEMA_FUNDING"),
        ("WITHDRAW_TO_PLAY_WALLET", "WITHDRAW_TO_PLAY_WALLET"),
        ("FUNDING", "FUNDING"),
        ("FUNDING_FROM_WITHDRAWABLE_WALLET", "FUNDING_FROM_WITHDRAWABLE_WALLET"),
        ("REDBILLER_FUNDING", "REDBILLER_FUNDING"),
        ("REFERRAL_BONUS", "REFERRAL_BONUS"),
        ("FROM_ADMIN_DASHBOARD", "FROM_ADMIN_DASHBOARD"),
        ("CORAL_PAY", "CORAL_PAY"),
        ("WATU_PAY", "WATU_PAY"),
        ("TRANSAFERING_TO_PLAY_WALLET", "TRANSAFERING_TO_PLAY_WALLET"),
        ("SOCCER_CASH_GAME_PLAY", "SOCCER_CASH_GAME_PLAY"),
        ("SAL_4_LIFE_GAME_PLAY", "SAL_4_LIFE_GAME_PLAY"),
        ("INSTANT_CASHOUT_GAME_PLAY", "INSTANT_CASHOUT_GAME_PLAY"),
        ("WYSE_CASH_GAME_PLAY", "WYSE_CASH_GAME_PLAY"),
        ("SOCCER_CASH_GAME_WIN", "SOCCER_CASH_GAME_WIN"),
        ("SAL_4_LIFE_GAME_WIN", "SAL_4_LIFE_GAME_WIN"),
        ("INSTANT_CASHOUT_GAME_WIN", "INSTANT_CASHOUT_GAME_WIN"),
        ("WYSE_CASH_GAME_WIN", "WYSE_CASH_GAME_WIN"),
        ("VIRTUAL_SOCCER_GAME_PLAY", "VIRTUAL_SOCCER_GAME_PLAY"),
        ("VIRTUAL_SOCCER_GAME_WON", "VIRTUAL_SOCCER_GAME_WON"),
        ("VIRTUAL_SOCCER_GAME_WON", "VIRTUAL_SOCCER_GAME_WON"),
        ("BANKER_GAME_PLAY", "BANKER_GAME_PLAY"),
        ("BANKER_GAME_WON", "BANKER_GAME_WON"),
        ("QUIKA_GAME_PLAY", "QUIKA_GAME_PLAY"),
        ("QUIKA_GAME_WON", "QUIKA_GAME_WON"),
        ("AWOOF_GAME_PLAY", "AWOOF_GAME_PLAY"),
        ("AWOOF_GAME_WON", "AWOOF_GAME_WON"),
        ("BONUS_AWARDED", "BONUS_AWARDED"),
        ("TEST_PURPOSE", "TEST_PURPOSE"),
        ("GAME_PLAY", "GAME_PLAY"),
        ("DEBT_COLLECTION", "DEBT_COLLECTION"),
        ("SPECIAL_CASE", "SPECIAL_CASE"),
        ("GAME_PLAY_REVERSAL", "GAME_PLAY_REVERSAL"),
        ("TELCO_FUNDING", "TELCO_FUNDING"),
        ("REDBILLER_TELCO_FUNDING", "REDBILLER_TELCO_FUNDING"),
        ("UPSTREAM_FUNDING", "UPSTREAM_FUNDING"),
        ("MANCALA_GAME_PLAY", "MANCALA GAME PLAY"),
        ("MANCALA_GAME_PLAY_RVSL", "MANCALA GAME PLAY RVSL"),
        ("FUND_WEMA_PLAY_WALLET", "FUND WEMA PLAY WALLET"),
        ("MANCALA_WINNING", "MANCALA WINNING"),
        ("AIRTIME_PURCHASE", "AIRTIME_PURCHASE"),
        ("AIRTIME_PURCHASE_REVERSAL", "AIRTIME_PURCHASE_REVERSAL"),
        ("WITHDRAWAL_DEBT_DEDUCTION", "WITHDRAWAL_DEBT_DEDUCTION"),
    ]

    GENERAL_ACTION_STATUS = [
        (None, "None"),
        ("WINNINGS", "WINNINGS"),
        ("FUNDING", "FUNDING"),
        ("GAME_PLAY", "GAME_PLAY"),
    ]

    wallet = models.ForeignKey(UserWallet, on_delete=models.CASCADE)
    transaction_type = models.CharField(
        max_length=125,
        choices=TRANSACTION_TYPE_CHOICES,
        null=True,
        blank=True,
        default="credit",
    )
    user: str
    transaction_reference = models.CharField(max_length=125, default=uuid.uuid4)
    unique_transaction_reference = models.CharField(max_length=125, blank=True, null=True, unique=True)
    method = models.CharField(max_length=125, null=True, blank=True, default="WALLET")
    amount = models.FloatField(default=0)
    bal_before = models.FloatField(default=0)
    bal_after = models.FloatField(default=0)
    status = models.CharField(max_length=125, default="pending", null=True, blank=True)
    transaction_from = models.CharField(max_length=125, default="WALLET", choices=TRANSACTION_FROM)
    date_created = models.DateTimeField(auto_now_add=True)
    show_transaction = models.BooleanField(default=True)
    withdrawable_wallet_balance = models.FloatField(default=0)
    game_play_available_balance = models.FloatField(default=0)
    airtime_wallet_balance = models.FloatField(default=0)
    general_action_status = models.CharField(max_length=300, choices=GENERAL_ACTION_STATUS, null=True, blank=True)

    def __str__(self) -> str:
        return f"{self.wallet}-Wallet"

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "WALLET TRANSACTION"
        verbose_name_plural = "WALLET TRANSACTIONS"

    @property
    def user(self):
        return self.wallet.user.id

    @staticmethod
    def create_wallet_transaction(
        wallert_instance,
        transaction_type,
        amount,
        status,
        bal_before,
        bal_after,
        withdrawable_wallet_balance,
        game_play_available_balance,
        method=None,
    ):
        wallet_trans_instance = WalletTransaction.objects.create(
            wallet=wallert_instance,
            transaction_type=transaction_type,
            amount=amount,
            status=status,
            bal_after=bal_after,
            bal_before=bal_before,
            withdrawable_wallet_balance=withdrawable_wallet_balance,
            game_play_available_balance=game_play_available_balance,
        )

        if wallert_instance.transaction_from is not None and wallert_instance.transaction_from == "GAME":
            wallet_trans_instance.transaction_from = "GAME"
            wallet_trans_instance.save()

        elif wallert_instance.transaction_from is not None and wallert_instance.transaction_from == "WITHDRAWAL":
            wallet_trans_instance.method = "WITHDRAWAL"
            wallet_trans_instance.transaction_from = "WITHDRAWAL"
            wallet_trans_instance.save()

        elif wallert_instance.transaction_from is not None:
            wallet_trans_instance.transaction_from = wallert_instance.transaction_from
            wallet_trans_instance.save()

    @staticmethod
    def filter_transaction_history(user, start_date=None, end_date=None, status=None, method=None, req=None):
        from wallet_app.serializers import (
            ReferralTransactionSerializer,
            WalletTransactionHistorySerializer,
        )

        data = {}
        d = []
        page = req.GET.get("page", 1) if req is not None else 1

        if start_date is None and end_date is None and status is None and method is None:
            transactions = WalletTransaction.objects.filter(wallet__user=user, show_transaction=True)
            transactions = Paginator.paginate(request=req, queryset=transactions, page=page)
            transactions = WalletTransactionHistorySerializer(transactions, many=True)

            referral_transaction = ReferralTransaction.objects.filter(user=user)
            referral_transaction = Paginator.paginate(request=req, queryset=referral_transaction, page=page)
            referral_transaction = ReferralTransactionSerializer(referral_transaction, many=True)

            print("data before", data)
            data["referral_transaction"] = referral_transaction.data
            data["wallet_transaction"] = transactions.data

            print("data after", data)

            data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

            del data["referral_transaction"]
            del data["wallet_transaction"]

            if not data["all_transactions"]:
                data["all_transactions"] = []
                return data

            for i in data["all_transactions"]:
                d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                d.append(d2)

            data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

            return data

        if start_date and end_date:
            transactions = WalletTransaction.objects.filter(
                wallet__user=user,
                date_created__date__range=(start_date, end_date),
                show_transaction=True,
            )

            if status:
                st = status.casefold()
                transactions = transactions.filter(status=st)
                transactions = Paginator.paginate(request=req, queryset=transactions, page=page)
                if st == "successful":
                    referral_transaction = ReferralTransaction.objects.filter(user=user, created_at__date__range=(start_date, end_date))
                    referral_transaction = Paginator.paginate(request=req, queryset=referral_transaction, page=page)
                else:
                    referral_transaction = ReferralTransaction.objects.none()

            if method:
                _method = method.casefold()
                if _method == "wallet":
                    transactions = transactions.filter(transaction_type="deposit")
                    transactions = Paginator.paginate(request=req, queryset=transactions, page=page)
                    wallet_transction_serializer = WalletTransactionHistorySerializer(transactions, many=True)
                    data["wallet_transaction"] = wallet_transction_serializer.data

                    data["referral_transaction"] = {}

                    data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

                    del data["referral_transaction"]
                    del data["wallet_transaction"]

                    if not data["all_transactions"]:
                        data["all_transactions"] = []
                        return data

                    for i in data["all_transactions"]:
                        d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                        d.append(d2)

                    data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

                    return data

                elif _method == "referral":
                    data["wallet_transaction"] = {}

                    referral_transaction_serializer = ReferralTransactionSerializer(referral_transaction, many=True)
                    data["referral_transaction"] = referral_transaction_serializer.data

                    data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

                    if not data["all_transactions"]:
                        data["all_transactions"] = []
                        return data

                    for i in data["all_transactions"]:
                        d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                        d.append(d2)

                    del data["referral_transaction"]
                    del data["wallet_transaction"]

                    data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

                    return data

                wallet_transction_serializer = WalletTransactionHistorySerializer(transactions, many=True)
                data["wallet_transaction"] = wallet_transction_serializer.data

                referral_transaction_serializer = ReferralTransactionSerializer(referral_transaction, many=True)
                data["referral_transaction"] = referral_transaction_serializer.data

                data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

                if not data["all_transactions"]:
                    data["all_transactions"] = []
                    return data

                for i in data["all_transactions"]:
                    d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                    d.append(d2)

                del data["referral_transaction"]
                del data["wallet_transaction"]

                data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

                return data

            wallet_transction_serializer = WalletTransactionHistorySerializer(transactions, many=True)
            data["wallet_transaction"] = wallet_transction_serializer.data

            referral_transaction_serializer = ReferralTransactionSerializer(referral_transaction, many=True)
            data["referral_transaction"] = referral_transaction_serializer.data

            data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

            if not data["all_transactions"]:
                data["all_transactions"] = []
                return data

            for i in data["all_transactions"]:
                d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                d.append(d2)

            del data["referral_transaction"]
            del data["wallet_transaction"]

            data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

            return data

        elif status:
            st = status.casefold()
            transactions = WalletTransaction.objects.filter(wallet__user=user, status=st)
            transactions = transactions.filter(status=status)
            transactions = Paginator.paginate(request=req, queryset=transactions, page=page)

            if status.casefold() == "successful":
                referral_transaction = ReferralTransaction.objects.filter(user=user)
                referral_transaction = Paginator.paginate(request=req, queryset=referral_transaction, page=page)
            else:
                referral_transaction = ReferralTransaction.objects.none()

            if method:
                _method = method.casefold()
                if _method == "wallet":
                    transactions = WalletTransaction.objects.filter(wallet__user=user, status=st)
                    transactions = transactions.filter(status=status)
                    transactions = transactions.filter(transaction_type="deposit")
                    transactions = Paginator.paginate(request=req, queryset=transactions, page=page)

                    wallet_transction_serializer = WalletTransactionHistorySerializer(transactions, many=True)
                    data["wallet_transaction"] = wallet_transction_serializer.data

                    data["referral_transaction"] = {}

                    data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

                    if not data["all_transactions"]:
                        data["all_transactions"] = []
                        return data

                    for i in data["all_transactions"]:
                        d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                        d.append(d2)

                    del data["referral_transaction"]
                    del data["wallet_transaction"]

                    data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

                    return data

                elif _method == "referral":
                    data["wallet_transaction"] = {}

                    referral_transaction_serializer = ReferralTransactionSerializer(referral_transaction, many=True)
                    data["referral_transaction"] = referral_transaction_serializer.data

                    data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

                    if not data["all_transactions"]:
                        data["all_transactions"] = []
                        return data

                    for i in data["all_transactions"]:
                        d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                        d.append(d2)

                    del data["referral_transaction"]
                    del data["wallet_transaction"]

                    data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

                    return data

            wallet_transction_serializer = WalletTransactionHistorySerializer(transactions, many=True)
            data["wallet_transaction"] = wallet_transction_serializer.data

            referral_transaction_serializer = ReferralTransactionSerializer(referral_transaction, many=True)
            data["referral_transaction"] = referral_transaction_serializer.data

            data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

            if not data["all_transactions"]:
                data["all_transactions"] = []
                return data

            for i in data["all_transactions"]:
                d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                d.append(d2)

            del data["referral_transaction"]
            del data["wallet_transaction"]

            data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

            return data

        elif method:
            _method = method.casefold()
            if _method == "wallet":
                transactions = WalletTransaction.objects.filter(wallet__user=user)
                wallet_transction_serializer = WalletTransactionHistorySerializer(transactions, many=True)
                data["wallet_transaction"] = wallet_transction_serializer.data

                data["referral_transaction"] = {}

                data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

                del data["referral_transaction"]
                del data["wallet_transaction"]

                if not data["all_transactions"]:
                    data["all_transactions"] = []
                    return data

                for i in data["all_transactions"]:
                    d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                    d.append(d2)

                del data["referral_transaction"]
                del data["wallet_transaction"]

                data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

                return data

            elif _method == "referral":
                data["wallet_transaction"] = {}
                referral_transaction = ReferralTransaction.objects.filter(user=user)
                referral_transaction_serializer = ReferralTransactionSerializer(referral_transaction, many=True)
                data["referral_transaction"] = referral_transaction_serializer.data
                data["all_transactions"] = list(data["referral_transaction"]) + list(data["wallet_transaction"])

                if not data["all_transactions"]:
                    data["all_transactions"] = []
                    return data

                for i in data["all_transactions"]:
                    d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                    d.append(d2)

                del data["referral_transaction"]
                del data["wallet_transaction"]

                data["all_transactions"] = sorted(d, key=lambda x: x["date_created"], reverse=True)

                return data


class BeneficiaryDetail(models.Model):
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    bank_name = models.CharField(max_length=125, null=True, blank=True)
    account_number = models.CharField(max_length=125, unique=True)
    account_name = models.CharField(max_length=125, null=True, blank=True)
    bank_code = models.CharField(max_length=125, null=True, blank=True)
    logo = models.CharField(max_length=300, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return self.user.__str__()

    class Meta:
        verbose_name = "BENEFICIARY"
        verbose_name_plural = "BENEFICIARIES"


class CreditCardDetail(models.Model):
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="card")
    card_signature = models.CharField(max_length=4000)
    bank_name = models.CharField(max_length=2100)
    account_name = models.CharField(max_length=2100, default="Invalid", blank=True)
    bin = models.CharField(max_length=26)
    card_last_four_digit = models.CharField(max_length=24)
    authorization_code = models.CharField(max_length=2100)
    exp_month = models.CharField(max_length=22)
    exp_year = models.CharField(max_length=24)
    card_type = models.CharField(max_length=2200)
    channel = models.CharField(max_length=2200)
    country_code = models.CharField(max_length=2200)
    brand = models.CharField(max_length=2200)
    date_created = models.DateTimeField(auto_now_add=True)
    updated_date_created = models.DateTimeField(auto_now=True)
    device_provider = models.CharField(max_length=2300, null=True, blank=True)

    def __str__(self):
        return str(self.id) and str(self.card_signature)

    class Meta:
        verbose_name = "CREDIT CARD"
        verbose_name_plural = "CREDIT CARDS"

    @staticmethod
    def create_credit_card(
        user,
        card_signature,
        account_name,
        bank_name,
        bin,
        card_last_four_digit,
        authorization_code,
        exp_month,
        exp_year,
        card_type,
        channel,
        country_code,
        brand,
        device_provider,
    ):
        similar_old_cards = user.card.filter(
            card_signature=card_signature,
            card_last_four_digit=card_last_four_digit,
            card_type=card_type,
            account_name=account_name,
            bank_name=bank_name,
        )

        if similar_old_cards.exists():
            return

        new_credit_card = CreditCardDetail(
            user=user,
            card_signature=card_signature,
            bank_name=bank_name,
            account_name="invalid" if account_name is None else account_name,
            bin=bin,
            card_last_four_digit=card_last_four_digit,
            authorization_code=authorization_code,
            exp_month=exp_month,
            exp_year=exp_year,
            card_type=card_type,
            channel=channel,
            country_code=country_code,
            brand=brand,
            device_provider=device_provider,
        )
        new_credit_card.save()

        return new_credit_card


class PaystackTransaction(models.Model):
    PAYMENT_REASON = [
        ("WALLET_FUNDING", "WALLET_FUNDING"),
        ("LOTTERY_PAYMENT", "LOTTERY_PAYMENT"),
        ("MOBILE_LOTTERY_PAYMENT", "MOBILE_LOTTERY_PAYMENT"),
        ("MOBILE_AGENT_FUNDING", "MOBILE_AGENT_FUNDING"),
    ]

    CHANNEL = [
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS", "POS"),
        ("USSD", "USSD"),
    ]

    STATUS = [
        ("PENDING", "PENDING"),
        ("SUCCESSFUL", "SUCCESSFUL"),
        ("FAILED", "FAILED"),
    ]

    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="paystack_transactions")
    amount = models.FloatField(default=0.0)
    reference = models.CharField(max_length=2115)
    event = models.CharField(max_length=2115)
    paid_at = models.DateTimeField()
    created_at = models.DateTimeField()
    received_at = models.DateTimeField(auto_now_add=True)
    channel = models.CharField(max_length=2110, choices=CHANNEL, default="WEB")
    currency = models.CharField(max_length=2120)
    reason = models.CharField(max_length=2120, null=True)
    mobile = models.BooleanField(null=True)
    bank = models.CharField(max_length=2120, null=True)
    card_type = models.CharField(max_length=2125, null=True)
    gateway_response = models.CharField(max_length=2125)
    device_provider = models.CharField(max_length=2300, null=True, blank=True)
    raw_data = models.TextField(null=True, blank=True)
    payment_reason = models.CharField(max_length=2120, choices=PAYMENT_REASON, default="WALLET_DEPOSIT")
    is_verified = models.BooleanField(default=False)
    status = models.CharField(max_length=300, choices=STATUS, default="PENDING")

    class Meta:
        verbose_name = "PAYSTACK TRANSACTION"
        verbose_name_plural = "PAYSTACK TRANSACTIONS"

    @staticmethod
    def create_transaction(user, data, raw_data):
        card_details = data.get("data").get("authorization")
        logs = data.get("data").get("log")
        amount = (data.get("data").get("amount")) / 100

        payment_reference = data.get("data").get("reference")
        device_provider = data.get("device_provider")
        similar_payments = PaystackTransaction.objects.filter(reference=payment_reference)
        if similar_payments.exists():
            return False

        if isinstance(logs, dict):
            mobile = logs.get("mobile")
        else:
            mobile = True

        transaction = PaystackTransaction.objects.create(
            user=user,
            amount=amount,
            reference=payment_reference,
            event=data.get("event"),
            paid_at=standard_str_to_dt(data.get("data", {}).get("paid_at")),
            created_at=standard_str_to_dt(data.get("data", {}).get("created_at")),
            channel=data.get("data", {}).get("channel"),
            currency=data.get("data", {}).get("currency"),
            raw_data=data.get("raw_data"),
            reason=data.get("data", {}).get("reason"),
            mobile=mobile,
            card_type=data.get("data", {}).get("authorization", {}).get("card_type"),
            bank=data.get("data", {}).get("authorization", {}).get("bank"),
            gateway_response=data.get("data", {}).get("gateway_response"),
            device_provider=device_provider,
        )

        if (
            transaction.event == "charge.success"
            and "Approved" in transaction.gateway_response
            or transaction.gateway_response == "Payment successful"
            or "success" in (transaction.gateway_response).lower()
        ):
            UserWallet.fund_wallet(user=user, amount=transaction.amount)

            wallet_transaction_instance = WalletTransaction.objects.get(transaction_reference=transaction.reference)
            wallet_transaction_instance.status = "successful"
            wallet_transaction_instance.method = transaction.channel
            wallet_transaction_instance.save()

            CreditCardDetail.create_credit_card(
                user,
                card_details.get("card_signature", ""),
                card_details.get("account_name", ""),
                card_details.get("bank", ""),
                card_details.get("bin", ""),
                card_details.get("last4", ""),
                card_details.get("authorization_code", ""),
                card_details.get("exp_month", ""),
                card_details.get("exp_year", ""),
                card_details.get("card_type", ""),
                card_details.get("channel", ""),
                card_details.get("country_code", ""),
                card_details.get("brand", ""),
                device_provider=device_provider,
            )
            return True

    @staticmethod
    def verify_lottery_payment(instance, data, payment_for="MAIN_WYSE_CASH_WEB_LOTTERY_PAYMENT"):
        """
        Verify user lottery payment and update his lottery as paid
        """

        from wallet_app.tasks import notify_admin_of_user_funding
        from wyse_ussd.tasks import share_ussd_payment_across_lottery_pool

        instance.raw_data = data.get("raw_data")
        instance.bank = data.get("data", {}).get("authorization", {}).get("bank")
        instance.save()

        transaction_amount = data.get("data", {}).get("amount", {})

        # verify payment
        verification_data = PaymentGateway().paystack_verify_payment(instance.reference)
        amount = transaction_amount

        print("verification_data", verification_data, "\n\n\n")

        if isinstance(verification_data, dict):
            # tokenize card
            celery_tokenize_paystack_card.delay(instance.user.id, json.dumps(data.get("raw_data")))

            if verification_data.get("data").get("status").casefold() == "success" or verification_data.get("message") == "Verification successful":
                # update paystack model instance
                instance.is_verified = True
                instance.status = "SUCCESSFUL"
                instance.save()

                if instance.channel == "USSD":
                    pending_lottery_payment_instance = UssdLotteryPayment.objects.filter(
                        user=instance.user,
                        is_successful=False,
                        is_verified=False,
                    ).last()

                    if pending_lottery_payment_instance:
                        if (instance.amount - 50) == pending_lottery_payment_instance.amount:
                            pending_lottery_payment_instance.amount_paid = instance.amount

                            pending_lottery_payment_instance.amount_paid = instance.amount - 50
                            pending_lottery_payment_instance.save()
                            # pending_lottery_payment_instance.is_verified = True
                            # pending_lottery_payment_instance.is_successful = True
                            # pending_lottery_payment_instance.save()

                            share_ussd_payment_across_lottery_pool.delay(
                                instance.user.phone_number,
                                int(instance.amount - 50),
                                pending_lottery_payment_instance.game_play_id,
                                transfrom="PAYSTACK_FUNDING",
                            )

                            return True

                    wallet_payload = {
                        "transaction_from": "PAYSTACK_FUNDING",
                    }

                    transaction_ref = f"{uuid.uuid4()}{int(time.time())}"

                    debit_credit_record = DebitCreditRecord.create_record(
                        phone_number=instance.user.phone_number,
                        amount=amount,
                        channel="USSD",
                        reference=transaction_ref,
                        transaction_type="CREDIT",
                    )

                    UserWallet.fund_wallet(
                        user=instance.user,
                        amount=int(amount),
                        channel="WEB",
                        transaction_id=debit_credit_record.reference,
                        user_wallet_type="GAME_PLAY_WALLET",
                        from_telco_channel=False,
                        **wallet_payload,
                    )

                    return True

                if payment_for == "MAIN_WYSE_CASH_WEB_LOTTERY_PAYMENT":
                    """
                    MAIN WHYSE CASH WEB LOTTERY PAYMENT VERIFICATION
                    """

                    # fund user wallet
                    get_fund_wallet = UserWallet.objects.filter(user=instance.user, wallet_tag="WEB").last()
                    if get_fund_wallet:
                        # get_fund_wallet.game_available_balance += instance.amount
                        # get_fund_wallet.transaction_from = "PAYSTACK_FUNDING"
                        # get_fund_wallet.save()

                        debit_credit_record = DebitCreditRecord.create_record(
                            phone_number=user_profile.phone_number,  # noqa
                            amount=instance.amount,
                            channel="WEB",
                            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                            transaction_type="CREDIT",
                        )

                        wallet_payload = {
                            "transaction_from": "PAYSTACK_FUNDING",
                        }

                        UserWallet.fund_wallet(
                            user=user_profile,  # noqa
                            amount=int(amount),
                            channel="WEB",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="GAME_PLAY_WALLET",
                            **wallet_payload,
                        )

                        # notify admin
                        notify_admin_of_user_funding.delay(
                            amount=int(instance.amount),
                            phone_number=instance.user.phone_number,
                            channel="PAYSTACK",
                        )
                    else:
                        get_fund_wallet = UserWallet.objects.create(
                            user=instance.user,
                            wallet_tag="WEB",
                        )

                        # get_fund_wallet.game_available_balance += instance.amount
                        # get_fund_wallet.transaction_from = "PAYSTACK_FUNDING"
                        # get_fund_wallet.save()

                        debit_credit_record = DebitCreditRecord.create_record(
                            phone_number=user_profile.phone_number,  # noqa
                            amount=instance.amount,
                            channel="WEB",
                            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                            transaction_type="CREDIT",
                        )

                        wallet_payload = {
                            "transaction_from": "PAYSTACK_FUNDING",
                        }

                        UserWallet.fund_wallet(
                            user=user_profile,  # noqa
                            amount=int(amount),
                            channel="WEB",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="GAME_PLAY_WALLET",
                            **wallet_payload,
                        )

                        # notify admin
                        notify_admin_of_user_funding.delay(
                            amount=int(instance.amount),
                            phone_number=instance.user.phone_number,
                            channel="PAYSTACK",
                        )

                    # update paystack model instance
                    instance.is_verified = True
                    instance.status = "SUCCESSFUL"
                    instance.save()

                    """
                    Check if payment reference is ends with 's4l' or 'wc'
                    s4l: single 4 life
                    wc: whyse cash
                    if the transaction referrence ends with one of this string.
                    re-rouute this function call to lottery payment
                    """

                    if instance.reference.endswith("s4l"):
                        _wallet = UserWallet.objects.filter(user=instance.user, wallet_tag="WEB").last()
                        amount = instance.amount

                        db_id = instance.reference.split("-")[-2]
                        lottery_instance = LottoTicket.objects.get(id=int(db_id))
                        lottery_qs = LottoTicket.objects.filter(game_play_id=lottery_instance.game_play_id)

                        return UserWallet.lottery_payment(_wallet, amount, lottery_qs, lottery_instance)

                    elif instance.reference.endswith("wc"):
                        _wallet = UserWallet.objects.filter(user=instance.user, wallet_tag="WEB").last()
                        amount = instance.amount

                        db_id = instance.reference.split("-")[-2]
                        lottery_instance = LotteryModel.objects.get(id=int(db_id))
                        lottery_qs = LotteryModel.objects.filter(game_play_id=lottery_instance.game_play_id)

                        return UserWallet.lottery_payment(_wallet, amount, lottery_qs, lottery_instance)

                    # play lottery for main whyse cash lottery
                    """
                    The reason why i'm re-initializing userwallet is because i want the initial value of
                    game_available_balance not to be the same. so that the transaction will be recorded as debit
                    """
                    _wallet = UserWallet.objects.filter(user=instance.user, wallet_tag="WEB").last()

                    _user_lottery = LotteryModel.objects.filter(user_profile=instance.user, paid=False).last()
                    if _user_lottery is None:
                        return {"message": "No lottery found"}
                    else:
                        if _user_lottery.expected_amount < 1:
                            return {"message": "No lottery found"}
                        else:
                            is_active_batch = _user_lottery.batch.is_active
                            if is_active_batch is False:
                                return {"message": "Batch is not active"}

                            amount = _user_lottery.expected_amount
                            if _wallet.game_available_balance <= amount:
                                # combine game_available_balance and withdrawable_available_balance
                                play_amount = _wallet.game_available_balance + _wallet.withdrawable_available_balance

                                if play_amount <= amount:
                                    # since user does not have enough money to play the lottery
                                    # we'll dedeuct the amount he funded from his game play wallet
                                    # and match it to lottery and play it for him

                                    _play_amount = _wallet.game_available_balance

                                    filter_user_lottery = LotteryModel.objects.filter(game_play_id=_user_lottery.game_play_id)

                                    amount_to_deduct_from_wallet = 0

                                    for _lottery in filter_user_lottery:
                                        _lottery_amount = LotteryModel.determine_stake_for_payment_collection(_lottery)
                                        if _play_amount < _lottery_amount:
                                            continue
                                        else:
                                            _lottery.paid = True
                                            _lottery.save()

                                            amount_to_deduct_from_wallet += _lottery_amount

                                            # update lottery_expected_amount in redis
                                            redis_db = RedisStorage(_lottery.batch.batch_uuid)
                                            get_redis_item = redis_db.get_data()
                                            if get_redis_item is not None:
                                                lottery_expected_amount = float(get_redis_item.decode("utf-8"))

                                                # update redis item
                                                redis_db.set_data(lottery_expected_amount + _lottery_amount)
                                            else:
                                                redis_db.set_data(_lottery_amount)

                                            _play_amount -= _lottery_amount
                                            if _play_amount < 1:
                                                break

                                    if amount_to_deduct_from_wallet > 0:
                                        _wallet.game_available_balance -= amount_to_deduct_from_wallet
                                        _wallet.save()
                                        return {"message": "payment verified successfully"}

                            else:
                                amount_to_deduct_from_wallet = _user_lottery.expected_amount

                                pay_for_lottery = LotteryModel.spread_lottery_amount(instance.user, amount_to_deduct_from_wallet)
                                if pay_for_lottery:
                                    _wallet.game_available_balance -= amount_to_deduct_from_wallet
                                    _wallet.save()

                                    redis_db = RedisStorage(_user_lottery.batch.batch_uuid)
                                    get_redis_item = redis_db.get_data()
                                    if get_redis_item is not None:
                                        lottery_expected_amount = float(get_redis_item.decode("utf-8"))

                                        # update redis item
                                        redis_db.set_data(lottery_expected_amount + amount_to_deduct_from_wallet)
                                    else:
                                        redis_db.set_data(amount_to_deduct_from_wallet)

                                    return {"message": "payment verified successfully"}

                elif payment_for == "MOBILE_LOTTERY_PAYMENT":
                    agent_phone = instance.user.phone_number

                    agent_wallet = AgentWallet.objects.filter(agent__phone=agent_phone).last()

                    # get user lottery using id
                    # we'll check two different models if the id is doesn't exist in one model

                    _db_id = instance.reference.split("-")[-1]

                    lotte_ticket = LottoTicket.objects.filter(id=int(_db_id))

                    try:
                        if not lotte_ticket.exists():
                            raise LottoTicket.DoesNotExist
                        else:
                            lotto_ticket_instance = lotte_ticket.last()

                            if agent_wallet:
                                agent_wallet.game_play_bal += instance.amount
                                agent_wallet.save()

                            else:
                                agent = Agent.objects.filter(phone=agent_phone).last()
                                AgentWallet.objects.create(
                                    agent=agent,
                                    game_play_bal=instance.amount,
                                    agent_name=agent.name,
                                    agent_phone_number=agent.phone,
                                    agent_email=agent.email,
                                )

                            # update paystack model instance
                            instance.is_verified = True
                            instance.status = "SUCCESSFUL"
                            instance.save()

                            _wallet = AgentWallet.objects.filter(agent__phone=agent_phone).last()

                            if lotto_ticket_instance.expected_amount < 1:
                                return {"message": "No lottery found"}

                            else:
                                _batch_instance = LotteryBatch.objects.filter(batch_uuid=lotto_ticket_instance.batch.batch_uuid).last()
                                if _batch_instance.is_active is False:
                                    _wallet.game_play_bal = transaction_amount
                                    _wallet.save()

                                    return {"message": "Batch is not active"}

                                if _wallet.game_play_bal < lotto_ticket_instance.expected_amount:
                                    # combine game_available_balance and withdrawable_available_balance
                                    play_amount = _wallet.game_play_bal + _wallet.withdrawable_available_bal

                                    if play_amount < lotto_ticket_instance.expected_amount:
                                        # since user does not have enough money to play the lottery
                                        # we'll dedeuct the amount he funded from his game play wallet
                                        # and match it to lottery and play it for him

                                        _lottery_qs = LottoTicket.objects.filter(game_play_id=lotto_ticket_instance.game_play_id)
                                        amount_to_deduct_from_wallet = 0
                                        for i in _lottery_qs:
                                            i.paid = True
                                            i.save()

                                            play_amount -= 100
                                            amount_to_deduct_from_wallet += 100

                                            if play_amount < 1:
                                                break

                                        if amount_to_deduct_from_wallet > 0:
                                            _wallet.game_play_bal -= amount_to_deduct_from_wallet
                                            _wallet.save()

                                            return {"message": "payment verified successfully"}

                                else:
                                    amount_to_deduct_from_wallet = lotto_ticket_instance.expected_amount

                                    LottoTicket.objects.filter(game_play_id=lotto_ticket_instance.game_play_id).update(paid=True)

                                    _wallet.game_play_bal -= amount_to_deduct_from_wallet
                                    _wallet.save()

                                    redis_db = RedisStorage(lotto_ticket_instance.batch.batch_uuid)
                                    get_redis_item = redis_db.get_data()
                                    if get_redis_item is not None:
                                        lottery_expected_amount = float(get_redis_item.decode("utf-8"))

                                        # update redis item
                                        redis_db.set_data(lottery_expected_amount + amount_to_deduct_from_wallet)
                                    else:
                                        redis_db.set_data(amount_to_deduct_from_wallet)

                                    return {"message": "payment verified successfully"}

                    except LottoTicket.DoesNotExist:
                        lottery = LotteryModel.objects.filter(id=int(_db_id))

                        if not lottery.exists():
                            return {"message": "No lottery found"}

                        _lottery_instance = lottery.last()

                        if agent_wallet:
                            agent_wallet.game_play_bal += instance.amount
                            agent_wallet.save()

                            # notify admin
                            notify_admin_of_user_funding.delay(
                                amount=int(instance.amount),
                                phone_number=instance.user.phone_number,
                                channel="PAYSTACK",
                            )
                        else:
                            agent = Agent.objects.filter(phone=agent_phone).last()
                            AgentWallet.objects.create(
                                agent=agent,
                                game_play_bal=instance.amount,
                                agent_name=agent.name,
                                agent_phone_number=agent.phone,
                                agent_email=agent.email,
                            )

                        # update paystack model instance
                        instance.is_verified = True
                        instance.status = "SUCCESSFUL"
                        instance.save()

                        _wallet = AgentWallet.objects.select_related("agent").filter(agent__phone=agent_phone).last()

                        if _lottery_instance.expected_amount < 1:
                            return {"message": "No lottery found"}

                        _batch_instance = LotteryBatch.objects.filter(batch_uuid=_lottery_instance.batch.batch_uuid).last()

                        if _batch_instance.is_active is False:
                            _wallet.game_play_bal = transaction_amount
                            _wallet.save()

                            return {"message": "Batch is not active"}

                        if _wallet.game_play_bal < _lottery_instance.expected_amount:
                            # combine game_available_balance and withdrawable_available_balance
                            play_amount = _wallet.game_play_bal + _wallet.withdrawable_available_bal

                            if play_amount < _lottery_instance.expected_amount:
                                # since user does not have enough money to play the lottery
                                # we'll dedeuct the amount he funded from his game play wallet
                                # and match it to lottery and play it for him

                                _lottery_qs = LotteryModel.objects.filter(game_play_id=_lottery_instance.game_play_id)
                                amount_to_deduct_from_wallet = 0
                                for i in _lottery_qs:
                                    i.paid = True
                                    i.save()

                                    play_amount -= 100
                                    amount_to_deduct_from_wallet += 100

                                    if play_amount < 1:
                                        break

                                if amount_to_deduct_from_wallet > 0:
                                    _wallet.game_play_bal -= amount_to_deduct_from_wallet
                                    _wallet.save()

                                    return {"message": "payment verified successfully"}

                        else:
                            amount_to_deduct_from_wallet = _lottery_instance.expected_amount

                            LotteryModel.objects.filter(game_play_id=_lottery_instance.game_play_id).update(paid=True, paid_date=timezone.now())

                            _wallet.game_play_bal -= amount_to_deduct_from_wallet
                            _wallet.save()

                            redis_db = RedisStorage(_lottery_instance.batch.batch_uuid)
                            get_redis_item = redis_db.get_data()
                            if get_redis_item is not None:
                                lottery_expected_amount = float(get_redis_item.decode("utf-8"))

                                # update redis item
                                redis_db.set_data(lottery_expected_amount + amount_to_deduct_from_wallet)
                            else:
                                redis_db.set_data(amount_to_deduct_from_wallet)

                            return {"message": "payment verified successfully"}

                elif instance.payment_reason == "LOTTERY_PAYMENT":
                    user_profile = UserProfile.objects.filter(user__phone_number=instance.user.phone_number).last()
                    if not user_profile:
                        return {"message": "No user profile found"}

                    pending_lottery_payment = UssdLotteryPayment.objects.filter(
                        game_play_id__isnull=False,
                        user=user_profile,
                        is_successful=False,
                        is_verified=False,
                    ).last()

                    if not pending_lottery_payment:
                        return {"message": "No pending payment found"}

                    share_ussd_payment_across_lottery_pool(
                        user_profile.phone_number,
                        int(amount),
                        pending_lottery_payment.game_play_id,
                        transfrom="PAYSTACK_FUNDING",
                        transaction_unique_id=instance.reference,
                    )

                    return {"message": "payment verified successfully"}

            elif "iled" in verification_data.get("data").get("status").casefold():
                # update paystack model instance
                instance.is_verified = True
                instance.status = "FAILED"
                instance.save()

                return {"message": "payment failed"}

        return {"message": "payment not verified"}

    @staticmethod
    def fund_play_via_paystack(instance, data):
        """
        FUND WALLET VIA PAYSTACK
        """
        from wallet_app.tasks import notify_admin_of_user_funding

        instance.raw_data = data.get("raw_data")
        instance.bank = data.get("data", {}).get("authorization", {}).get("bank")
        instance.save()

        print("data", data, "\n\n")

        # verify payment
        verification_data = PaymentGateway().paystack_verify_payment(instance.reference)
        if isinstance(verification_data, dict):
            if verification_data.get("data").get("status").casefold() == "success" or verification_data.get("message") == "Verification successful":
                # tokenize card
                celery_tokenize_paystack_card.delay(instance.user.id, json.dumps(data.get("raw_data")))

                # fund user wallet
                get_fund_wallet = UserWallet.objects.filter(user=instance.user, wallet_tag="WEB").last()
                if get_fund_wallet:
                    # get_fund_wallet.game_available_balance += instance.amount
                    # get_fund_wallet.transaction_from = "PAYSTACK_FUNDING"
                    # get_fund_wallet.save()

                    debit_credit_record = DebitCreditRecord.create_record(
                        phone_number=instance.user.phone_number,
                        amount=int(instance.amount),
                        channel="WEB",
                        reference=instance.reference,
                        transaction_type="CREDIT",
                    )

                    wallet_payload = {
                        "transaction_from": "PAYSTACK_FUNDING",
                    }

                    UserWallet.fund_wallet(
                        user=instance.user,
                        amount=int(instance.amount),
                        channel="WEB",
                        transaction_id=debit_credit_record.reference,
                        user_wallet_type="GAME_PLAY_WALLET",
                        **wallet_payload,
                    )

                    # notify admin
                    notify_admin_of_user_funding.delay(
                        amount=int(instance.amount),
                        phone_number=instance.user.phone_number,
                        channel="PAYSTACK",
                    )
                else:
                    # user_wallet_instance = UserWallet.objects.create(
                    #     user=instance.user,
                    #     wallet_tag="WEB",
                    # )
                    # user_wallet_instance.game_available_balance += instance.amount
                    # get_fund_wallet.transaction_from = "PAYSTACK_FUNDING"
                    # user_wallet_instance.save()

                    # fund users wallet
                    debit_credit_record = DebitCreditRecord.create_record(
                        phone_number=instance.user.phone_number,
                        amount=int(instance.amount),
                        channel="WEB",
                        reference=instance.reference,
                        transaction_type="CREDIT",
                    )

                    wallet_payload = {
                        "transaction_from": "PAYSTACK_FUNDING",
                    }

                    UserWallet.fund_wallet(
                        user=instance.user,
                        amount=int(instance.amount),
                        channel="WEB",
                        transaction_id=debit_credit_record.reference,
                        user_wallet_type="GAME_PLAY_WALLET",
                        **wallet_payload,
                    )

                    # notify admin
                    notify_admin_of_user_funding.delay(
                        amount=int(instance.amount),
                        phone_number=instance.user.phone_number,
                        channel="PAYSTACK",
                    )

                # update paystack model instance
                instance.is_verified = True
                instance.status = "SUCCESSFUL"
                instance.save()

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #

                engage_event_payload = {
                    "event": "USER_WALLET_FUNDING",
                    "properties": {
                        "FUNDING_CHANNEL": "PAYSTACK",
                    },
                }

                lottery_play_engange_event.delay(
                    user_id=instance.user.id,
                    is_user_profile_id=True,
                    **engage_event_payload,
                )

                # ----------------------------------- ENGAGE EVENT ----------------------------------- #

            elif "iled" in verification_data.get("data").get("status").casefold():
                # update paystack model instance
                instance.is_verified = True
                instance.status = "FAILED"
                instance.save()

                return {"message": "payment failed"}

        return {"message": "payment not verified"}


class WebWinnersFundingTable(models.Model):
    """
    Web winners funding table
    """

    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    amount = models.FloatField()
    is_funded = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.id} {self.funded}"

    class Meta:
        verbose_name = "WEB WINNERS FUNDING TABLE"
        verbose_name_plural = "WEB WINNERS FUNDING TABLES"

    @staticmethod
    def create_web_winners_funding(user, amount):
        """
        Create web winners funding table
        """
        WebWinnersFundingTable.objects.create(user=user, amount=amount)

    @staticmethod
    def get_web_winners_funding(user):
        """
        Get web winners funding table
        """
        return WebWinnersFundingTable.objects.filter(user=user).last()

    @staticmethod
    def get_all_web_winners_funding():
        """
        Get all web winners funding table
        """
        return WebWinnersFundingTable.objects.all()

    @staticmethod
    def get_web_winners_funding_amount(user):
        """
        Get web winners funding table amount
        """
        return WebWinnersFundingTable.objects.filter(user=user).last().amount

    @staticmethod
    def update_web_winners_funding(user, amount):
        """
        Update web winners funding table
        """
        web_winners_funding = WebWinnersFundingTable.get_web_winners_funding(user)
        web_winners_funding.amount = amount
        web_winners_funding.save()

    @staticmethod
    def delete_web_winners_funding(user):
        """
        Delete web winners funding table
        """
        web_winners_funding = WebWinnersFundingTable.get_web_winners_funding(user)
        web_winners_funding.delete()


def credit_user_withdrawable_available_balance(sender, instance: WebWinnersFundingTable, created, **kwargs):
    if created:
        user_wallet = UserWallet.objects.filter(user=instance.user, wallet_tag="WEB").last()

        if user_wallet:
            user_wallet.withdrawable_available_balance += instance.amount
            user_wallet.transaction_from = "WINNINGS"
            user_wallet.save()

            instance.is_funded = True
            instance.save()

        else:
            UserWallet.objects.create(
                user=instance.user,
                withdrawable_available_balance=instance.amount,
                wallet_tag="WEB",
            )

            instance.is_funded = True
            instance.save()


post_save.connect(credit_user_withdrawable_available_balance, sender=WebWinnersFundingTable)


class WithdrawalTable(models.Model):
    WOVEN = "WOVEN"

    DISBURSEMENT_WALLET_CHOICES = [
        (WOVEN, "WOVEN"),
    ]

    CHANNEL = [
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS", "POS"),
    ]

    source = models.CharField(max_length=300, choices=DISBURSEMENT_WALLET_CHOICES, default=WOVEN)
    amount = models.FloatField()
    phone = models.CharField(max_length=150)
    payout_trans_ref = models.CharField(max_length=300)
    disbursed = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    bank_code = models.CharField(max_length=200)
    bank_name = models.CharField(max_length=200)
    source_unique_ref = models.CharField(max_length=300, null=True, blank=True)
    name = models.CharField(max_length=300)
    payout_verified = models.BooleanField(default=False)
    payout_payload = models.TextField(null=True, blank=True)
    source_response_payload = models.TextField(null=True, blank=True)
    verification_response_payload = models.TextField(null=True, blank=True)
    channel = models.CharField(max_length=300, choices=CHANNEL, default="WEB")
    agent_wallet_type = models.CharField(max_length=300, null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "WITGHRAWAL TABLE"
        verbose_name_plural = "WITHDRAWAL TABLES"


class GeneralWithdrawableWallet(models.Model):
    """
    This is the general withdrawable wallet
    """

    amount = models.FloatField(default=0)
    previous_balance = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    phone = None
    game_type = None

    # def __init__(self, *args, **kwargs):
    #     super(GeneralWithdrawableWallet, self).__init__(*args, **kwargs)
    #     self.__original_amount = self.amount

    def __str__(self):
        return f"{self.amount}"

    class Meta:
        verbose_name = "MAIN PAYOUT WALLET"
        verbose_name_plural = "MAIN PAYOUT WALLETS"

    def save(self, *args, **kwargs):
        phone_number = self.phone if self.phone else "None"
        game_type = self.game_type if self.game_type else "None"

        # print(phone_number, game_type)
        # print("\n\n\n\n\n\n")

        if self.pk:
            __original_amount = 0.00
            # ------------------- GETTING OLD VALUES ------------------- #

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "amount":
                    __original_amount = field.value_from_object(old)

            # ------------------- GETTING OLD VALUES ENDS HERE ------------------- #
            print("Amount::", self.amount, "Previous::", self.previous_balance)
            if self.amount != __original_amount:
                if self.amount > __original_amount:
                    self.previous_balance = __original_amount
                    GeneralWithdrawableWalletTransaction.create_wallet_transaction(
                        amount=self.amount - __original_amount,
                        previous_balance=__original_amount,
                        phone_number=phone_number,
                        game_type=game_type,
                        transaction_type="CREDIT",
                    )

                elif self.amount < __original_amount:
                    self.previous_balance = __original_amount
                    GeneralWithdrawableWalletTransaction.create_wallet_transaction(
                        amount=self.amount - __original_amount,
                        previous_balance=__original_amount,
                        phone_number=phone_number,
                        game_type=game_type,
                        transaction_type="DEBIT",
                    )
        try:
            return super(GeneralWithdrawableWallet, self).save(*args, **kwargs)
        except Exception as err:
            print(err)
            raise Exception("Negative Balance Error")

    @classmethod
    def add_fund(cls, amount, phone, game_type):
        # print("add_fund(cls, amount, phone, game_type)", amount, phone, game_type)
        withdrawable_wallet = cls.objects.last()
        if withdrawable_wallet:
            withdrawable_wallet.amount += amount
            withdrawable_wallet.phone = phone
            withdrawable_wallet.game_type = game_type
            withdrawable_wallet.save()
        else:
            cls.objects.create(amount=amount)

    @classmethod
    def deduct_fund(cls, amount, phone):
        withdrawable_wallet = cls.objects.last()
        if withdrawable_wallet:
            withdrawable_wallet.amount -= int(amount)
            withdrawable_wallet.phone = phone
            withdrawable_wallet.save()

    @classmethod
    def get_withdrawable_wallet(cls):
        return cls.objects.last()

    @classmethod
    def get_withdrawable_wallet_amount(cls):
        return 0 if cls.objects.last() is None else cls.objects.last().amount

    @classmethod
    def update_withdrawable_wallet(cls, amount):
        withdrawable_wallet = cls.get_withdrawable_wallet()
        withdrawable_wallet.amount = amount
        withdrawable_wallet.save()


class GeneralWithdrawableWalletTransaction(models.Model):
    """
    This is the general withdrawable wallet transaction
    """

    TRANSACTION_TYPE = [
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
        ("REVERSAL", "REVERSAL"),
    ]

    amount = models.FloatField(default=0)
    previous_balance = models.FloatField(default=0)
    phone_number = models.CharField(max_length=150)
    game_type = models.CharField(max_length=150, null=True, blank=True)
    transaction_type = models.CharField(max_length=150, choices=TRANSACTION_TYPE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def create_wallet_transaction(cls, amount, previous_balance, phone_number, game_type, transaction_type):
        """
        Create wallet transaction
        """
        print(
            dict(
                amount=amount,
                previous_balance=previous_balance,
                phone_number=phone_number,
                game_type=game_type,
                transaction_type=transaction_type,
            )
        )
        cls.objects.create(
            amount=amount,
            previous_balance=previous_balance,
            phone_number=phone_number,
            game_type=game_type,
            transaction_type=transaction_type,
        )

    class Meta:
        verbose_name = "MAIN PAYOUT WALLET TRANSACTION"
        verbose_name_plural = "MAIN PAYOUT WALLET TRANSACTIONS"


class BankTransferFunding(models.Model):
    """
    This is the bank transfer model
    """

    BANK_TRANSFER_STATUS = [
        ("PENDING", "PENDING"),
        ("SUCCESSFUL", "SUCCESSFUL"),
        ("FAILED", "FAILED"),
    ]

    amount = models.FloatField(default=0)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    bank_account_number = models.CharField(max_length=150, null=True, blank=True)
    phone = models.CharField(max_length=150, null=True, blank=True)
    status = models.CharField(max_length=150, choices=BANK_TRANSFER_STATUS, default="PENDING")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.phone

    class Meta:
        verbose_name = "BANK TRANSFER FUNDING"
        verbose_name_plural = "BANK TRANSFER FUNDINGS"

    @classmethod
    def create_bank_transfer(cls, amount, bank_name, bank_account_number, phone):
        """
        Create bank transfer
        """

        cls.objects.create(
            amount=amount,
            bank_name=bank_name,
            bank_account_number=bank_account_number,
            phone=phone,
        )

    @classmethod
    def get_bank_transfer(cls, phone):
        """
        Get bank transfer
        """

        return cls.objects.filter(phone=phone).last()

    @classmethod
    def update_bank_transfer_status(cls, phone, status):
        """
        Update bank transfer status
        """

        bank_transfer = cls.get_bank_transfer(phone)
        if bank_transfer:
            bank_transfer.status = status
            bank_transfer.save()


class FloatWallet(models.Model):
    """
    This is the float wallet
    """

    WALLET_SOURCE = (
        ("VFD", "VFD"),
        ("WOVEN", "WOVEN"),
        ("AGENT_FUNDING_WALLET", "AGENT_FUNDING_WALLET"),
        ("NON_RETAIL_WALLET", "NON_RETAIL_WALLET"),
        ("RETAIL_RTP_WALLET", "RETAIL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "GHANA_RTP_WALLET"),
        ("RTO_WALLET", "RTO_WALLET"),
    )

    amount = models.FloatField(default=0)
    previous_balance = models.FloatField(default=0)
    source = models.CharField(max_length=150, choices=WALLET_SOURCE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.source

    class Meta:
        verbose_name = "FLOAT WALLET"
        verbose_name_plural = "FLOAT WALLETS"

    def save(self, *args, **kwargs):
        if self.pk:
            __original_amount = 0
            # ------------------- GETTING OLD VALUES ------------------- #

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "amount":
                    __original_amount = field.value_from_object(old)

            # ------------------- GETTING OLD VALUES ENDS HERE ------------------- #

            if __original_amount != self.amount:
                self.previous_balance = __original_amount

        return super(FloatWallet, self).save(*args, **kwargs)

    @classmethod
    def get_float_wallet(cls, source):
        """
        Get float wallet
        """

        instance = cls.objects.filter(source=source).last()
        if instance is None:
            cls.objects.create(amount=0, source=source)
            return cls.objects.last()
        return instance

    @classmethod
    def update_float_wallet(cls):
        from wallet_app.tasks import celery_update_float_wallet

        celery_update_float_wallet.delay()


class CommissionWallet(models.Model):
    """
    This is the commission wallet
    """

    amount = models.FloatField(default=0)
    previous_balance = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    phone = None

    def __str__(self):
        return "Commission Wallet"

    class Meta:
        verbose_name = "COMMISSION WALLET"
        verbose_name_plural = "COMMISSION WALLETS"

    def save(self, *args, **kwargs):
        if self.pk:
            __original_amount = 0
            # ------------------- GETTING OLD VALUES ------------------- #

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "amount":
                    __original_amount = field.value_from_object(old)

            # ------------------- GETTING OLD VALUES ENDS HERE ------------------- #

            if __original_amount != self.amount:
                self.previous_balance = __original_amount

                if self.amount > __original_amount:
                    CommissionWalletTransaction.create_commission_wallet_transaction(
                        amount=self.amount - __original_amount,
                        previous_balance=__original_amount,
                        phone_number=self.phone,
                        transaction_type="CREDIT",
                    )
                else:
                    CommissionWalletTransaction.create_commission_wallet_transaction(
                        amount=__original_amount - self.amount,
                        previous_balance=__original_amount,
                        phone_number=self.phone,
                        transaction_type="DEBIT",
                    )

        return super(CommissionWallet, self).save(*args, **kwargs)

    @classmethod
    def get_commission_wallet(cls):
        """
        Get commission wallet
        """

        return cls.objects.last()

    @classmethod
    def get_commission_wallet_amount(cls):
        return 0 if cls.objects.last() is None else cls.objects.last().amount

    @classmethod
    def update_commission_wallet_amount(cls, amount, phone_number):
        if cls.objects.last() is None:
            cls.objects.create(amount=0)

        commission_wallet = cls.objects.all().last()

        commission_wallet.amount += amount
        commission_wallet.phone = phone_number
        commission_wallet.save()


class CommissionWalletTransaction(models.Model):
    """
    This is the commission wallet transaction
    """

    TRANSACTION_TYPE = [
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
    ]

    amount = models.FloatField(default=0)
    phone_number = models.CharField(max_length=150, null=True, blank=True)
    previous_balance = models.FloatField(default=0)
    transaction_type = models.CharField(max_length=150, choices=TRANSACTION_TYPE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def create_commission_wallet_transaction(cls, amount, previous_balance, transaction_type, phone_number):
        """
        Create commission wallet transaction
        """

        cls.objects.create(
            amount=amount,
            previous_balance=previous_balance,
            transaction_type=transaction_type,
            phone_number=phone_number,
        )

    class Meta:
        verbose_name = "COMMISSION WALLET TRANSACTION"
        verbose_name_plural = "COMMISSION WALLET TRANSACTIONS"


class RtoWallet(models.Model):
    """
    This is the rto wallet
    """

    amount = models.FloatField(default=0)
    previous_balance = models.FloatField(default=0)
    amount_rewarded = models.FloatField(default=0)
    telco_amount = models.FloatField(default=0)
    previous_telco_amount = models.FloatField(default=0)
    telco_amount_rewarded = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    phone = None

    def __str__(self):
        return "RTO Wallet"

    class Meta:
        verbose_name = "RTO WALLET"
        verbose_name_plural = "RTO WALLETS"

    # def save(self, *args, **kwargs):
    #     if self.pk:
    #         __original_amount = 0
    #         # ------------------- GETTING OLD VALUES ------------------- #

    #         old = self.__class__.objects.get(pk=self._get_pk_val())
    #         for field in self.__class__._meta.fields:
    #             if field.name == "amount":
    #                 __original_amount = field.value_from_object(old)

    #         # ------------------- GETTING OLD VALUES ENDS HERE ------------------- #

    #         if __original_amount != self.amount:
    #             self.previous_balance = __original_amount

    #             if self.amount > __original_amount:
    #                 RtoWalletTransaction.create_rto_wallet_transaction(
    #                     amount=self.amount - __original_amount,
    #                     previous_balance=__original_amount,
    #                     phone_number=self.phone,
    #                     transaction_type="CREDIT",
    #                 )
    #             else:
    #                 RtoWalletTransaction.create_rto_wallet_transaction(
    #                     amount=__original_amount - self.amount,
    #                     previous_balance=__original_amount,
    #                     phone_number=self.phone,
    #                     transaction_type="DEBIT",
    #                 )

    #     return super(RtoWallet, self).save(*args, **kwargs)

    @classmethod
    def get_rto_wallet(cls):
        """
        Get rto wallet
        """

        return cls.objects.last()

    @classmethod
    def get_rto_wallet_amount(cls):
        return 0 if cls.objects.last() is None else cls.objects.last().amount

    @classmethod
    def update_rto_wallet_amount(cls, amount, phone_number, wallet_type="DEFAULT"):
        cls.fund_wallet(amount, phone_number, wallet_type)

    @classmethod
    def fund_wallet(cls, amount, phone, wallet_type="DEFAULT"):
        object_instance = cls.objects.last()
        if not object_instance:
            object_instance = cls.objects.create(amount=0, telco_amount=0)

        if wallet_type == "DEFAULT":
            balence_before = object_instance.amount
            balance_after = balence_before + amount

            object_instance.amount = balance_after
            object_instance.previous_balance = balence_before
            object_instance.save()

            RtoWalletTransaction.create_rto_wallet_transaction(
                amount=amount,
                previous_balance=balance_after,
                phone_number=phone,
                transaction_type="CREDIT",
                type_of_rto=wallet_type,
            )

        elif wallet_type == "TELCO":
            balence_before = object_instance.telco_amount
            balance_after = balence_before + amount

            object_instance.telco_amount = balance_after
            object_instance.previous_telco_amount = balence_before
            object_instance.save()

            RtoWalletTransaction.create_rto_wallet_transaction(
                amount=amount,
                previous_balance=balance_after,
                phone_number=phone,
                transaction_type="CREDIT",
                type_of_rto=wallet_type,
            )

        else:
            return "Invalid wallet type"

    @classmethod
    def deduct_wallet(cls, amount, phone=None, wallet_type="DEFAULT"):
        object_instance = cls.objects.last()
        if wallet_type == "DEFAULT":
            balence_before = object_instance.amount
            balance_after = balence_before - amount

            object_instance.amount = balance_after
            object_instance.previous_balance = balence_before
            object_instance.amount_rewarded += amount
            object_instance.save()

            RtoWalletTransaction.create_rto_wallet_transaction(
                amount=amount,
                previous_balance=balance_after,
                phone_number=phone,
                transaction_type="DEBIT",
                type_of_rto=wallet_type,
            )

        elif wallet_type == "TELCO":
            balence_before = object_instance.telco_amount
            balance_after = balence_before - amount

            object_instance.telco_amount = balance_after
            object_instance.previous_telco_amount = balence_before
            object_instance.telco_amount_rewarded += amount
            object_instance.save()

            RtoWalletTransaction.create_rto_wallet_transaction(
                amount=amount,
                previous_balance=balance_after,
                phone_number=phone,
                transaction_type="DEBIT",
                type_of_rto=wallet_type,
            )

        else:
            return "Invalid wallet type"


class RtoWalletTransaction(models.Model):
    """
    This is the rto wallet transaction
    """

    TRANSACTION_TYPE = [
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
    ]

    TYPE_OF_RTO = (
        ("TELCO", "TELCO"),
        ("DEFAULT", "DEFAULT"),
    )

    amount = models.FloatField(default=0)
    phone_number = models.CharField(max_length=150, null=True, blank=True)
    previous_balance = models.FloatField(default=0)
    transaction_type = models.CharField(max_length=150, choices=TRANSACTION_TYPE)
    type_of_rto = models.CharField(max_length=150, choices=TYPE_OF_RTO, default="DEFAULT")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def create_rto_wallet_transaction(
        cls,
        amount,
        previous_balance,
        transaction_type,
        phone_number,
        type_of_rto="DEFAULT",
    ):
        """
        Create rto wallet transaction
        """

        cls.objects.create(
            amount=amount,
            previous_balance=previous_balance,
            transaction_type=transaction_type,
            phone_number=phone_number,
            type_of_rto=type_of_rto,
        )

    class Meta:
        verbose_name = "RTO WALLET TRANSACTION"
        verbose_name_plural = "RTO WALLET TRANSACTIONS"


class VirtualSoccerExcessWallet(models.Model):
    amount = models.FloatField(default=0)
    previous_balance = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    phone = None

    def __str__(self):
        return "Virtual Soccer Excess Wallet"

    class Meta:
        verbose_name = "VIRTUAL SOCCER EXCESS WALLET"
        verbose_name_plural = "VIRTUAL SOCCER EXCESS WALLETS"

    def save(self, *args, **kwargs):
        if self.pk:
            __original_amount = 0
            # ------------------- GETTING OLD VALUES ------------------- #

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "amount":
                    __original_amount = field.value_from_object(old)

            # ------------------- GETTING OLD VALUES ENDS HERE ------------------- #

            if __original_amount != self.amount:
                self.previous_balance = __original_amount

                if self.amount > __original_amount:
                    VirtualSoccerExcessWalletTransaction.create_virtual_soccer_excess_wallet_transaction(
                        amount=self.amount - __original_amount,
                        previous_balance=__original_amount,
                        phone_number=self.phone,
                        transaction_type="CREDIT",
                    )
                else:
                    VirtualSoccerExcessWalletTransaction.create_virtual_soccer_excess_wallet_transaction(
                        amount=__original_amount - self.amount,
                        previous_balance=__original_amount,
                        phone_number=self.phone,
                        transaction_type="DEBIT",
                    )

        return super(VirtualSoccerExcessWallet, self).save(*args, **kwargs)

    @classmethod
    def get_virtual_soccer_excess_wallet(cls):
        """
        Get virtual soccer excess wallet
        """

        return cls.objects.last()

    @classmethod
    def get_virtual_soccer_excess_wallet_amount(cls):
        return 0 if cls.objects.last() is None else cls.objects.last().amount

    @classmethod
    def update_virtual_soccer_excess_wallet_amount(cls, amount, phone_number):
        if cls.objects.last() is None:
            cls.objects.create(amount=0)

        virtual_soccer_excess_wallet = cls.objects.all().last()

        virtual_soccer_excess_wallet.amount += amount
        virtual_soccer_excess_wallet.phone = phone_number
        virtual_soccer_excess_wallet.save()


class VirtualSoccerExcessWalletTransaction(models.Model):
    TRANSACTION_TYPE = [
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
    ]

    amount = models.FloatField(default=0)
    phone_number = models.CharField(max_length=150, null=True, blank=True)
    previous_balance = models.FloatField(default=0)
    transaction_type = models.CharField(max_length=150, choices=TRANSACTION_TYPE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def create_virtual_soccer_excess_wallet_transaction(cls, amount, previous_balance, transaction_type, phone_number):
        """
        Create virtual soccer excess wallet transaction
        """

        cls.objects.create(
            amount=amount,
            previous_balance=previous_balance,
            transaction_type=transaction_type,
            phone_number=phone_number,
        )

    class Meta:
        verbose_name = "VIRTUAL SOCCER EXCESS WALLET TRANSACTION"
        verbose_name_plural = "VIRTUAL SOCCER EXCESS WALLET TRANSACTIONS"


class IllusionWallet(models.Model):
    amount = models.FloatField(default=0)
    previous_balance = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    phone = None
    game_type = None

    def save(self, *args, **kwargs):
        if self.pk:
            __original_amount = 0
            # ------------------- GETTING OLD VALUES ------------------- #

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "amount":
                    __original_amount = field.value_from_object(old)

            # ------------------- GETTING OLD VALUES ENDS HERE ------------------- #

            if __original_amount != self.amount:
                self.previous_balance = __original_amount

                if self.amount > __original_amount:
                    IllusionWalletTransaction.create_illusion_wallet_transaction(
                        amount=self.amount - __original_amount,
                        previous_balance=__original_amount,
                        transaction_type="CREDIT",
                    )
                else:
                    IllusionWalletTransaction.create_illusion_wallet_transaction(
                        amount=__original_amount - self.amount,
                        previous_balance=__original_amount,
                        phone_number=self.phone,
                        game_type=self.game_type,
                        transaction_type="DEBIT",
                    )
        return super(IllusionWallet, self).save(*args, **kwargs)

    @classmethod
    def add_illusion_wallet(cls, amount):
        if cls.objects.last() is None:
            cls.objects.create(amount=0)

        illusion_wallet = cls.objects.all().last()

        illusion_wallet.amount += amount
        illusion_wallet.phone = None
        illusion_wallet.game_type = None
        illusion_wallet.save()

    @classmethod
    def deduct_illusion_wallet(cls, amount, phone, game_type):
        if cls.objects.last() is None:
            cls.objects.create(amount=0)
            return False

        illusion_wallet = cls.objects.all().last()

        if illusion_wallet.amount < amount:
            return False
        else:
            illusion_wallet.amount -= amount
            illusion_wallet.phone = phone
            illusion_wallet.game_type = game_type
            illusion_wallet.save()

            return True

    class Meta:
        verbose_name = "ILLUSION WALLET"
        verbose_name_plural = "ILLUSION WALLETS"


class IllusionWalletTransaction(models.Model):
    TRANSACTION_TYPE = [
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
    ]

    GAME_TYPE = (
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("BANKER", "BANKER"),
        ("QUIKA", "QUIKA"),
    )

    amount = models.FloatField(default=0)
    phone_number = models.CharField(max_length=150, null=True, blank=True)
    previous_balance = models.FloatField(default=0)
    transaction_type = models.CharField(max_length=150, choices=TRANSACTION_TYPE)
    game_type = models.CharField(max_length=150, choices=GAME_TYPE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def create_illusion_wallet_transaction(
        cls,
        amount,
        previous_balance,
        transaction_type,
        phone_number=None,
        game_type=None,
    ):
        """
        Create illusion wallet transaction
        """

        cls.objects.create(
            amount=amount,
            previous_balance=previous_balance,
            transaction_type=transaction_type,
            phone_number=phone_number,
            game_type=game_type,
        )

    class Meta:
        verbose_name = "ILLUSION WALLET TRANSACTION"
        verbose_name_plural = "ILLUSION WALLET TRANSACTIONS"


class DebtCollection(models.Model):
    WALLET_TYPE = (
        ("USER_PLAY_WALLET", "USER_PLAY_WALLET"),
        ("USER_WINNING_WALLET", "USER_WINNING_WALLET"),
        ("AGENT_PLAY_WALLET", "AGENT_PLAY_WALLET"),
        ("AGENT_WINNING_WALLET", "AGENT_WINNING_WALLET"),
    )
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    amount = models.FloatField(default=0)
    debt_after = models.FloatField(default=0)
    wallet_type = models.CharField(max_length=150, choices=WALLET_TYPE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.user.phone_number

    class Meta:
        verbose_name = "DEBT COLLECTION"
        verbose_name_plural = "DEBT COLLECTIONS"

    @classmethod
    def create_record(cls, user, amount, wallet_type, debt_after):
        cls.objects.create(user=user, amount=amount, wallet_type=wallet_type, debt_after=debt_after)


def get_debit_credit_record_id():
    return f"{uuid.uuid4()}-{int(time.time())}"


class DebitCreditRecord(models.Model):
    TRANSACTION_TYPE = (
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
        ("MANCALA_GAME_PLAY_RVSL", "MANCALA_GAME_PLAY_RVSL"),
    )

    CHANNEL = (
        ("POS/MOBILE", "POS/MOBILE"),
        ("WEB", "WEB"),
        ("USSD", "USSD"),
        ("REQUEST", "REQUEST"),
    )

    STATUS = (
        ("SUCCESS", "SUCCESS"),
        ("FAILED", "FAILED"),
        ("PENDING", "PENDING"),
    )

    phone_number = models.CharField(max_length=150)
    amount = models.FloatField(default=0)
    channel = models.CharField(max_length=150, choices=CHANNEL)
    reference = models.CharField(max_length=150, unique=True)
    transaction_type = models.CharField(max_length=150, choices=TRANSACTION_TYPE)
    debit_credit_record_id = models.CharField(max_length=300, default=get_debit_credit_record_id)
    status = models.CharField(max_length=150, choices=STATUS, default="PENDING")
    initial_request_payload = models.TextField(null=True, blank=True)
    response_payload = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "DEBIT CREDIT RECORD"
        verbose_name_plural = "DEBIT CREDIT RECORDS"

        indexes = [models.Index(fields=["debit_credit_record_id", "reference"])]

    @classmethod
    def create_record(cls, phone_number, amount, channel, reference, transaction_type):
        return cls.objects.create(
            phone_number=phone_number,
            amount=amount,
            channel=channel,
            reference=reference,
            transaction_type=transaction_type,
        )


class ServiceChargeWallet(models.Model):
    SERVICES = (("AFRICASTALKING", "AFRICASTALKING"), ("WOVEN", "WOVEN"))

    service = models.CharField(max_length=150, choices=SERVICES)
    amount = models.FloatField(default=0)
    previous_balance = models.FloatField(default=0)
    amount_resolved = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SERVICE CHARGE WALLET"
        verbose_name_plural = "SERVICE CHARGE WALLETS"

    def __str__(self):
        return self.service

    @classmethod
    def add_fund(cls, amount, service):
        if service not in [service[0] for service in cls.SERVICES]:
            pass
        else:
            # print("service", service, "adding funds", "amount", amount)
            service_wallet = cls.objects.filter(service=service).last()
            if service_wallet:
                previous_balance = service_wallet.amount
                balance_after = service_wallet.amount + amount

                service_wallet.amount += amount
                service_wallet.previous_balance = previous_balance
                service_wallet.save()

                ServiceChargeWalletTransaction.objects.create(
                    amount=amount,
                    service=service,
                    previous_balance=previous_balance,
                    balance_after=balance_after,
                    transaction_type="CREDIT",
                )

            else:
                previous_balance = 0
                balance_after = amount
                cls.objects.create(amount=amount, service=service)

                ServiceChargeWalletTransaction.objects.create(
                    amount=amount,
                    service=service,
                    previous_balance=previous_balance,
                    balance_after=balance_after,
                    transaction_type="CREDIT",
                )

    @classmethod
    def deduct_fund(cls, amount, service):
        if service not in [service[0] for service in cls.SERVICES]:
            pass
        else:
            service_wallet = cls.objects.filter(service=service).last()
            if service_wallet:
                previous_balance = service_wallet.amount
                balance_after = service_wallet.amount - amount

                service_wallet.amount -= amount
                service_wallet.amount_resolved += amount
                service_wallet.save()

                ServiceChargeWalletTransaction.objects.create(
                    amount=amount,
                    service=service,
                    previous_balance=previous_balance,
                    balance_after=balance_after,
                    transaction_type="DEBIT",
                )


class ServiceChargeWalletTransaction(models.Model):
    SERVICES = (("AFRICASTALKING", "AFRICASTALKING"), ("WOVEN", "WOVEN"))

    TRANSACTION_TYPE = (("DEBIT", "DEBIT"), ("CREDIT", "CREDIT"))

    service = models.CharField(max_length=150, choices=SERVICES)
    amount = models.FloatField(default=0)
    previous_balance = models.FloatField(default=0)
    balance_after = models.FloatField(default=0)
    transaction_type = models.CharField(max_length=150, choices=TRANSACTION_TYPE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SERVICE CHARGE WALLET TRANSACTION"
        verbose_name_plural = "SERVICE CHARGE WALLET TRANSACTIONS"

    def __str__(self):
        return self.service


class WithdrawalPINSystem(models.Model):
    GAME_TYPE = (("MANCALA", "MANCALA"),)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    phone_number = models.CharField(max_length=13)
    otp = models.CharField(max_length=4)
    # wallet_transaction = models.ForeignKey(WalletTransaction, on_delete=models.SET_NULL, null=True, blank=True)
    game_type = models.CharField(max_length=250, choices=GAME_TYPE, default="MANCALA")
    used = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    def save(self, *args, **kwargs):
        if not self.otp:
            # Generate a unique 4-digit OTP
            while True:
                otp = str(random.randint(1000, 9999))
                if not WithdrawalPINSystem.objects.filter(otp=otp, phone_number=self.phone_number).exists():
                    self.otp = otp
                    break
        super().save(*args, **kwargs)

    def mark_as_used(self):
        if not self.used:
            self.used = True
            self.is_active = False
            self.save()

    def __str__(self):
        return f"{self.phone_number}"

    class Meta:
        verbose_name = "WITHDRAWAL OTP"
        verbose_name_plural = "WITHDRAWAL OTP"

    @classmethod
    def get_active_otp(cls, otp, phone_number):
        return cls.objects.filter(otp=otp, phone_number=phone_number, is_active=True, used=False)

    @classmethod
    def create_otp_instance(cls, phone_number: str, game_type: str) -> str:
        """
        Create or retrieve an OTP (One-Time Password) instance for a user.

        Args:
            cls (class): The class containing the database model for OTP instances.
            phone_number (str): The user's phone number for whom the OTP is being created.
            game_type (str): The type of game or operation for which the OTP is required.

        Returns:
            str: The generated or retrieved OTP for the user.

        Example:
            otp = WithdrawalPINSystem.create_otp_instance(OTPManager, "1234567890", "registration")

        Note:
            This method creates or retrieves an OTP instance for a user with the provided
            phone number and game type. If an active, unused OTP instance exists for the
            user, it retrieves the last one. If not, it creates a new instance and returns
            the OTP associated with it.

        """
        otp_instance = cls.objects.filter(phone_number=phone_number, is_active=True, used=False)
        if otp_instance.exists():
            return otp_instance.last().otp
        else:
            new_instance_created = cls.objects.create(phone_number=phone_number, game_type=game_type)
            return new_instance_created.otp


class DebtRecovery(models.Model):
    """
    This is the debt recovery model
    """

    CHANNEL = [
        ("WEB", "WEB"),
        ("USSD", "USSD"),
        ("MOBILE", "MOBILE"),
    ]

    amount = models.FloatField(default=0)
    amount_recovered = models.FloatField(default=0)
    phone_number = models.CharField(max_length=150)
    full_name = models.CharField(max_length=150, null=True, blank=True)
    completed = models.BooleanField(default=False)
    channel = models.CharField(max_length=150, choices=CHANNEL)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.phone_number

    class Meta:
        verbose_name = "DEBT RECOVERY"
        verbose_name_plural = "DEBT RECOVERIES"

    @classmethod
    def create_debt_recovery(cls, amount, phone, full_name):
        """
        Create debt recovery
        """

        cls.objects.create(amount=amount, phone_number=phone, full_name=full_name)

    @classmethod
    def get_debt_recovery_instance(cls, phone):
        """
        Get debt recovery
        """
        try:
            return cls.objects.get(phone_number=phone, completed=False)
        except cls.DoesNotExist:
            return None
        return None

    @classmethod
    def update_debt_recovery_insatnce(cls, phone, amount, channel):
        """
        Update debt recovery status
        """

        debt_recovery = cls.get_debt_recovery_instance(phone)
        if debt_recovery is None:
            return

        debt_recovery.amount_recovered += amount
        if debt_recovery.amount_recovered >= debt_recovery.amount:
            debt_recovery.completed = True

        debt_recovery.save()

        # create transaction record
        DebtRecoveryTransaction.objects.create(phone_number=phone, amount=amount, channel=channel, full_name=debt_recovery.full_name)


class DebtRecoveryTransaction(models.Model):
    """
    This is the debt recovery model
    """

    CHANNEL = [
        ("WEB", "WEB"),
        ("USSD", "USSD"),
        ("MOBILE", "MOBILE"),
    ]

    phone_number = models.CharField(max_length=150)
    amount = models.FloatField(default=0)
    full_name = models.CharField(max_length=150, null=True, blank=True)
    channel = models.CharField(max_length=150, choices=CHANNEL)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.phone

    class Meta:
        verbose_name = "DEBT RECOVERY TRANSACTION"
        verbose_name_plural = "DEBT RECOVERY TRANSACTIONS"
