import json
import uuid
from datetime import datetime

from django.core.management.base import BaseCommand
from django.utils import timezone

from main.helpers.vfd_disbursement_helper import VfdDisbursementHelperFunc
from main.helpers.woven_manager import WovenHelper
from main.models import PayoutTransactionTable
from payout_process.models import PayoutProcessFirstStep
from pos_app.models import Agent, AgentWallet, LottoSuperAgentWallet, LottoSuperAgentWalletTransaction, PosLotteryWinners
from retail_metrics.models import PayoutAnalytics
from wallet_app.models import (
    DebitCreditRecord,
    GeneralWithdrawableWalletTransaction,
    UserWallet,
)
from wallet_system.models import Wallet
import ast

def disbursement_reversal(obj, reference, is_airtime=False):
    """
    Reverses a failed disbursement by crediting the user's wallet.
    
    This function handles reversals for both POS and web transactions.
    It credits the appropriate wallet based on the transaction channel
    and updates the general wallet accordingly.
    
    Args:
        obj (PayoutTransactionTable): The payout transaction object to reverse
        reference (str): Reference ID for the transaction
        is_airtime (bool, optional): Whether this is an airtime transaction. Defaults to False.
        
    Returns:
        bool: True if the reversal was successful, False otherwise
    """
    if obj.amount < 1:
        return True

    if obj.channel == "POS":
        agent_wallet = AgentWallet.objects.filter(agent__phone=obj.phone).last()

        if agent_wallet:
            print("GOT HERE")
            PayoutProcessFirstStep.update_payout_process_one_as_failed(payout_referece=obj.payout_trans_ref)
            # Create credit record for the reversal
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=agent_wallet.agent.phone,
                amount=obj.amount,
                channel="POS/MOBILE",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="CREDIT",
            )

            # Fund agent's wallet
            wallet_payload = {
                "transaction_from": "PAYOUT_REVERSAL",
            }


            print("agent_wallet.agent.terminal_id", agent_wallet.agent.terminal_id)
            if agent_wallet.agent.terminal_id is None:
                try:
                    res = UserWallet.fund_wallet(
                        user=agent_wallet.agent,
                        amount=obj.amount,
                        channel="POS",
                        transaction_id=f"{reference}-PAYOUT_REVERSAL",
                        user_wallet_type="WINNINGS_WALLET",
                        **wallet_payload,
                    )

                    print("res", res)
                except Exception as e:
                    print(f"Error funding agent wallet: {e}")
                    return False


            # Update lottery winners table
            update_pos_lottery_winners_table(ref=obj.payout_trans_ref)

            return True

        return False

    else:  # Web channel
        user_wallet = UserWallet.objects.filter(user__phone_number=obj.phone, wallet_tag="WEB").last()

        if user_wallet:
            # Create credit record for the reversal
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_wallet.user.phone_number,
                amount=obj.amount,
                channel="WEB",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="CREDIT",
            )

            # Determine transaction type based on whether it's airtime or not
            transaction_from = "REVERSAL"
            user_wallet_type = "WINNINGS_WALLET"

            if is_airtime:
                transaction_from = "AIRTIME_PURCHASE_REVERSAL"
                user_wallet_type = "AIRTIME_WALLET"

            wallet_payload = {
                "transaction_from": transaction_from,
            }

            # Fund user's wallet
            UserWallet.fund_wallet(
                user=user_wallet.user,
                amount=obj.amount,
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type=user_wallet_type,
                **wallet_payload,
            )

            return True

        # Refund the amount to general wallet if user wallet not found
        general_wallet = GeneralWithdrawableWalletTransaction.objects.last()
        general_wallet.amount += obj.amount
        general_wallet.transaction_type = "REVERSAL"
        general_wallet.save()

    return False


def update_pos_lottery_winners_table(ref, status="FAILED"):
    """
    Updates the POS lottery winners table based on payout status.
    
    Args:
        ref (str): Reference ID for the payout transaction
        status (str, optional): Status of the payout. Defaults to "FAILED".
    """
    
    pos_lottery_winner_instance = PosLotteryWinners.objects.filter(payout_ref=ref).last()
    if pos_lottery_winner_instance:
        if status == "SUCCESSFUL":
            PayoutProcessFirstStep.update_payout_process_one_as_successful(payout_referece=ref)

            pos_lottery_winner_instance.payout_successful = True
            pos_lottery_winner_instance.payout_verified = True
            pos_lottery_winner_instance.save()

        elif status == "FAILED":
            PayoutProcessFirstStep.update_payout_process_one_as_failed(payout_referece=ref)

            pos_lottery_winner_instance.payout_successful = False
            pos_lottery_winner_instance.payout_verified = False
            pos_lottery_winner_instance.is_win_claimed = False
            pos_lottery_winner_instance.withdrawl_initiated = False
            pos_lottery_winner_instance.save()


class Command(BaseCommand):
    """
    Django management command to re-verify unverified withdrawal transactions.
    
    This command checks the status of all unverified withdrawals and updates
    their status based on responses from payment providers (VFD, WOVEN, BUDDY, etc.).
    It also handles reversals for failed transactions.
    """
    help = "Re-verify unverified withdrawals and handle reversals for failed transactions"

    def handle_woven_transaction(self, obj):
        """
        Verify and handle a WOVEN transaction.
        
        Args:
            obj (PayoutTransactionTable): The transaction to verify
            
        Returns:
            bool: True if verification was completed, False otherwise
        """
        woven_gateway = WovenHelper()
        trans_details = woven_gateway.get_transaction_with_ref(obj.payout_trans_ref)

        if not isinstance(trans_details, dict):
            return False

        _response_data = trans_details.get("data", {}).get("payout_transactions")

        if not _response_data:  # Empty payout_transactions list - payment might not have been initiated
            current_date = timezone.now().date()
            diff = current_date - obj.date_added.date()

            if diff.days > 2:
                # Reverse the withdrawal after 2 days
                obj.is_verified = True
                obj.verification_response_payload = trans_details
                obj.status = "FAILED"
                obj.save()

                # Check if instance was successfully marked as verified
                the_same_instance = PayoutTransactionTable.objects.get(id=obj.id)
                if the_same_instance.is_verified is True:
                    disbursement_reversal(obj, the_same_instance.payout_trans_ref)

                update_pos_lottery_winners_table(ref=obj.payout_trans_ref)
                return True
        else:
            transaction_status = str(_response_data[0]["transaction_status"]).casefold()
            obj.source_unique_ref = _response_data[0]["unique_reference"]
            obj.verification_response_payload = trans_details
            
            if transaction_status == "active":
                obj.is_verified = True
                obj.disbursed = True
                obj.status = "SUCCESS"
                obj.save()

                update_pos_lottery_winners_table(ref=obj.payout_trans_ref, status="SUCCESSFUL")
                return True
                
            elif transaction_status == "failed":
                obj.is_verified = True
                obj.status = "FAILED"
                obj.save()

                the_same_instance = PayoutTransactionTable.objects.get(id=obj.id)
                if the_same_instance.is_verified is True:
                    disbursement_reversal(obj, the_same_instance.payout_trans_ref)

                update_pos_lottery_winners_table(ref=obj.payout_trans_ref)
                return True
            else:
                obj.save()
                return True
                
        return False

    def handle_vfd_transaction(self, obj):
        """
        Verify and handle a VFD transaction.
        
        Args:
            obj (PayoutTransactionTable): The transaction to verify
            
        Returns:
            bool: True if verification was completed, False otherwise
        """
        vfd_disbursement_helper = VfdDisbursementHelperFunc()
        print(f"Verifying payout... {obj.payout_trans_ref}")
        verify_response = vfd_disbursement_helper.verify_payout(reference=obj.payout_trans_ref)

        print(f"Verify response: {verify_response}")

        if isinstance(verify_response, dict):
            obj.verification_response_payload = verify_response
            obj.save()

            status_code = verify_response.get("data", {}).get("status_code")
            status_msg = verify_response.get("data", {}).get("status")
            is_reversed = verify_response.get("data", {}).get("reversed")

            print(f"Status code: {status_code}, Status msg: {status_msg}, Is reversed: {is_reversed}")

            if status_code == "00":
                print("SUCCESSFUL PAYOUT")
                obj.is_verified = True
                obj.disbursed = True
                obj.status = "SUCCESS"
                obj.save()

                update_pos_lottery_winners_table(
                    ref=obj.payout_trans_ref,
                    status="SUCCESSFUL",
                )
                return True
                
            elif (status_msg == "REVERSAL") and (is_reversed is True):
                print(f"REVERSAL FAILED FOR {obj.phone}, Ref: {obj.payout_trans_ref}")
                obj.is_verified = True
                obj.status = "FAILED"
                obj.unique_game_play_id = f"{obj.game_play_id}-{obj.id}-failed"
                obj.save()
                
                the_same_instance = PayoutTransactionTable.objects.get(id=obj.id)
                # print(f"Is verified: {the_same_instance.is_verified}")
                
                if the_same_instance.is_verified is True:
                    if obj.recipient_wallet == "USER_WALLET":
                        disbursement_reversal(obj, the_same_instance.payout_trans_ref)
                    else:
                        Wallet.fund_wallet(
                            wallet_type=obj.recipient_wallet,
                            amount = obj.amount,
                            is_reversal=True
                        )
                return True
                
            elif verify_response.get("status") == "error":
                obj.is_verified = True
                obj.status = "FAILED"
                obj.unique_game_play_id = f"{obj.game_play_id}-{obj.id}-failed"
                obj.save()
                
                the_same_instance = PayoutTransactionTable.objects.get(id=obj.id)
                print(f"Is verified: {the_same_instance.is_verified}")
                
                if the_same_instance.is_verified is True:
                    if the_same_instance.is_verified is True:
                        disbursement_reversal(obj, the_same_instance.payout_trans_ref)
                    else:
                        Wallet.fund_wallet(
                            wallet_type=obj.recipient_wallet,
                            amount = obj.amount,
                            is_reversal=True
                        )
                return True
            
        else:
            obj.verification_response_payload = verify_response
            obj.save()
            
        return False

    def handle_buddy_transaction(self, obj, failed_response_keywords):
        """
        Verify and handle a BUDDY transaction.
        
        Args:
            obj (PayoutTransactionTable): The transaction to verify
            failed_response_keywords (list): List of keywords indicating failed transactions
            
        Returns:
            bool: True if verification was completed, False otherwise
        """

        if "rto" in obj.payout_trans_ref:
            obj.is_verified = True
            obj.save()

            return True

        if obj.source_response_payload is None:
            # Try to verify using VFD helper as fallback
            vfd_disbursement_helper = VfdDisbursementHelperFunc()
            verify_payout_response = vfd_disbursement_helper.verify_payout(f"{obj.payout_trans_ref}")

            # print("verify_payout_response", verify_payout_response, "\n\n")

            obj.source_response_payload = verify_payout_response

            if isinstance(verify_payout_response, dict):
                status = verify_payout_response.get("data", {}).get("status", "")
                if status == "SUCCESSFUL":
                    obj.is_verified = True
                    obj.disbursed = True
                    obj.status = "SUCCESS"
                    obj.source_response_payload = verify_payout_response
                    obj.save()

                    try:
                        PayoutAnalytics.add_or_create_record(
                            amount = obj.amount
                        )
                    except:
                        pass

                    if obj.recipient_wallet == "USER_WALLET":
                        try:
                            update_pos_lottery_winners_table(
                                ref=obj.payout_trans_ref,
                                status="SUCCESSFUL",
                            )
                        except Exception:
                            return True
                    return True
                    
                else:

                    verify_payout_response = vfd_disbursement_helper.verify_payout(f"{obj.payout_trans_ref}-{obj.game_play_id}")
                    if isinstance(verify_payout_response, dict):
                        status = verify_payout_response.get("data", {}).get("status", "")
                        if status == "SUCCESSFUL":
                            obj.is_verified = True
                            obj.disbursed = True
                            obj.status = "SUCCESS"
                            obj.source_response_payload = verify_payout_response
                            obj.save()

                            if obj.recipient_wallet == "USER_WALLET":
                                try:
                                    update_pos_lottery_winners_table(
                                        ref=obj.payout_trans_ref,
                                        status="SUCCESSFUL",
                                    )
                                except Exception:
                                    return True
                            return True
                            
                        else:

                            obj.is_verified = True
                            obj.unique_game_play_id = f"{obj.game_play_id}-{obj.id}-failed"
                            obj.save()

                            try:
                                update_pos_lottery_winners_table(
                                    ref=obj.payout_trans_ref,
                                )
                            except Exception:
                                return True

                            return False
                    

                    return False

                
            return False

        # Parse the response payload
        _agency_buddy_response = str(obj.source_response_payload).replace("'", '"')
        try:
            parsed_response = json.loads(_agency_buddy_response)
        except Exception:
            try:
                parsed_response = json.loads(obj.source_response_payload)
            except Exception:
                try:
                    parsed_response = ast.literal_eval(obj.source_response_payload)
                except Exception:
                    parsed_response = obj.source_response_payload



        print("parsed_response", parsed_response, "\n\n") 

        failed_payout = [
        response for response in failed_response_keywords 
            if (response in obj.source_response_payload)
        ]
        
        if failed_payout:
            try:
                if obj.verification_response_payload is not None:
                    failed_payout = [
                        response for response in failed_response_keywords
                        if (response in obj.verification_response_payload)
                    ]
            except Exception:
                if len(failed_payout) <= 0:
                    failed_payout = []
        print("failed_payout", failed_payout, "\n\n")
        if failed_payout:
            obj.is_verified = True
            obj.status = "FAILED"
            obj.unique_game_play_id = f"{obj.game_play_id}-{obj.id}-failed"
            obj.save()

            the_same_instance = PayoutTransactionTable.objects.get(id=obj.id)
            if the_same_instance.is_verified is True:
                if obj.recipient_wallet == "USER_WALLET":
                    disbursement_reversal(obj, the_same_instance.payout_trans_ref)
                else:
                    Wallet.fund_wallet(
                        wallet_type=obj.recipient_wallet,
                        amount = obj.amount,
                        is_reversal=True
                    )
            return True
                


                    

            

        # Handle based on response message
        print("parsed_response", parsed_response)
        message = parsed_response.get("message", "")
        trans_status = parsed_response.get("message", "")
        if message == "Transaction completed successfully":
            obj.is_verified = True
            obj.disbursed = True
            obj.status = "SUCCESS"
            obj.save()
            if obj.recipient_wallet == "USER_WALLET":
                update_pos_lottery_winners_table(ref=obj.payout_trans_ref, status="SUCCESSFUL")
            
            return True
        elif trans_status == "success":
            obj.is_verified = True
            obj.disbursed = True
            obj.status = "SUCCESS"
            obj.save()
            if obj.recipient_wallet == "USER_WALLET":
                update_pos_lottery_winners_table(ref=obj.payout_trans_ref, status="SUCCESSFUL")
            
            return True
            
        # elif message in ["Insufficient balance", "TEST PAYOUT FROM LOTTO BACKEND"]:
        #     obj.is_verified = True
        #     obj.status = "FAILED"
        #     obj.unique_game_play_id = f"{obj.game_play_id}-{obj.id}-failed"
        #     obj.save()

        #     the_same_instance = PayoutTransactionTable.objects.get(id=obj.id)
        #     if the_same_instance.is_verified is True:
        #         if obj.recipient_wallet == "USER_WALLET":
        #             disbursement_reversal(obj, the_same_instance.payout_trans_ref)
        #         else:
        #             Wallet.fund_wallet(
        #                 wallet_type=obj.recipient_wallet,
        #                 amount = obj.amount,
        #                 is_reversal=True
        #             )
        #     return True
            
        else:
            # Check for other failure keywords
            if obj.source_response_payload is not None:
                failed_payout = [
                    response for response in failed_response_keywords 
                    if (response in obj.source_response_payload)
                ]
                
                if failed_payout:
                    try:
                        if obj.verification_response_payload is not None:
                            failed_payout = [
                                response for response in failed_response_keywords
                                if (response in obj.verification_response_payload)
                            ]
                    except Exception:
                        if len(failed_payout) <= 0:
                            failed_payout = []

                if failed_payout:
                    obj.is_verified = True
                    obj.status = "FAILED"
                    obj.unique_game_play_id = f"{obj.game_play_id}-{obj.id}-failed"
                    obj.save()

                    the_same_instance = PayoutTransactionTable.objects.get(id=obj.id)
                    if the_same_instance.is_verified is True:
                        if obj.recipient_wallet == "USER_WALLET":
                            disbursement_reversal(obj, the_same_instance.payout_trans_ref)
                        else:
                            Wallet.fund_wallet(
                                wallet_type=obj.recipient_wallet,
                                amount = obj.amount,
                                is_reversal=True
                            )
                    return True

            # Check for success in verification response
            if (obj.verification_response_payload is not None) and ("success" in obj.verification_response_payload):
                obj.is_verified = True
                obj.disbursed = True
                obj.status = "SUCCESS"
                obj.save()

                update_pos_lottery_winners_table(ref=obj.payout_trans_ref, status="SUCCESSFUL")
                return True
                
        return False

    def handle_liberty_pay_transaction(self, obj):
        """
        Verify and handle a LIBERTY_PAY transaction.
        
        Args:
            obj (PayoutTransactionTable): The transaction to verify
            
        Returns:
            bool: True if verification was completed, False otherwise
        """
        vfd_disbursement_helper = VfdDisbursementHelperFunc()
        verify_response = vfd_disbursement_helper.verify_payout(reference=obj.payout_trans_ref)

        if isinstance(verify_response, dict):
            obj.verification_response_payload = verify_response
            obj.save()

            if verify_response.get("status") == "error":
                obj.is_verified = True
                obj.status = "FAILED"
                obj.unique_game_play_id = f"{obj.game_play_id}-{obj.id}-failed"
                obj.save()
                
                # Uncomment if reversal should be performed for Liberty Pay
                # the_same_instance = PayoutTransactionTable.objects.get(id=obj.id)
                # if the_same_instance.is_verified is True:
                #     disbursement_reversal(obj, is_airtime=True)
                
                return True
        else:
            obj.verification_response_payload = verify_response
            obj.save()
            
        return False



    def handle_commission_transaction(self, obj):
        obj: PayoutTransactionTable = obj

        
        if obj.source_response_payload is not None:
            _agency_buddy_response = str(obj.source_response_payload).replace("'", '"')
            try:
                parsed_response = json.loads(_agency_buddy_response)
            except Exception as e:
                try:
                    parsed_response = ast.literal_eval(obj.source_response_payload)
                except Exception as e:
                    print("_agency_buddy_response", _agency_buddy_response)
                    print(f"Commission BUDDY exception: {e}", "\n\n")
                    return False

            commission_status = parsed_response.get("status", None)
            print("commission_status", commission_status, "\n\n")
            if commission_status is None:
                return
            
            if commission_status is False:

                obj.is_verified = True
                obj.save()
                
                if obj.is_agent_commission:
    
                    if PayoutTransactionTable.objects.filter(id = obj.id, is_verified = True).exists():
                        agent_instance = Agent.objects.get(phone = obj.phone)

                        reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
                        debit_credit_record = DebitCreditRecord.create_record(
                            phone_number=agent_instance.phone,
                            amount=obj.amount,
                            channel="POS/MOBILE",
                            reference=reference,
                            transaction_type="CREDIT",
                        )

                        payload = {"transaction_from": "COMMISSION_REWARD_REVERSAL"}

                        return UserWallet.fund_wallet(
                            user=agent_instance,
                            amount=debit_credit_record.amount,
                            channel="POS",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="COMMISSION_WALLET",
                            **payload,
                        )

                elif obj.is_super_agent_commission:
                    if PayoutTransactionTable.objects.filter(id = obj.id, is_verified = True).exists():
                        super_agent_wallet_instance = LottoSuperAgentWallet.objects.get(super_agent__phone = obj.phone)
                        balance_before = super_agent_wallet_instance.commission_balance
                        balance_after = balance_before + obj.amount
                        rewarded_commission_balance = super_agent_wallet_instance.rewarded_commission_balance
                        aft_rewarded_commission_balance = rewarded_commission_balance - obj.amount

                        LottoSuperAgentWallet.objects.filter(id = super_agent_wallet_instance.id).update(commission_balance = balance_after, rewarded_commission_balance = aft_rewarded_commission_balance)

                        LottoSuperAgentWalletTransaction.objects.create(
                            wallet = super_agent_wallet_instance,
                            amount = obj.amount,
                            transactton_type = "REVERSAL",
                            transaction_from = "COMMISSION_REWARDED"
                        )
            else:
                obj.is_verified = True
                obj.disbursed = True
                obj.save()


            
    def handle(self, *args, **kwargs):
        """
        Execute the command to re-verify unverified withdrawals.
        
        Gets all unverified withdrawals for the current year and processes them
        according to their payment source.
        """
        import pytz

        # Set up timezone
        now_utc = datetime.now(pytz.utc)
        tz = pytz.timezone("Africa/Lagos")
        now_tz = now_utc.astimezone(tz)

        # Get all unverified withdrawals for current year
        un_verified_withdrawals = PayoutTransactionTable.objects.filter(
            is_verified=False, 
            date_added__year=now_tz.year
        )

        


        # un_verified_withdrawals = PayoutTransactionTable.objects.filter(id__in = [417612])
        



        # Define common failure response keywords
        VFD_PAYOUT_FAILED_RESPONSES = [
            "transfer limit",
            "You do not have",
            "value is greater than",
            "cannot transfer",
            "transfer count",
            "valid ID",
            "cannot transfer money",
            "Insufficient balance",
            "Please contact support",
            "authentication",
            "FROM LOTTO BACKEND",
            "TEST PAYOUT",
            "your limit",
            "an error occured",
            "Expecting value",
            "504 Gateway Time-out",
            "nginx/1.18.0",
            "Time-out",
            "KYC",
            "transaction_pin",
            "field is required",
            "!doctype html",
            "Duplicate Transaction",
            "<!doctype html"
        ]




        if un_verified_withdrawals:
            for obj in un_verified_withdrawals:
                # Skip commission transactions
                if obj.type_of_transaction == "COMMISSION":
                    try:
                        self.handle_commission_transaction(obj)
                        continue
                    except Exception as e:
                        print(f"Commission exception two: {e}")
                        continue


                # Process based on transaction source
                if obj.source == "WOVEN":
                    self.handle_woven_transaction(obj)
                    
                elif obj.source == "VFD":
                    self.handle_vfd_transaction(obj)
                    
                elif obj.source == "BUDDY":
                    print("BUDDY", obj.id)
                    self.handle_buddy_transaction(obj, VFD_PAYOUT_FAILED_RESPONSES)
                    
                elif obj.source == "LIBERTY_PAY":
                    self.handle_liberty_pay_transaction(obj)

            print("Done")
        else:
            print("No unverified withdrawals")