import csv
import datetime
import random
from io import StringIO

from django.contrib import admin, messages
from django.db.models import Count, Q
from django.utils import timezone
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from scratch_cards.models import (
    CountAgentTickets,
    PayOut,
    ScratchCard,
    ScratchCardAgents,
    Variables,
    WeeklyWinners,
)
from scratch_cards.tasks import send_whisper_sms

# Register your models here.


# RESOURCES
class ScratchCardResource(resources.ModelResource):
    class Meta:
        model = ScratchCard


class PayOutResource(resources.ModelResource):
    class Meta:
        model = PayOut


class ScratchCardAgentsResource(resources.ModelResource):
    class Meta:
        model = ScratchCardAgents


class WeeklyWinnersResource(resources.ModelResource):
    class Meta:
        model = WeeklyWinners


class VariablesResource(resources.ModelResource):
    class Meta:
        model = Variables


#
# ADMINS


class PlayerPhoneNumberFilter(admin.SimpleListFilter):
    title = "Player Phone Number"
    parameter_name = "player_phone_number"

    def lookups(self, request, model_admin):
        return (
            ("yes", "Yes"),
            ("no", "No"),
        )

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(player_phone_number__isnull=False)
        elif self.value() == "no":
            return queryset
        return queryset


class ScratchCardResourceAdmin(ImportExportModelAdmin):
    resource_class = ScratchCardResource
    search_fields = [
        "serial_skew",
        "serial_number",
        "claimant_account_name",
        "claimant_account_number",
        "claimant_phone_number",
        "claimant_bank_code",
    ]
    list_filter = ("paid", "sold", "claimed", "serial_skew", "earning", PlayerPhoneNumberFilter)
    date_hierarchy = "date_created"
    ordering = ["-date_updated"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = (
        "makeavailable",
        "makeunavailable",
        "markassold",
        "pick_random_winner",
        "set_tickets_to_drawn",
        "set_specific_tickets_to_drawn",
        "update_tickets_from_file",
    )  # Necessary

    @admin.action(description="makeavailable")
    def makeavailable(modeladmin, request, queryset):
        for obj in queryset:
            obj.paid = True
            obj.sold = True
            obj.save()
            messages.success(request, "Successfully made available!")

    @admin.action(description="makeunavailable")
    def makeunavailable(modeladmin, request, queryset):
        for obj in queryset:
            obj.paid = False
            obj.sold = False
            obj.save()
            messages.success(request, "Successfully made unavailable!")

    @admin.action(description="markassold")
    def markassold(modeladmin, request, queryset):
        for obj in queryset:
            obj.sold = True
            obj.save()
            messages.success(request, "Successfully updated as sold!")

    # @admin.action(description="Start Lotto Draw")
    # def pick_random_winner(modeladmin, request, queryset):
    #     eligible_cards = ScratchCard.objects.filter(player_phone_number__isnull=False, sold=True, paid=True, serial_skew="S", drawn=False).exclude(
    #         id__in=WeeklyWinners.objects.values("ticket_id")
    #     )

    #     if eligible_cards.exists():
    #         winner_card = random.choice(eligible_cards)
    #         today_str = timezone.now().date().strftime("%Y-%m-%d")

    #         WeeklyWinners.objects.create(week_date=today_str, ticket=winner_card)
    #         # send sms here
    #         winner_message = f"Congratulations, you have been drawn as a winner in the Winwise Lotto Draw, Business Edition. Your winning number is: {winner_card.index}."
    #         send_whisper_sms.delay(message=winner_message, user_phone=winner_card.player_phone_number)
    #         messages.success(request, f"Scractch Card with Index: <{winner_card.index}> has been picked as a successful winner for the week.")
    #     else:
    #         modeladmin.message_user(request, "No eligible scratch cards found for the week.", level="warning")

    @admin.action(description="START LOTTO DRAW")
    def pick_random_winner(modeladmin, request, queryset):
        eligible_cards = ScratchCard.objects.filter(player_phone_number__isnull=False, sold=True, paid=True, serial_skew="S", drawn=False).exclude(
            id__in=WeeklyWinners.objects.values("ticket_id")
        )

        if not eligible_cards.exists():
            modeladmin.message_user(request, "No eligible scratch cards found for the week.", level="warning")
            return

        variables = Variables.objects.all().first()
        no_draw = variables.number_tickets_to_draw
        top_agents = eligible_cards.values("agent").annotate(count=Count("id")).order_by("-count")[:no_draw]

        top_agent_ids = [agent["agent"] for agent in top_agents]

        winners = []
        for agent_id in top_agent_ids:
            agent_cards = eligible_cards.filter(agent_id=agent_id)
            if agent_cards.exists():
                winners.append(random.choice(list(agent_cards)))

        if winners:
            today_str = timezone.now().date().strftime("%Y-%m-%d")

            for winner_card in winners:
                WeeklyWinners.objects.create(week_date=today_str, ticket=winner_card)

                winner_message = f"Congratulations! You have been drawn as a winner in the Winwise Lotto Draw, Business Edition. Your winning number is: {winner_card.index}."
                send_whisper_sms.delay(message=winner_message, user_phone=winner_card.player_phone_number)
                messages.success(request, f"Scratch Card with Index: <{winner_card.index}> has been picked as a successful winner for the week.")
        else:
            modeladmin.message_user(request, "No eligible winners were found for the selected agents.", level="warning")

    @admin.action(description="Set Tickets to Drawn")
    def set_tickets_to_drawn(modeladmin, request, queryset):
        eligible_cards = ScratchCard.objects.filter(player_phone_number__isnull=False, sold=True, paid=True, drawn=False)
        eligible_cards.update(drawn=True, week_drawn=timezone.now().date())
        messages.success(request, "Successfully set tickets to drawn!")

    @admin.action(description="SET SELECTED TICKETS TO DRAWN")
    def set_specific_tickets_to_drawn(modeladmin, request, queryset):
        # eligible_cards = ScratchCard.objects.filter(player_phone_number__isnull=False, sold=True, paid=True, drawn=False)
        queryset.update(drawn=True, week_drawn=timezone.now().date())
        messages.success(request, "Successfully set selected tickets to drawn!")

    @admin.action(description="UPDATE TICKETS FROM FILE")
    def update_tickets_from_file(modeladmin, request, queryset):
        variables = Variables.objects.all().first()
        if not variables or not variables.ticket_update_file:
            messages.error(request, "No ticket update file found.")
            return

        csv_file_content = variables.ticket_update_file
        csv_file = StringIO(csv_file_content)
        reader = csv.reader(csv_file)

        for row in reader:
            if len(row) < 2:
                continue  # skip row if it does not have at least 2 columns (improperly formatted)

            index = row[0].strip()
            earning = row[1].strip().lower()
            earning_amount = float(row[2].strip()) if len(row) > 2 and earning == "won" else 0.0

            try:
                scratch_card = ScratchCard.objects.get(index=index, serial_skew="S")
                if earning == "lost":
                    scratch_card.earning = "Lost"
                elif earning == "won":
                    scratch_card.earning = "Won"
                    scratch_card.earning_amount = earning_amount
                elif earning == "weekly draw":
                    scratch_card.earning = "Weekly Draw"
                else:
                    continue

                scratch_card.save()
                messages.success(request, f"Successfully updated ScratchCard with index {index}.")
            except ScratchCard.DoesNotExist:
                messages.warning(request, f"ScratchCard with index {index} not found. Skipping.")


class PayOutResourceAdmin(ImportExportModelAdmin):
    resource_class = PayOutResource
    search_fields = ["phone_number", "account_name", "account_number", "bank_code", "transaction_reference"]
    list_filter = ("status",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = ("retry_transaction",)  # Necessary

    @admin.action(description="Retry Selected Failed Transaction")
    def retry_transaction(modeladmin, request, queryset):
        for transaction in queryset:
            PayOut.retry_transaction(transaction_reference=transaction.transaction_reference)
            messages.success(request, "Successfully retried transaction!")


class ScratchCardAgentsResourceAdmin(ImportExportModelAdmin):
    resource_class = ScratchCardAgents
    search_fields = ["first_name", "last_name", "phone_number", "email"]
    date_hierachy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class VariablesResourceAdmin(ImportExportModelAdmin):
    resource_class = VariablesResource
    # search_fields = ["first_name", "last_name", "phone_number", "email"]
    date_hierachy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WeeklyWinnersAdmin(ImportExportModelAdmin):
    resource_class = WeeklyWinnersResource
    search_fields = ["ticket__index", "week_date", "ticket__player_phone_number"]
    list_filter = ("week_date",)
    list_display = ["week_date", "ticket", "created_at", "updated_at"]
    date_hierarchy = "created_at"


class CountAgentTicketsAdmin(admin.ModelAdmin):
    list_display = ["agent", "counts", "updated_at"]
    search_fields = ["start_date", "end_date", "agent__first_name", "agent__last_name", "agent__phone_number"]
    ordering = ["-counts"]

    def update_agent_ticket_counts(self):
        agents = ScratchCard.objects.filter(paid=True, sold=True, player_phone_number__isnull=False, agent__isnull=False, serial_skew="S")
        for agent in agents:
            ticket_count = ScratchCard.objects.filter(
                paid=True, sold=True, player_phone_number__isnull=False, agent=agent.agent, serial_skew="S"
            ).count()
            count_agent_ticket, created = CountAgentTickets.objects.get_or_create(agent=agent.agent)
            count_agent_ticket.counts = ticket_count
            count_agent_ticket.updated_at = timezone.now()
            count_agent_ticket.save()

    def get_search_results(self, request, queryset, search_term):
        self.update_agent_ticket_counts()
        start_date = request.GET.get("start_date", None)
        end_date = request.GET.get("end_date", None)

        if start_date and end_date:
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d") + datetime.timedelta(days=1)
            agent_ids = ScratchCard.objects.filter(
                paid=True,
                sold=True,
                player_phone_number__isnull=False,
                agent__isnull=False,
                serial_skew="S",
                purchased_at__range=(start_date, end_date),
            ).values_list("agent", flat=True)

            queryset = CountAgentTickets.objects.filter(agent__in=agent_ids)
            return queryset, False

        if search_term:
            terms = search_term.split(" ")
            if len(terms) == 2:
                try:
                    start_date = datetime.datetime.strptime(terms[0], "%Y-%m-%d")
                    end_date = datetime.datetime.strptime(terms[1], "%Y-%m-%d") + datetime.timedelta(days=1)
                    scratch_cards = (
                        ScratchCard.objects.filter(
                            paid=True,
                            sold=True,
                            player_phone_number__isnull=False,
                            agent__isnull=False,
                            serial_skew="S",
                            purchased_at__range=(start_date, end_date),
                        )
                        .values("agent")
                        .annotate(count=Count("id"))
                    )

                    for agent_data in scratch_cards:
                        agent_id = agent_data["agent"]
                        count = agent_data["count"]
                        CountAgentTickets.objects.update_or_create(agent_id=agent_id, defaults={"counts": count, "updated_at": timezone.now()})

                    agent_ids = [agent_data["agent"] for agent_data in scratch_cards]
                    total_count = sum([agent_data["count"] for agent_data in scratch_cards])
                    request.total_count = total_count
                    queryset = CountAgentTickets.objects.filter(agent__in=agent_ids)
                    return queryset, False
                    # ).values_list('agent', flat=True)

                    # queryset = CountAgentTickets.objects.filter(agent__in=agent_ids)
                except ValueError:
                    queryset = CountAgentTickets.objects.filter(
                        Q(agent__first_name__icontains=search_term)
                        | Q(agent__last_name__icontains=search_term)
                        | Q(agent__full_name__icontains=search_term)
                    )
                    total_count = sum([q.counts for q in queryset])
                    request.total_count = total_count
                    return queryset, False
            else:
                queryset = CountAgentTickets.objects.filter(
                    Q(agent__first_name__icontains=search_term)
                    | Q(agent__last_name__icontains=search_term)
                    | Q(agent__full_name__icontains=search_term)
                )
                total_count = sum([q.counts for q in queryset])
                request.total_count = total_count
                return queryset, False

        else:
            queryset = CountAgentTickets.objects.filter(
                Q(agent__first_name__icontains=search_term) | Q(agent__last_name__icontains=search_term) | Q(agent__full_name__icontains=search_term)
            )
            total_count = sum([q.counts for q in queryset])
            request.total_count = total_count
            return queryset, False

    def changelist_view(self, request, extra_context=None):
        response = super().changelist_view(request, extra_context)
        total_count = getattr(request, "total_count", 0)
        if total_count:
            messages.info(request, f"Total Counts: {total_count}")
        return response


admin.site.register(CountAgentTickets, CountAgentTicketsAdmin)
admin.site.register(WeeklyWinners, WeeklyWinnersAdmin)
admin.site.register(ScratchCardAgents, ScratchCardAgentsResourceAdmin)
admin.site.register(ScratchCard, ScratchCardResourceAdmin)
admin.site.register(PayOut, PayOutResourceAdmin)
admin.site.register(Variables, VariablesResourceAdmin)
