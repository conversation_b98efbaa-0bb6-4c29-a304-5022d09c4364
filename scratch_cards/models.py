import time

from django.db import models, transaction

from pos_app.models import Agent

# Create your models here.
TICKET_EARNING = [
    ("LOST", "lost"),
    ("WON", "won"),
]

PAYOUT_STATUS = [
    ("PENDING", "pending"),
    ("SUCCESSFUL", "successful"),
    ("FAILED", "failed"),
]


class ScratchCard(models.Model):
    agent = models.ForeignKey(Agent, related_name="scratch_card_agents", on_delete=models.SET_NULL, null=True, blank=True)
    index = models.CharField(max_length=100, null=True, blank=True)
    serial_skew = models.CharField(max_length=100, null=True, blank=True)
    serial_number = models.CharField(max_length=100, null=True, blank=True)
    serial_number_int = models.IntegerField(default=0)
    earning = models.CharField(max_length=50, null=True, blank=True, default="LOST")
    earning_amount = models.FloatField(null=True, blank=True, default=0.0)
    pin = models.CharField(max_length=100, null=True, blank=True)
    game_id = models.CharField(max_length=150, null=True, blank=True)
    pick = models.TextField(null=True, blank=True)
    result = models.TextField(null=True, blank=True)
    paid = models.BooleanField(default=False)
    sold = models.BooleanField(default=False)
    ticket_amount_paid = models.FloatField(null=True, blank=True, default=0.0)
    claimant_account_name = models.CharField(max_length=255, null=True, blank=True)
    claimant_account_number = models.CharField(max_length=50, null=True, blank=True)
    claimant_bank_code = models.CharField(max_length=100, null=True, blank=True)
    claimant_phone_number = models.CharField(max_length=100, null=True, blank=True)
    player_phone_number = models.CharField(max_length=100, null=True, blank=True)
    claimed = models.BooleanField(default=False)
    winning_status = models.CharField(max_length=100, null=True, blank=True)
    drawn = models.BooleanField(default=False)
    week_drawn = models.DateField(null=True, blank=True)
    purchased_at = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return str(self.ticket_number)

    @property
    def ticket_number(self) -> str:
        return f"{self.serial_skew}{self.serial_number}"

    def save(self, *args, **kwargs):
        self.serial_number_int = self.serial_number
        super(ScratchCard, self).save(*args, **kwargs)

    class Meta:
        unique_together = ("serial_skew", "serial_number", "index", "game_id")
        verbose_name = "SCRATCH CARD"
        verbose_name_plural = "SCRATCH CARDS"

    @classmethod
    def create_scratch_card(cls, data):
        for item in data:
            index = item["index"]
            serial_skew = item["serial_skew"]
            serial_number = item["serial_number"]
            earning = item["earning"]
            earning_amount = item["earning_amount"]
            pin = item["pin"]
            game_id = item["game_id"]
            pick = item["pick"]
            result = item["result"]

            cls.objects.create(
                index=index,
                serial_skew=serial_skew,
                serial_number=serial_number,
                earning=earning,
                earning_amount=earning_amount,
                pin=pin,
                game_id=game_id,
                pick=pick,
                result=result,
            )

    @classmethod
    def get_unique_scratch_cards(cls, ticket_number=None, phone_number=None):
        """
        This method returns a unique scratch card queryset for the given ticket number
        combining the serial_skew and serial_number
        """
        serial_skew = "".join(filter(str.isalpha, ticket_number))
        serial_number = "".join(filter(str.isdigit, ticket_number))

        if phone_number is None:
            queryset = ScratchCard.objects.filter(serial_skew=serial_skew, serial_number=serial_number)
        else:
            queryset = ScratchCard.objects.filter(serial_skew=serial_skew, serial_number=serial_number, player_phone_number=phone_number)

        return queryset

    @classmethod
    def get_scratch_card(cls, serial_skew, serial_number):
        """
        This method returns the scratch card object for the given serial skew
        and serial number, returns None is not found
        """
        scratch_card = cls.objects.filter(serial_skew=serial_skew, serial_number=serial_number).first()
        return scratch_card

    @classmethod
    def separate_letters_and_numbers(cls, input_string):
        letters = "".join(char for char in input_string if char.isalpha())
        numbers = "".join(char for char in input_string if char.isdigit())
        return letters, numbers


class PayOut(models.Model):
    ticket = models.OneToOneField(ScratchCard, unique=True, on_delete=models.CASCADE, related_name="scratch_card_winning")
    game_id = models.CharField(max_length=100, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True, default=0.0)
    phone_number = models.CharField(max_length=100, null=True, blank=True)
    account_name = models.CharField(max_length=255, null=True, blank=True)
    account_number = models.CharField(max_length=50, null=True, blank=True)
    bank_code = models.CharField(max_length=100, null=True, blank=True)
    transaction_reference = models.CharField(max_length=200, null=True, blank=True, unique=True)
    request = models.TextField(null=True, blank=True)
    response = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, null=True, blank=True, choices=PAYOUT_STATUS, default="PENDING")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SCRATCH CARD PAYOUT"
        verbose_name_plural = "SCRATCH CARD PAYOUTS"

    @classmethod
    def reference_exists(cls, transaction_reference):
        reference_exists = cls.objects.filter(transaction_reference=transaction_reference).exists()
        return reference_exists

    @classmethod
    def create_transaction_reference(cls, game_id, phone_number, account_number) -> str:
        current_time_stamp = int(time.time())
        formed_reference = f"{game_id}{phone_number}{account_number}-{current_time_stamp}"
        get_reference = cls.reference_exists(transaction_reference=formed_reference)
        if not get_reference:
            reference = formed_reference
        else:
            reference = None

        return reference

    @classmethod
    def create_transaction(cls, account_name, account_number, bank_code, game_id, phone_number, transaction_reference, ticket):
        transaction = cls.objects.create(
            account_name=account_name,
            account_number=account_number,
            bank_code=bank_code,
            game_id=game_id,
            phone_number=phone_number,
            transaction_reference=transaction_reference,
            ticket=ticket,
            amount=ticket.earning_amount,
        )
        return transaction

    @classmethod
    def retry_transaction(cls, transaction_reference):
        from scratch_cards.helpers.api import send_scratch_card_payout

        TRANSACTION_STATUS = ["PENDING", "FAILED"]
        transaction_ins = cls.objects.filter(transaction_reference=transaction_reference, status__in=TRANSACTION_STATUS).first()
        if not transaction_ins:
            payout = {
                "status": "failed",
                "status_code": 400,
                "data": {"status": False, "message": "payout is already successful", "transaction": None},
                "errors": None,
            }
        else:
            with transaction.atomic():
                if "-" in transaction_reference:
                    current_time_stamp = int(time.time())
                    base_reference = transaction_reference.split("-")[0]
                    new_reference = f"{base_reference}-{current_time_stamp}"
                    transaction_ins.transaction_reference = new_reference
                    transaction_ins.save()
                    payout = send_scratch_card_payout(transaction_reference=new_reference)

                else:
                    current_time_stamp = int(time.time())
                    new_reference = f"{transaction_reference}-{current_time_stamp}"
                    transaction_ins.transaction_reference = new_reference
                    transaction_ins.save()
                    payout = send_scratch_card_payout(transaction_reference=new_reference)

        return payout


class ScratchCardAgents(models.Model):
    first_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, null=True, blank=True)
    date_of_birth = models.DateField()
    nationality = models.CharField(max_length=100, null=True, blank=True)
    phone_Number = models.CharField(max_length=100, null=True, blank=True)
    whatsapp_phone_Number = models.CharField(max_length=100, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    house_number = models.CharField(max_length=100, null=True, blank=True)
    street = models.CharField(max_length=100, null=True, blank=True)
    landmark = models.CharField(max_length=100, null=True, blank=True)
    bus_stop = models.CharField(max_length=100, null=True, blank=True)
    lga = models.CharField(max_length=100, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    account_name = models.CharField(max_length=100, null=True, blank=True)
    account_number = models.CharField(max_length=100, null=True, blank=True)
    bank_name = models.CharField(max_length=100, null=True, blank=True)
    nok_name = models.CharField(max_length=100, null=True, blank=True)
    nok_phone_number = models.CharField(max_length=100, null=True, blank=True)
    nok_address = models.CharField(max_length=100, null=True, blank=True)
    nok_relationship = models.CharField(max_length=100, null=True, blank=True)
    nin = models.CharField(max_length=100, null=True, blank=True)
    sales_manager = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SCRATCH CARD AGENT"
        verbose_name_plural = "SCRATCH CARD AGENTS"


class WeeklyWinners(models.Model):
    week_date = models.DateField()  # Date representing the Friday of the week
    ticket = models.ForeignKey(ScratchCard, on_delete=models.CASCADE, related_name="weekly_winners")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Winner: {self.ticket.player_phone_number}, Date: {self.week_date}"

    class Meta:
        constraints = [models.UniqueConstraint(fields=["week_date", "ticket"], name="unique_weekly_winner")]


class CountAgentTickets(models.Model):
    agent = models.OneToOneField("pos_app.Agent", on_delete=models.CASCADE, unique=True)
    counts = models.IntegerField(default=0)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    # created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.agent.full_name} - {self.counts} tickets"

    class Meta:
        verbose_name = "Count Agent Tickets"
        verbose_name_plural = "Count Agent Tickets"


class Variables(models.Model):

    ticket_update_file = models.TextField(null=True, blank=True)
    number_tickets_to_draw = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Variable"
        verbose_name_plural = "Variables"
