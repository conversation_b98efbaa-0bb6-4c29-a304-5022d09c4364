import json

import requests
from django.conf import settings

from scratch_cards.models import PayOut

USE_DEBUG_MODE = settings.DEBUG

SCRATCH_CARD_EMAIL = settings.SCRATCH_CARD_EMAIL
SCRATCH_CARD_PASSWORD = settings.SCRATCH_CARD_PASSWORD


def paystack_account_name(account_number, bank_code):
    from wallet_app.helpers.payment_gateway import PaymentGateway

    # VERIFY BANK DETAILS
    if not USE_DEBUG_MODE:
        paystack = PaymentGateway()
        verify_bank_response = paystack.fetch_account_name(account_number=account_number, bank_code=bank_code)

        if isinstance(verify_bank_response, dict):
            if verify_bank_response.get("status") is False:
                data = {"status": False, "message": "Invalid bank details", "account_name": ""}
            else:
                account_name = verify_bank_response.get("data", {}).get("account_name", "")
                data = {"status": True, "message": "success", "account_name": account_name}
        else:
            data = {"status": False, "message": "Sorry we could not process your request", "account_name": ""}

        return data
    else:
        data = {"status": True, "message": "success", "account_name": "Wise Winn"}
        return data


def wema_login():
    if not USE_DEBUG_MODE:
        headers = {
            "Content-Type": "application/json",
        }
        url = "https://banking.libertypayng.com/api/v1/companies/auth/login/"

        payload = json.dumps(
            {
                "email": SCRATCH_CARD_EMAIL,
                "password": SCRATCH_CARD_PASSWORD,
            }
        )
        response = requests.request("POST", url, headers=headers, data=payload)
        login_response = response.json()
        access_token = login_response.get("data", {}).get("access")
        status = login_response.get("status", {})
        return {"status": status, "access_token": access_token}
    else:
        return {"status": "success", "access_token": "12345676542345676543"}


def send_scratch_card_payout(transaction_reference):
    from wyse_ussd.helper.bank import BankManger

    get_scratch_card_obj = PayOut.objects.filter(transaction_reference=transaction_reference).first()

    if get_scratch_card_obj:
        bearer_token = wema_login()
        access_token = bearer_token.get("access_token")

        # print(access_token, "ACCESS TOKEN\n")

        bank_code = BankManger().cbn_code_to_bank_code(cbn_code=get_scratch_card_obj.bank_code)
        # print(bank_code, "BANK CODE\n")

        # print(USE_DEBUG_MODE, "DEBUG MODE\n")

        if bearer_token:
            payload = json.dumps(
                {
                    "source_account": **********,
                    "mode": "LIVE",
                    "account_name": get_scratch_card_obj.account_name,
                    "account_number": get_scratch_card_obj.account_number,
                    "bank_code": bank_code,
                    "request_reference": f"{get_scratch_card_obj.transaction_reference}",
                    "amount": get_scratch_card_obj.amount,
                    "narration": "scratch and win",
                }
            )

            get_scratch_card_obj.request = payload
            get_scratch_card_obj.status = "PENDING"
            get_scratch_card_obj.save()
            if not USE_DEBUG_MODE:
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                url = "https://banking.libertypayng.com/accounts/transfer_money/"

                response = requests.request("POST", url, headers=headers, data=payload)
                payout_response = response.json()

                # print(payout_response, "SEND PAYOUT\n")

                if isinstance(payout_response, dict):
                    data = payout_response
                    get_scratch_card_obj.response = data
                    get_scratch_card_obj.save()
                else:
                    data = {
                        "status": "failed",
                        "status_code": 500,
                        "data": {"status": False, "message": "an error occurred", "transaction": None},
                        "errors": None,
                    }

                return data
            else:
                data = {
                    "status": "success",
                    "status_code": 200,
                    "data": {
                        "status": True,
                        "message": "success",
                        "transaction": {
                            "company": "Space X_R",
                            "source_account": "**********",
                            "mode": "LIVE",
                            "account_name": get_scratch_card_obj.account_name,
                            "account_number": get_scratch_card_obj.account_number,
                            "bank_code": get_scratch_card_obj.bank_code,
                            "request_reference": get_scratch_card_obj.transaction_reference,
                            "amount": get_scratch_card_obj.amount,
                            "narration": "TEST",
                            "status": "PENDING",
                            "session_id": "***************",
                        },
                    },
                    "errors": None,
                }
                get_scratch_card_obj.response = data
                get_scratch_card_obj.save()
                return data
        else:
            data = {
                "status": "failed",
                "status_code": 500,
                "data": {"status": False, "message": "an error occurred", "transaction": None},
                "errors": None,
            }
    else:
        data = {
            "status": "failed",
            "status_code": 500,
            "data": {"status": False, "message": "an error occurred", "transaction": None},
            "errors": None,
        }

        return data


def get_transaction_status(transaction_reference):
    from scratch_cards.helpers.api import wema_login

    bearer_token = wema_login()
    access_token = bearer_token.get("access_token")
    if bearer_token:
        if not USE_DEBUG_MODE:
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            }
            url = f"https://banking.libertypayng.com/accounts/verify_transfer?search={transaction_reference}"

            response = requests.request("GET", url, headers=headers)
            payout_response = response.json()
            # print(payout_response, "VERIFY\n")

            if isinstance(payout_response, dict):
                data = payout_response
            else:
                data = {}
        else:
            data = {
                "status": "success",
                "status_code": 200,
                "data": {
                    "transactions": [
                        {
                            "id": "xxxxxxxxxxxxxxxxxxxxxx",
                            "created_at": "2024-06-13T20:23:01.445694+01:00",
                            "updated_at": "2024-06-13T20:23:01.445724+01:00",
                            "beneficiary_account_number": "xxxxxxxx",
                            "beneficiary_account_name": "TEST",
                            "reference": "xxxxxxxxxxxxxxx",
                            "amount": 100.0,
                            "fee": 0.0,
                            "amount_payable": 100.0,
                            "transaction_status": "SUCCESSFUL",
                            "session_id": "xxxxxxxxxxxxxxxxxxxxxxxxx",
                            "bank_code": "xxxxx",
                            "bank_name": None,
                            "source_account": "xxxxxxxxx",
                            "service_provider": "WEMA_BANK",
                            "transaction_type": "DEBIT",
                            "narration": "scratch and win",
                            "is_verified": True,
                            "mode": "LIVE",
                            "company_reference": f"{transaction_reference}",
                            "currency": "NGN",
                            "company": "WinWise Scratch-N-Win",
                            "sub_company": None,
                        }
                    ]
                },
                "errors": None,
            }
    else:
        data = {}
    return data
