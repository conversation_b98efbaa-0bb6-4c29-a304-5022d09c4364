import uuid
from datetime import datetime

import requests
from decouple import config
from django.conf import settings

from main.models import PayoutTransactionTable
from pos_app.models import AgencyBankingToken
from pos_app.pos_helpers import agent_login, ghana_lotto_wallet_login
from wallet_system.models import Wallet, WalletTransaction
import redis


def move_ghana_rtp_value_to_ghana_rtp_wallet(amount):
    try:
        wallet = Wallet.objects.get(wallet_type="GHANA_RTP_WALLET")
    except Wallet.DoesNotExist:
        return None

    # amount = wallet.balance

    if amount <= 0:
        return None

    balance_before = wallet.balance

    if balance_before < amount:
        raise ValueError("Insufficient balance")

    balance_after = balance_before - amount

    wallet.previous_balance = round(balance_before, 2)
    wallet.balance = round(balance_after, 2)
    wallet.save()

    # create wallet transaction
    WalletTransaction.objects.create(
        wallet=wallet,
        wallet_type="GHANA_RTP_WALLET",
        amount=amount,
        previous_balance=balance_before,
        current_balance=balance_after,
        transaction_type="DEBIT",
    )

    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

    phone = "*************"

    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": "MOVE GHANA RTP VALUE TO GHANA RTP WALLET",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    _withdraw_table_instance = PayoutTransactionTable.objects.create(
        source="BUDDY",
        amount=amount,
        disbursement_unique_id=payout_reference,
        phone=phone,
        payout_trans_ref=payout_reference,
        channel="POS",
        payout_payload=payload,
        balance_before=balance_before,
        balance_after=balance_after,
        joined_since=wallet.get_duration(),
        name="Ghana RTP Wallet",
        source_wallet="AGENT_FUNDING_WALLET",
        recipient_wallet="GHANA_RTP_WALLET",
    )

    # ettings.AGENCY_BANKING_TRANSACTION_PIN
    cp_payload = payload.copy()
    cp_payload["transaction_pin"] = config("AGENCY_BANKING_TRANSACTION_PIN")

    token  = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    try:
        response = requests.post(url, headers=headers, json=cp_payload)


        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"commission_agent_login_token")

            return move_ghana_rtp_value_to_ghana_rtp_wallet(amount)
        

    except requests.exceptions.RequestException:
        _withdraw_table_instance.status = "FAILED"
        _withdraw_table_instance.save()

        return None

    try:
        res = response.json()
    except ValueError:
        res = response.text

    _withdraw_table_instance.source_response_payload = res
    _withdraw_table_instance.save()

    return True


def move_retail_rtp_value_to_retail_rtp_wallet(amount):
    try:
        wallet = Wallet.objects.get(wallet_type="RETAIL_RTP_WALLET")
    except Wallet.DoesNotExist:
        return None

    # amount = wallet.balance

    if amount <= 0:
        return

    balance_before = wallet.balance

    if balance_before < amount:
        raise ValueError("Insufficient balance")

    balance_after = balance_before - amount

    wallet.previous_balance = round(balance_before, 2)
    wallet.balance = round(balance_after, 2)
    wallet.save()

    # create wallet transaction
    WalletTransaction.objects.create(
        wallet=wallet,
        wallet_type="RETAIL_RTP_WALLET",
        amount=amount,
        previous_balance=balance_before,
        current_balance=balance_after,
        transaction_type="DEBIT",
    )

    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

    phone = "*************"

    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": "RETAIL RTP",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    _withdraw_table_instance = PayoutTransactionTable.objects.create(
        source="BUDDY",
        amount=amount,
        disbursement_unique_id=payout_reference,
        phone=phone,
        payout_trans_ref=payout_reference,
        channel="POS",
        payout_payload=payload,
        balance_before=balance_before,
        balance_after=balance_after,
        joined_since=wallet.get_duration(),
        name="RETAIL RTP Wallet",
        source_wallet="AGENT_FUNDING_WALLET",
        recipient_wallet="RETAIL_RTP_WALLET",
    )

    cp_payload = payload.copy()
    cp_payload["transaction_pin"] = config("AGENCY_BANKING_TRANSACTION_PIN")

    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    try:
        response = requests.post(url, headers=headers, json=cp_payload)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"commission_agent_login_token")

            return move_retail_rtp_value_to_retail_rtp_wallet(amount)
        


    except requests.exceptions.RequestException:
        _withdraw_table_instance.status = "FAILED"
        _withdraw_table_instance.save()

        return None

    try:
        res = response.json()
    except ValueError:
        res = response.text

    _withdraw_table_instance.source_response_payload = res
    _withdraw_table_instance.save()

    return True


def move_rto_value_to_rto_wallet(amount):
    try:
        wallet = Wallet.objects.get(wallet_type="RTO_WALLET")
    except Wallet.DoesNotExist:
        return None

    # amount = wallet.balance

    if amount <= 0:
        return None

    balance_before = wallet.balance

    if balance_before < amount:
        raise ValueError("Insufficient balance")

    balance_after = balance_before - amount

    wallet.previous_balance = round(balance_before, 2)
    wallet.balance = round(balance_after, 2)
    wallet.save()

    # create wallet transaction
    WalletTransaction.objects.create(
        wallet=wallet,
        wallet_type="RTO_WALLET",
        amount=amount,
        previous_balance=balance_before,
        current_balance=balance_after,
        transaction_type="DEBIT",
    )

    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

    phone = "*************"

    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": "RTO REVENUE",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    _withdraw_table_instance = PayoutTransactionTable.objects.create(
        source="BUDDY",
        amount=amount,
        disbursement_unique_id=payout_reference,
        phone=phone,
        payout_trans_ref=payout_reference,
        channel="POS",
        payout_payload=payload,
        balance_before=balance_before,
        balance_after=balance_after,
        joined_since=wallet.get_duration(),
        name="RTO Wallet",
        source_wallet="AGENT_FUNDING_WALLET",
        recipient_wallet="RTO_WALLET",
    )

    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

    cp_payload = payload.copy()
    cp_payload["transaction_pin"] = config("AGENCY_BANKING_TRANSACTION_PIN")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    try:
        response = requests.post(url, headers=headers, json=cp_payload)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"commission_agent_login_token")

            return move_rto_value_to_rto_wallet(amount)

    except requests.exceptions.RequestException:
        _withdraw_table_instance.status = "FAILED"
        _withdraw_table_instance.save()

        return None

    try:
        res = response.json()
    except ValueError:
        res = response.text

    _withdraw_table_instance.source_response_payload = res
    _withdraw_table_instance.save()

    return True


def move_ghana_rto_value_to_rto_wallet(amount):
    try:
        wallet = Wallet.objects.get(wallet_type="GHANA_RTO_WALLET")
    except Wallet.DoesNotExist:
        return None

    # amount = wallet.balance

    if amount <= 0:
        return None

    balance_before = wallet.balance

    if balance_before < amount:
        raise ValueError("Insufficient balance")

    balance_after = balance_before - amount

    wallet.previous_balance = round(balance_before, 2)
    wallet.balance = round(balance_after, 2)
    wallet.save()

    # create wallet transaction
    WalletTransaction.objects.create(
        wallet=wallet,
        wallet_type="GHANA_RTO_WALLET",
        amount=amount,
        previous_balance=balance_before,
        current_balance=balance_after,
        transaction_type="DEBIT",
    )

    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

    phone = "*************"

    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": "GHANA RTO",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    _withdraw_table_instance = PayoutTransactionTable.objects.create(
        source="BUDDY",
        amount=amount,
        disbursement_unique_id=payout_reference,
        phone=phone,
        payout_trans_ref=payout_reference,
        channel="POS",
        payout_payload=payload,
        balance_before=balance_before,
        balance_after=balance_after,
        joined_since=wallet.get_duration(),
        name="RTO Wallet",
        source_wallet="GHANA_RTP_WALLET",
        recipient_wallet="RTO_WALLET",
    )

    # ettings.AGENCY_BANKING_TRANSACTION_PIN
    cp_payload = payload.copy()
    cp_payload["transaction_pin"] = config("AGENCY_BANKING_GHANA_LOTTO_WALLET_TRANSACTION_PIN")

    token = AgencyBankingToken.retrieve_token("GHANA_RTP_WALLET")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    try:
        response = requests.post(url, headers=headers, json=cp_payload)
        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"ghana_lotto_wallet_login")

            return move_ghana_rto_value_to_rto_wallet(amount)
        
        
    except requests.exceptions.RequestException:
        _withdraw_table_instance.status = "FAILED"
        _withdraw_table_instance.save()

        return None

    try:
        res = response.json()
    except ValueError:
        res = response.text

    _withdraw_table_instance.source_response_payload = res
    _withdraw_table_instance.save()

    return True


def move_moveable_ghana_rtp_value_to_retails_wallet(amount):
    try:
        wallet = Wallet.objects.get(wallet_type="MOVEABLE_GHANA_RTP_WALLET")
    except Wallet.DoesNotExist:
        return None

    # amount = wallet.balance

    if amount <= 0:
        return None

    balance_before = wallet.balance

    if balance_before < amount:
        raise ValueError("Insufficient balance")

    balance_after = balance_before - amount

    wallet.previous_balance = round(balance_before, 2)
    wallet.balance = round(balance_after, 2)
    wallet.save()

    # create wallet transaction
    WalletTransaction.objects.create(
        wallet=wallet,
        wallet_type="GHANA_RTO_WALLET",
        amount=amount,
        previous_balance=balance_before,
        current_balance=balance_after,
        transaction_type="DEBIT",
    )

    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

    phone = "*************"

    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": "MOVE MONEY FROM GHANA RTP TO RETAIL RTP",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    _withdraw_table_instance = PayoutTransactionTable.objects.create(
        source="BUDDY",
        amount=amount,
        disbursement_unique_id=payout_reference,
        phone=phone,
        payout_trans_ref=payout_reference,
        channel="POS",
        payout_payload=payload,
        balance_before=balance_before,
        balance_after=balance_after,
        joined_since=wallet.get_duration(),
        name="RETAIL RTP Wallet",
        source_wallet="GHANA_RTP_WALLET",
        recipient_wallet="RETAIL_RTP_WALLET",
    )

    # ettings.AGENCY_BANKING_TRANSACTION_PIN
    cp_payload = payload.copy()
    cp_payload["transaction_pin"] = config("AGENCY_BANKING_GHANA_LOTTO_WALLET_TRANSACTION_PIN")

    token = AgencyBankingToken.retrieve_token("GHANA_RTP_WALLET")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    try:
        response = requests.post(url, headers=headers, json=cp_payload)
        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"ghana_lotto_wallet_login")

            return move_moveable_ghana_rtp_value_to_retails_wallet(amount)
        


    except requests.exceptions.RequestException:
        _withdraw_table_instance.status = "FAILED"
        _withdraw_table_instance.save()

        return None

    try:
        res = response.json()
    except ValueError:
        res = response.text

    _withdraw_table_instance.source_response_payload = res
    _withdraw_table_instance.save()

    return True

def move_retail_commission_value_to_retail_commission_wallet(amount):
    try:
        wallet = Wallet.objects.get(wallet_type="COMMISSION")
    except Wallet.DoesNotExist:
        return None

    # amount = wallet.balance

    if amount <= 0:
        return None

    balance_before = wallet.balance

    if balance_before < amount:
        raise ValueError("Insufficient balance")

    balance_after = balance_before - amount

    wallet.previous_balance = round(balance_before, 2)
    wallet.balance = round(balance_after, 2)
    wallet.save()

    # create wallet transaction
    WalletTransaction.objects.create(
        wallet=wallet,
        wallet_type="COMMISSION",
        amount=amount,
        previous_balance=balance_before,
        current_balance=balance_after,
        transaction_type="DEBIT",
    )

    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

    phone = "*************"

    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": "RETAIL COMMISSION",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    _withdraw_table_instance = PayoutTransactionTable.objects.create(
        source="BUDDY",
        amount=amount,
        disbursement_unique_id=payout_reference,
        phone=phone,
        payout_trans_ref=payout_reference,
        channel="POS",
        payout_payload=payload,
        balance_before=balance_before,
        balance_after=balance_after,
        joined_since=wallet.get_duration(),
        name="RETAIL COMMISSION WALLETt",
        source_wallet="AGENT_FUNDING_WALLET",
        recipient_wallet="RETAIL_COMMISSION_WALLET",
    )

    # ettings.AGENCY_BANKING_TRANSACTION_PIN
    cp_payload = payload.copy()
    cp_payload["transaction_pin"] = config("AGENCY_BANKING_TRANSACTION_PIN")

    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    try:
        response = requests.post(url, headers=headers, json=cp_payload)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"commission_agent_login_token")

            return move_retail_commission_value_to_retail_commission_wallet(amount)
        
        
    except requests.exceptions.RequestException:
        _withdraw_table_instance.status = "FAILED"
        _withdraw_table_instance.save()

        return None

    try:
        res = response.json()
    except ValueError:
        res = response.text

    _withdraw_table_instance.source_response_payload = res
    _withdraw_table_instance.save()

    return True
