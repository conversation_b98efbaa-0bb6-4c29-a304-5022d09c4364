from datetime import datetime

from dateutil.relativedelta import relativedelta
from django.db import models
import uuid


from decouple import config
import requests
from django.conf import settings

import redis




# Create your models here.
class Wallet(models.Model):
    WALLET_TYPE = [
        ("AGENT_FUNDING_WALLET", "AGENT_FUNDING_WALLET"),
        ("NON_RETAIL_WALLET", "NON_RETAIL_WALLET"),
        ("RETAIL_RTP_WALLET", "RET<PERSON>IL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "GHANA_RTP_WALLET"),
        ("MOVE<PERSON>LE_GHANA_RTP_WALLET", "MOVEABLE_GHANA_RTP_WALLET"),
        ("GHANA_RTO_WALLET", "GHANA_RTO_WALLET"),
        ("RTO_WALLET", "RTO_WALLET"),
        ("COMMISSION", "COMMISSION"),
    ]

    balance = models.FloatField(default=0.0)
    wallet_phone_number = models.CharField(max_length=20, null=True, blank=True)
    previous_balance = models.FloatField(default=0.0)
    wallet_type = models.CharField(max_length=50, choices=WALLET_TYPE)
    user_id = models.CharField(max_length=50, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.wallet_type} wallet"

    def get_duration(self):
        """Calculate the duration since the user was created."""
        if not self.created_at:
            return "Unknown"

        current_time = datetime.now()
        created_time = self.created_at.replace(tzinfo=None)  # Ensure timezone compatibility
        delta = relativedelta(current_time, created_time)

        duration_parts = []
        if delta.years:
            duration_parts.append(f"{delta.years} year{'s' if delta.years > 1 else ''}")
        if delta.months:
            duration_parts.append(f"{delta.months} month{'s' if delta.months > 1 else ''}")

        return ", ".join(duration_parts) if duration_parts else "Less than a month"

    class Meta:
        verbose_name = "WALLET"
        verbose_name_plural = "WALLETS"

    @classmethod
    def get_retail_rtp_balance(cls):
        try:
            wallet = Wallet.objects.get(wallet_type="RETAIL_RTP_WALLET")
        except Wallet.DoesNotExist:
            wallet = Wallet.objects.create(wallet_type="RETAIL_RTP_WALLET")
        return wallet.balance

    @classmethod
    def get_non_retail_wallet_balance(cls):
        try:
            wallet = Wallet.objects.get(wallet_type="NON_RETAIL_WALLET")
        except Wallet.DoesNotExist:
            wallet = Wallet.objects.create(wallet_type="NON_RETAIL_WALLET")
        return wallet.balance

    @classmethod
    def fund_wallet(cls, wallet_type, amount, game_type=None, is_reversal=False):
        if amount <= 0:
            return None
        
        if wallet_type == "RETAIL_COMMISSION_WALLET":
            wallet_type = "COMMISSION"
        
        try:
            wallet = Wallet.objects.get(wallet_type=wallet_type)
        except Wallet.DoesNotExist:
            wallet = Wallet.objects.create(wallet_type=wallet_type)

        amount = round(amount, 2)

        balance_before = wallet.balance
        balance_after = balance_before + amount

        wallet.previous_balance = round(balance_before, 2)
        wallet.balance = round(balance_after, 2)
        wallet.save()

        # create wallet transaction
        WalletTransaction.objects.create(
            wallet=wallet,
            wallet_type=wallet_type,
            amount=amount,
            previous_balance=balance_before,
            current_balance=balance_after,
            transaction_type="CREDIT",
            game_type=game_type,
            is_reversal=is_reversal,
        )
        return wallet

    @classmethod
    def debit_wallet(cls, wallet_type, amount, game_type=None, user_phone=None, user_name=None, game_play_id=None):
        return
        if amount <= 0:
            return None
        try:
            wallet = Wallet.objects.get(wallet_type=wallet_type)
        except Wallet.DoesNotExist:
            return None

        amount = round(amount, 2)

        balance_before = wallet.balance
        balance_after = balance_before - amount

        wallet.previous_balance = balance_before
        wallet.balance = balance_after
        wallet.save()

        # create wallet transaction
        WalletTransaction.objects.create(
            wallet=wallet,
            wallet_type=wallet_type,
            amount=amount,
            previous_balance=balance_before,
            current_balance=balance_after,
            transaction_type="DEBIT",
            game_type=game_type,
            user_phone=user_phone,
            user_name=user_name,
            game_play_id=game_play_id,
        )
        return wallet

    @classmethod
    def wallet_to_wallet_transfer(cls, amount, from_wallet_type, to_wallet_type):
       
        from pos_app.models import AgencyBankingToken

        if amount <= 0:
            return False, "Amount must be greater than 0"
        
        if from_wallet_type == to_wallet_type:
            return False, "Wallet types must be different"
        
        if from_wallet_type == "RTO_WALLET":
            if to_wallet_type == "RETAIL_RTP_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "*************"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM RTO WALLET TO RETAIL RTP WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_RTO_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RTO_WALLET")
                
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
                        redis_db.delete(f"rto_wallet_login")
                        
                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type=from_wallet_type,
                            to_wallet_type=to_wallet_type,
                        )
                    
                    return True, response.json()
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"
            elif to_wallet_type == "AGENT_FUNDING_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "2348077469471"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM RTO WALLET TO RETAIL FUNDING WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_RTO_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RTO_WALLET")
                
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                
                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
                        redis_db.delete(f"rto_wallet_login")
                        
                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type=from_wallet_type,
                            to_wallet_type=to_wallet_type,
                        )
                    
                    
                    return True, response.json()
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

        elif from_wallet_type == "COMMISSION_WALLET":
            if to_wallet_type == "AGENT_FUNDING_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "2348077469471"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "COMMISSION WALLET TO RETAIL FUNDING WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }


                payload["transaction_pin"] = config("AGENCY_BANKING_RETAIL_COMMISSION_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RETAIL_COMMISSION_WALLET")
                
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
                        redis_db.delete(f"agency_banking_retail_commission_wallet_login")
                        
                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="AGENT_FUNDING_WALLET",
                            to_wallet_type="RETAIL_RTP_WALLET",
                        )

                    return True, response.json()
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

        elif from_wallet_type == "GHANA_RTP_WALLET":
            if to_wallet_type == "RTO_WALLET":

                phone = "*************"

                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM GHANA RTP WALLET TO RTO WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }



                payload["transaction_pin"] = config("AGENCY_BANKING_GHANA_LOTTO_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("GHANA_RTP_WALLET")
                
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
                        redis_db.delete(f"ghana_lotto_wallet_login")
                        
                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="GHANA_RTP_WALLET",
                            to_wallet_type="RTO_WALLET",
                        )
                    
                    return True, response.json()
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"



        return False, "This wallet feature has not beem implemented"
    
    @classmethod
    def get_user_id(cls, wallet_type):
        try:
            wallet = Wallet.objects.get(wallet_type=wallet_type)
            return wallet.user_id
        except Wallet.DoesNotExist:
            return None
        
        
class WalletTransaction(models.Model):
    TRANSACTION_TYPE = [
        ("CREDIT", "CREDIT"),
        ("DEBIT", "DEBIT"),
    ]

    WALLET_TYPE = [
        ("AGENT_FUNDING_WALLET", "AGENT_FUNDING_WALLET"),
        ("NON_RETAIL_WALLET", "NON_RETAIL_WALLET"),
        ("RETAIL_RTP_WALLET", "RETAIL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "GHANA_RTP_WALLET"),
        ("MOVEABLE_GHANA_RTP_WALLET", "MOVEABLE_GHANA_RTP_WALLET"),
        ("GHANA_RTO_WALLET", "GHANA_RTO_WALLET"),
        ("RTO_WALLET", "RTO_WALLET"),
        ("COMMISSION", "COMMISSION"),
    ]

    GAME_TYPE = [
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("QUIKA", "QUIKA"),
        ("BANKER", "BANKER"),
    ]

    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE)
    wallet_type = models.CharField(max_length=50, choices=WALLET_TYPE)
    wallet_phone_number = models.CharField(max_length=20, null=True, blank=True)
    user_phone = models.CharField(max_length=20, null=True, blank=True)
    user_name = models.CharField(max_length=50, null=True, blank=True)
    game_play_id = models.CharField(max_length=100, null=True, blank=True)
    amount = models.FloatField(default=0.0)
    previous_balance = models.FloatField(default=0.0)
    current_balance = models.FloatField(default=0.0)
    transaction_type = models.CharField(max_length=50, choices=TRANSACTION_TYPE)
    game_type = models.CharField(max_length=50, choices=GAME_TYPE, null=True, blank=True)
    is_reversal = models.BooleanField(default=False)
    settled = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "WALET TRANSACTION"
        verbose_name_plural = "WALLET TRANSACTIONS"
