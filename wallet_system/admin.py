from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

# from wallet_app.models import WalletTransaction
from wallet_system.models import Wallet, WalletTransaction


# Register your models here.
class WalletResource(resources.ModelResource):
    class Meta:
        model = Wallet


class WalletTransactionResource(resources.ModelResource):
    class Meta:
        model = WalletTransaction


class WalletResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletResource
    # raw_id_fields = ("user_profile", "agent_profile")
    # autocomplete_fields = ["batch", "user_profile"]

    search_fields = ["wallet_type", "wallet_phone_number"]

    list_filter = ["created_at", "wallet_type"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletTransactionResource
    raw_id_fields = [
        "wallet",
    ]
    autocomplete_fields = ["wallet"]

    search_fields = ["wallet__wallet_type", "wallet__wallet_phone_number"]

    list_filter = ["created_at", "settled", "is_reversal", "wallet_type", "transaction_type", "game_type"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(Wallet, WalletResourceAdmin)
admin.site.register(WalletTransaction, WalletTransactionResourceAdmin)
