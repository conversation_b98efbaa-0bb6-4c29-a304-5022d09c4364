from django.contrib import admin, messages
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from django.conf import settings
import pytz
from datetime import datetime
from django.db.models import Sum

from pos_app.models import Agent, AgentWalletTransaction, LottoAgentRemittanceTable
from retail_metrics.models import AgentMetrics, GameAnalytics, PayoutAnalytics, RemittanceAnalytics, RetrieveTerminalsData, ReturnToPlayerAndReturnToOwnerAnalytics, WalletFundingAnalytics


# Register your models here.
class AgentMetricsResource(resources.ModelResource):
    class Meta:
        model = AgentMetrics

class RetrieveTerminalsDataResource(resources.ModelResource):
    class Meta:
        model = RetrieveTerminalsData



class GameAnalyticsResource(resources.ModelResource):
    class Meta:
        model = GameAnalytics


class ReturnToPlayerAndReturnToOwnerAnalyticsResource(resources.ModelResource):
    class Meta:
        model = ReturnToPlayerAndReturnToOwnerAnalytics



class WalletFundingAnalyticsResource(resources.ModelResource):
    class Meta:
        model = WalletFundingAnalytics


class PayoutAnalyticsResource(resources.ModelResource):
    class Meta:
        model = PayoutAnalytics


class RemittanceAnalyticsResource(resources.ModelResource):
    class Meta:
        model = RemittanceAnalytics



class AgentMetricsResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentMetricsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at",]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    actions = [
        "updaterecord"
    ]

    @admin.action(description="UPDATE RECORDS")
    def updaterecord(modeladmin, request, queryset):
        for obj in queryset:
            agents = Agent.objects.all()
            obj.total_agents = len(agents)
            obj.total_merchants = len(agents.filter(agent_type = "MERCHANT"))
            obj.total_lotto_agents = len(agents.filter(agent_type = "LOTTO_AGENT"))
            obj.total_personal_accounts = len(agents.filter(agent_type = "PERSONAL"))
            obj.total_liberty_retail = len(agents.filter(agent_type = "LIBERTY_RETAIL"))
            obj.save()

            break
        
        messages.success(request, "UPDATED SUCCESSFULLY")


class RetrieveTerminalsDataResourceAdmin(ImportExportModelAdmin):
    resource_class = RetrieveTerminalsDataResource

    date_hierarchy = "created_at"

    list_filter = ["created_at",]

    search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    actions = [
        "updaterecord"
    ]

    @admin.action(description="UPDATE RECORDS")
    def updaterecord(modeladmin, request, queryset):
        for obj in queryset:
            agents = Agent.objects.filter(terminal_id__isnull = False, terminal_retrieved = True)
            for agent in agents:
                try:
                    RetrieveTerminalsData.objects.create(
                        phone_number = agent.phone,
                        first_name = agent.first_name,
                        last_name = agent.last_name,
                        full_name = agent.full_name,
                        terminal_id = agent.terminal_id
                    )
                except:
                    pass

            break
        
        messages.success(request, "UPDATED SUCCESSFULLY")


class GameAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = GameAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at","year", "month", "game_type"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    actions = [
        "updatethismonthrecord"
    ]

    @admin.action(description="UPDATE THIS MONTH RECORDS")
    def updatethismonthrecord(modeladmin, request, queryset):
        for obj in queryset:
            TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            this_month = TODAY.month
            this_year = TODAY.year

            query_set_for_winnings = AgentWalletTransaction.objects.filter(date_created__year = this_year, transaction_from = "WINNINGS", game_type = obj.game_type).filter(ate_created__month = this_month)
            query_set_for_game_play = AgentWalletTransaction.objects.filter(date_created__year = this_year, transaction_from = "GAME_PLAY", game_type = obj.game_type).filter(ate_created__month = this_month)

            this_month_winnings = query_set_for_winnings.aggregate(Sum("amount")).get("amount__sum")
            if this_month_winnings is None:
                this_month_winnings = 0


            this_month_sales = query_set_for_game_play.aggregate(Sum("amount")).get("amount__sum")
            if this_month_sales is None:
                this_month_sales = 0

            
            GameAnalytics.update_record(
                game_type = obj.game_type,
                sales_amount = this_month_sales,
                winning_amount = this_month_winnings,
                year = this_year,
                month = this_month
            )

            




        
        messages.success(request, "UPDATED SUCCESSFULLY")





class ReturnToPlayerAndReturnToOwnerAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = ReturnToPlayerAndReturnToOwnerAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at","year", "month", "game_type"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class WalletFundingAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletFundingAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at","year", "month"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class PayoutAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = PayoutAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at","year", "month"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class RemittanceAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = RemittanceAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at","year", "month"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    ctions = [
        "updatethismonthrecord"
    ]

    @admin.action(description="UPDATE THIS MONTH RECORDS")
    def updatethismonthrecord(modeladmin, request, queryset):
        for obj in queryset:
            TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            this_month = TODAY.month
            this_year = TODAY.year

            queryset = LottoAgentRemittanceTable.objects.filter(created_at__year = this_year).filter(reated_at__month = this_month)
            amount_remitted = queryset.filter(remitted = True).aggregate(Sum("amount_paid")).get("amount_paid__sum")
            if amount_remitted is None:
                amount_remitted = 0


            amount_due = queryset.filter(remitted = False, due = True).aggregate(Sum("amount")).get("amount__sum")
            if amount_due is None:
                amount_due = 0
            

            obj.amount_remitted = amount_remitted
            obj.amount_due = amount_due
            obj.save()

        

            break


    


admin.site.register(AgentMetrics, AgentMetricsResourceAdmin)
admin.site.register(RetrieveTerminalsData, RetrieveTerminalsDataResourceAdmin)
admin.site.register(GameAnalytics, GameAnalyticsResourceAdmin)
admin.site.register(ReturnToPlayerAndReturnToOwnerAnalytics, ReturnToPlayerAndReturnToOwnerAnalyticsResourceAdmin)
admin.site.register(WalletFundingAnalytics, WalletFundingAnalyticsResourceAdmin)
admin.site.register(PayoutAnalytics, PayoutAnalyticsResourceAdmin)
admin.site.register(RemittanceAnalytics, RemittanceAnalyticsResourceAdmin)