from django.db import models
from django.conf import settings
import pytz
from datetime import datetime

# Create your models here.
class AgentMetrics(models.Model):
    total_agents = models.IntegerField(default=0)
    total_merchants = models.IntegerField(default=0)
    total_lotto_agents = models.IntegerField(default=0)
    total_personal_accounts = models.IntegerField(default=0)
    total_liberty_retail = models.IntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AGENT COUNT METRICS TABLE"
        verbose_name_plural = "AGENT COUNT METRICS TABLE"

    



class RetrieveTerminalsData(models.Model):
    phone_number = models.CharField(max_length=300, unique=True)
    first_name = models.CharField(max_length=300)
    last_name = models.Char<PERSON>ield(max_length=300)
    full_name = models.Char<PERSON>ield(max_length=300, blank=True, null=True)
    terminal_id = models.Char<PERSON><PERSON>(max_length=300, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RETRIEVED TERMINAL DATA"
        verbose_name_plural = "RETRIEVED TERMINAL DATAS"



class GameAnalytics(models.Model):

    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("QUIKA", "QUIKA"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("BANKER", "BANKER"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW")
    ]


    game_type = models.CharField(max_length=300, choices=LOTTERY_TYPE_CHOICES)
    sales = models.FloatField(default=0)
    winnings = models.FloatField(default=0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GAME ANALYTICS"
        verbose_name_plural = "GAME ANALYTICS"

    
    @classmethod
    def add_or_create_record(cls, game_type, sales_amount = 0, winning_amount = 0):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        this_month = TODAY.month
        this_year = TODAY.year

        try:
            instance = cls.objects.get(
                year = this_year,
                month = this_month,
                game_type = game_type
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year = this_year,
                month = this_month,
                game_type=game_type
            )

        instance.sales += sales_amount
        instance.winnings += winning_amount
        instance.save()
    

    @classmethod
    def update_record(cls, game_type, sales_amount, winning_amount, year, month):

        try:
            instance = cls.objects.get(
                year = year,
                month = month,
                game_type = game_type
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year = year,
                month = month,
                game_type=game_type
            )

        instance.winnings = winning_amount
        instance.sales = sales_amount
        instance.save()

        return instance


        


class ReturnToPlayerAndReturnToOwnerAnalytics(models.Model):

    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("QUIKA", "QUIKA"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("BANKER", "BANKER"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW")
    ]


    game_type = models.CharField(max_length=300, choices=LOTTERY_TYPE_CHOICES)
    rtp = models.IntegerField(default=0)
    rto = models.IntegerField(default=0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RETURN TO PLAYER & RETURN TO OWNER ANALYTICS"
        verbose_name_plural = "RETURN TO PLAYER & RETURN TO OWNER ANALYTICS"

    

    @classmethod
    def add_or_create_record(cls, game_type, rtp = 0, rto = 0):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        this_month = TODAY.month
        this_year = TODAY.year

        try:
            instance = cls.objects.get(
                year = this_year,
                month = this_month,
                game_type = game_type
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year = this_year,
                month = this_month,
                game_type=game_type
            )

        instance.rtp += rtp
        instance.rto += rto
        instance.save()
    

    @classmethod
    def update_record(cls, game_type, rtp, rto, year, month):

        try:
            instance = cls.objects.get(
                year = year,
                month = month,
                game_type = game_type
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year = year,
                month = month,
                game_type=game_type
            )

        instance.rto = rto
        instance.rtp = rtp
        instance.save()

        return instance




class WalletFundingAnalytics(models.Model):
    amount = models.FloatField(default=0.0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "WALLET FUNDING ANALYTICS"
        verbose_name_plural = "WALLET FUNDING ANALYTICS"
    


    @classmethod
    def add_or_create_record(cls, amount = 0):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        this_month = TODAY.month
        this_year = TODAY.year

        try:
            instance = cls.objects.get(
                year = this_year,
                month = this_month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year = this_year,
                month = this_month,
            )

        instance.amount += amount
        instance.save()
    

    @classmethod
    def update_record(cls, amount, year, month):

        try:
            instance = cls.objects.get(
                year = year,
                month = month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year = year,
                month = month,
            )

        instance.amount = amount
        instance.save()

        return instance


class PayoutAnalytics(models.Model):
    amount = models.FloatField(default=0.0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PAYOUT ANALYTICS"
        verbose_name_plural = "PAYOUT ANALYTICS"

    

    @classmethod
    def add_or_create_record(cls, amount = 0):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        this_month = TODAY.month
        this_year = TODAY.year

        try:
            instance = cls.objects.get(
                year = this_year,
                month = this_month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year = this_year,
                month = this_month,
            )

        instance.amount += amount
        instance.save()
    

    @classmethod
    def update_record(cls, amount, year, month):

        try:
            instance = cls.objects.get(
                year = year,
                month = month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year = year,
                month = month,
            )

        instance.amount = amount
        instance.save()

        return instance



class RemittanceAnalytics(models.Model):
    amount_remitted = models.FloatField(default=0.0)
    amount_due = models.FloatField(default=0.0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "REMITTANCE ANALYTICS"
        verbose_name_plural = "REMITTANCE ANALYTICS"

    
    @classmethod
    def update_record(cls, month, year, amount_remitted, amount_due):

        try:
            instance = cls.objects.get(
                year = year,
                month = month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year = year,
                month = month,
            )

        instance.amount_remitted = amount_remitted
        instance.amount_due = amount_due
        instance.save()

        return instance



